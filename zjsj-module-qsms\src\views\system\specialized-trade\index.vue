<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="人员姓名" prop="personName">
        <el-input
          v-model="queryParams.personName"
          placeholder="请输入人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属项目" prop="project">
        <el-input
          v-model="queryParams.project"
          placeholder="请输入所属项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工种" prop="workType">
        <el-input
          v-model="queryParams.workType"
          placeholder="请输入工种"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="在岗状态" prop="workStatus">
        <el-select
          v-model="queryParams.workStatus"
          placeholder="请选择在岗状态"
          clearable
        >
          <el-option label="在岗" value="在岗" />
          <el-option label="离岗" value="离岗" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:xmxx:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:xmxx:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:xmxx:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="人员编号" align="center" prop="personCode" />
      <el-table-column label="人员姓名" align="center" prop="personName" />
      <el-table-column label="身份证编号" align="center" prop="idCard" />
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="所属项目" align="center" prop="project" />
      <el-table-column label="工种" align="center" prop="workType" />
      <el-table-column label="证书名称" align="center" prop="certificateName" />
      <el-table-column label="证书编号" align="center" prop="certificateCode" />
      <el-table-column
        label="发证机关"
        align="center"
        prop="issuingAuthority"
      />
      <el-table-column label="发证日期" align="center" prop="issueDate" />
      <el-table-column
        label="证书有效期"
        align="center"
        prop="validityPeriod"
      />
      <el-table-column label="在岗状态" align="center" prop="workStatus" />

      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-hasPermi="['inspection:xmxx:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['inspection:zjProjectInfo:detail']"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            >查看</el-button
          >
          <el-button
            v-hasPermi="['inspection:xmxx:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改人员信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form
        ref="form"
        :model="form"
        :rules="mode === 'edit' ? rules : {}"
        label-width="140px"
        class="three-column-form"
      >
        <!-- <el-form-item v-if="form.id" label="人员id" prop="id">
          <el-input v-model="form.id" placeholder="请输入人员id" readonly />
        </el-form-item> -->
        <el-form-item label="人员编号：" prop="personCode">
          <el-input
            v-model="form.personCode"
            placeholder="请输入人员编号"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="人员姓名：" prop="personName">
          <el-input
            v-model="form.personName"
            placeholder="请输入人员姓名"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="身份证编号：" prop="idCard">
          <el-input
            v-model="form.idCard"
            placeholder="请输入身份证编号"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="公司名称：" prop="companyName">
          <el-input
            v-model="form.companyName"
            placeholder="请输入公司名称"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="所属项目：" prop="project">
          <el-input
            v-model="form.project"
            placeholder="请输入所属项目"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="工种：" prop="workType">
          <el-input
            v-model="form.workType"
            placeholder="请输入工种"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="证书名称：" prop="certificateName">
          <el-input
            v-model="form.certificateName"
            placeholder="请输入证书名称"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="证书编号：" prop="certificateCode">
          <el-input
            v-model="form.certificateCode"
            placeholder="请输入证书编号"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="发证机关：" prop="issuingAuthority">
          <el-input
            v-model="form.issuingAuthority"
            placeholder="请输入发证机关"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="发证日期：" prop="issueDate">
          <el-date-picker
            v-model="form.issueDate"
            type="date"
            placeholder="请选择发证日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="证书有效期：" prop="validityPeriod">
          <el-date-picker
            v-model="form.validityPeriod"
            type="date"
            placeholder="请选择证书有效期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            :disabled="mode === 'view'"
          />
        </el-form-item>
        <el-form-item label="在岗状态：" prop="workStatus">
          <el-select
            v-model="form.workStatus"
            placeholder="请选择在岗状态"
            :disabled="mode === 'view'"
          >
            <el-option label="在岗" value="在岗" />
            <el-option label="离岗" value="离岗" />
          </el-select>
        </el-form-item>
        <el-form-item label="附件上传：" prop="attachment">
          <el-upload
            v-if="mode === 'edit'"
            class="upload-demo"
            action="#"
            
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            :file-list="fileList"
            multiple
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </el-upload>
          <span v-else>{{ Array.isArray(form.attachment) ? form.attachment.join(',') : form.attachment ? form.attachment : "-" }}</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" v-if="mode === 'edit'">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSpecializedTrade,
  getSpecializedTrade,
  delSpecializedTrade,
  addSpecializedTrade,
  updateSpecializedTrade,
} from "@/api/system/specialized-trade";

export default {
  name: "SpecializedTrade",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 国外在建项目信息表格数据
      dataList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 表单模式：edit-编辑模式，view-查看模式
      mode: "edit",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        personCode: null,
        personName: null,
        idCard: null,
        companyName: null,
        project: null,
        workType: null,
        certificateName: null,
        certificateCode: null,
        issuingAuthority: null,
        issueDate: null,
        validityPeriod: null,
        workStatus: null,
        attachment: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 文件列表
      fileList: [],
      // 表单校验
      rules: {
        personCode: [
          { required: true, message: "人员编号不能为空", trigger: "change" },
        ],
        personName: [
          { required: true, message: "人员姓名不能为空", trigger: "change" },
        ],
        idCard: [
          { required: true, message: "身份证编号不能为空", trigger: "change" },
          {
            pattern:
              /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            message: "请输入正确的身份证号码",
            trigger: "change",
          },
        ],
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "change" },
        ],
        project: [
          { required: true, message: "所属项目不能为空", trigger: "change" },
        ],
        workType: [
          { required: true, message: "工种不能为空", trigger: "change" },
        ],
        certificateName: [
          { required: true, message: "证书名称不能为空", trigger: "change" },
        ],
        certificateCode: [
          { required: true, message: "证书编号不能为空", trigger: "change" },
        ],
        issuingAuthority: [
          { required: true, message: "发证机关不能为空", trigger: "change" },
        ],
        issueDate: [
          { required: true, message: "发证日期不能为空", trigger: "change" },
        ],
        validityPeriod: [
          { required: true, message: "证书有效期不能为空", trigger: "change" },
        ],
        workStatus: [
          { required: true, message: "在岗状态不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询国外在建项目信息列表 */
    getList() {
      this.loading = true;
      listSpecializedTrade(this.queryParams).then((response) => {
        this.dataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        personCode: null,
        personName: null,
        idCard: null,
        companyName: null,
        project: null,
        workType: null,
        certificateName: null,
        certificateCode: null,
        issuingAuthority: null,
        issueDate: null,
        validityPeriod: null,
        workStatus: null,
        attachment: null,
        createTime: null,
      };
      this.mode = "edit";
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.mode = "edit";
      this.open = true;
      this.title = "添加人员信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || (this.ids.length > 0 ? this.ids[0] : null);
      if (!id) {
        this.$modal.msgError("请选择要修改的数据");
        return;
      }
      getSpecializedTrade(id).then((response) => {
        this.form = response.data;
        this.mode = "edit";
        this.open = true;
        this.title = "修改人员信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log(this.form.id);

          if (this.form.id != null) {
            updateSpecializedTrade(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSpecializedTrade(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id ? [row.id] : this.ids;
      if (!ids || (Array.isArray(ids) && ids.length === 0)) {
        this.$modal.msgError("请选择要删除的数据");
        return;
      }

      this.$modal
        .confirm('是否确认删除' + ids.length + '条数据项？')
        .then(function () {
          return delSpecializedTrade(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/specializedTrade/export",
        {
          ...this.queryParams,
        },
        `specializedTrade_${new Date().getTime()}.xlsx`
      );
    },
    handleDetail(row) {
      this.reset();
      // 清除校验

      const id = row.id || this.ids;
      getSpecializedTrade(id).then((res) => {
        this.form = res.data;
        this.mode = "view";
        this.title = "查看人员详情";
        this.open = true;
        this.$refs["form"].clearValidate();
      });
    },

    /** 金额输入验证 - 只允许数字和小数点 */
    validateAmountInput(field, value) {
      const numericValue = value.replace(/[^0-9.]/g, "");
      // 确保只有一个小数点
      const parts = numericValue.split(".");
      if (parts.length > 2) {
        const validValue = parts[0] + "." + parts.slice(1).join("");
        this.form[field] = validValue;
      } else {
        this.form[field] = numericValue;
      }
    },

    /** 纯数字输入验证 - 只允许正整数 */
    validateNumberInput(field, value) {
      const numericValue = value.replace(/[^0-9]/g, "");
      this.form[field] = numericValue;
    },

    /** 百分比输入验证 - 允许数字和小数点，限制0-100 */
    validatePercentageInput(field, value) {
      let numericValue = value.replace(/[^0-9.]/g, "");
      // 确保只有一个小数点
      const parts = numericValue.split(".");
      if (parts.length > 2) {
        numericValue = parts[0] + "." + parts.slice(1).join("");
      }
      // 限制范围0-100
      const num = parseFloat(numericValue);
      if (!isNaN(num) && num > 100) {
        numericValue = "100";
      }
      this.form[field] = numericValue;
    },

    /** 金额字段验证规则 */
    validateAmount(rule, value, callback) {
      if (value && !/^[0-9]+(\.[0-9]+)?$/.test(value)) {
        callback(new Error("请输入有效的金额，只能包含数字和小数点"));
      } else {
        callback();
      }
    },

    /** 正整数验证规则 */
    validatePositiveInteger(rule, value, callback) {
      if (value && !/^[0-9]+$/.test(value)) {
        callback(new Error("请输入有效的数字"));
      } else {
        callback();
      }
    },

    /** 百分比验证规则 */
    validatePercentage(rule, value, callback) {
      if (value) {
        if (!/^[0-9]+(\.[0-9]+)?$/.test(value)) {
          callback(new Error("请输入有效的百分比，只能包含数字和小数点"));
        } else {
          const num = parseFloat(value);
          if (num < 0 || num > 100) {
            callback(new Error("百分比必须在0-100之间"));
          } else {
            callback();
          }
        }
      } else {
        callback();
      }
    },

    /** 文件上传相关方法 */
    handlePreview(file) {
      console.log(file);
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    beforeUpload(file) {
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 0.5;

      if (!isJPG) {
        this.$message.error("上传头像图片只能是 JPG/PNG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传头像图片大小不能超过 500KB!");
      }
      return isJPG && isLt2M;
    },
  },
};
</script>

<style scoped>
.three-column-form {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  padding: 10px;
}

.three-column-form .el-form-item {
  margin-bottom: 18px;
}
</style>
