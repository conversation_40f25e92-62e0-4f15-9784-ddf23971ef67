{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue", "mtime": 1757425884328}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_hazard", "require", "_selectHazardCategoryTree", "_interopRequireDefault", "name", "components", "selectHazardCategoryTree", "data", "treeLoading", "loading", "ids", "single", "multiple", "showSearch", "total", "hazardList", "title", "open", "queryParams", "pageNum", "pageSize", "hazardName", "hazardId", "parentId", "hazardCode", "orderNum", "target", "examid", "categoryType", "investigationContent", "hazardLevel", "hazardStatus", "rectificationDeadline", "rectificationRequirements", "hazardIdentification", "form", "rules", "required", "message", "trigger", "treeData", "defaultProps", "children", "label", "<PERSON><PERSON><PERSON><PERSON>", "selectId", "created", "getHazardHazardCategory", "getList", "methods", "handleHazardCategoryChange", "selectedItem", "console", "log", "hazardCategory", "value", "id", "setHazardCategoryEcho", "_this", "trySetEcho", "retryCount", "arguments", "length", "undefined", "concat", "$nextTick", "component", "$refs", "hazardCategorySelect", "displayValue", "setTimeout", "setTreeSelection", "warn", "expandToNode", "node", "parent", "isRoot", "tree", "setExpandedKey", "handleSelectVisibleChange", "visible", "_this2", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentNode", "getCurrentNode", "handleCommand", "command", "_this3", "$message", "type", "status", "promises", "map", "item", "updateHazard", "Promise", "allSettled", "then", "res", "successCount", "filter", "failedResults", "handleQuery", "errorMessages", "result", "index", "serialNumber", "errorMsg", "join", "dangerouslyUseHTMLString", "goToHazardCategory", "$router", "push", "path", "_this4", "code", "_this5", "listHazard", "rows", "cancel", "reset", "createBy", "createTime", "updateBy", "updateTime", "remark", "<PERSON><PERSON><PERSON><PERSON>", "resetForm", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "_this6", "indexOf", "handleAdd", "handleUpdate", "row", "_this7", "<PERSON><PERSON><PERSON><PERSON>", "submitForm", "_this8", "validate", "valid", "$modal", "msgSuccess", "add<PERSON><PERSON><PERSON>", "handleDelete", "_this9", "hazardIds", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "Date", "getTime"], "sources": ["src/views/inspection/hazard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- 左侧树结构 -->\r\n      <!-- <el-col :span=\"3\">\r\n        <div class=\"title mb-2\" @click=\"goToHazardCategory\">隐患类别管理</div>\r\n\r\n        <el-tree\r\n                  ref=\"tree\"\r\n          v-loading=\"treeLoading\"\r\n          :data=\"treeData\"\r\n          :props=\"defaultProps\"\r\n          :load=\"loadNode\"\r\n          lazy\r\n          @node-click=\"handleNodeClick\"\r\n        >\r\n          <template #default=\"{ node, data }\">\r\n            <el-tooltip effect=\"dark\" :content=\"data.label\" placement=\"top\">\r\n              <span\r\n                :ref=\"(el) => setLabelRef(el, node)\"\r\n                class=\"el-tree-node__label\"\r\n              >\r\n                {{ node.label }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-tree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 10px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <!-- 排查内容 -->\r\n          <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n            <el-input\r\n              v-model=\"queryParams.investigationContent\"\r\n              placeholder=\"请输入排查内容\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n            <el-input\r\n              v-model=\"queryParams.hazardCategory\"\r\n              placeholder=\"请输入隐患类别\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-dropdown @command=\"handleCommand\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                type=\"success\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                >修改状态<i class=\"el-icon-arrow-down el-icon--right\"\r\n              /></el-button>\r\n              <template #dropdown>\r\n                <el-dropdown-menu style=\"width: 100px; text-align: center\">\r\n                  <!-- 下拉选项，可根据实际需求修改 -->\r\n                  <el-dropdown-item command=\"enable\">启用</el-dropdown-item>\r\n                  <el-dropdown-item command=\"disable\">禁用</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"hazardList\"\r\n          height=\"calc(100vh - 250px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"隐患明细 id\" align=\"center\" prop=\"hazardId\" /> -->\r\n          <!-- <el-table-column label=\"隐患名称\" align=\"center\" prop=\"hazardName\" /> -->\r\n          <!-- <el-table-column label=\"父菜单ID\" align=\"center\" prop=\"parentId\" /> -->\r\n          <!-- <el-table-column label=\"隐患编号\" align=\"center\" prop=\"hazardCode\" /> -->\r\n          <!-- <el-table-column label=\"显示顺序\" align=\"center\" prop=\"orderNum\" /> -->\r\n          <!-- <el-table-column label=\" 打开方式\" align=\"center\" prop=\"target\" /> -->\r\n          <!-- <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" /> -->\r\n          <!-- <el-table-column label=\"检查类型\" align=\"center\" prop=\"examid\" /> -->\r\n          <!-- <el-table-column\r\n        label=\"大类类型：1：类型  2：行为\"\r\n        align=\"center\"\r\n        prop=\"categoryType\"\r\n      /> -->\r\n          <!-- 序号 -->\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                scope.$index +\r\n                1\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- 隐患类别 -->\r\n          <el-table-column\r\n            label=\"隐患类别\"\r\n            align=\"center\"\r\n            prop=\"hazardCategory\"\r\n            width=\"150\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"排查内容\"\r\n            align=\"center\"\r\n            prop=\"investigationContent\"\r\n            width=\"250\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column label=\"隐患级别\" align=\"center\" prop=\"hazardLevel\" /> -->\r\n          <el-table-column\r\n            label=\"整改要求\"\r\n            align=\"center\"\r\n            prop=\"rectificationRequirements\"\r\n            width=\"300\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"整改时限(天)\"\r\n            align=\"center\"\r\n            prop=\"rectificationDeadline\"\r\n            width=\"100\"\r\n          />\r\n          <!-- 事故隐患 -->\r\n          <el-table-column\r\n            label=\"事故隐患\"\r\n            align=\"center\"\r\n            prop=\"accidentHazard\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                v-if=\"scope.row.accidentHazard === '1'\"\r\n                class=\"hazard-tag hazard-general\"\r\n                size=\"small\"\r\n              >\r\n                一般\r\n              </el-tag>\r\n              <el-tag\r\n                v-else-if=\"scope.row.accidentHazard === '2'\"\r\n                class=\"hazard-tag hazard-common\"\r\n                size=\"small\"\r\n              >\r\n                常见\r\n              </el-tag>\r\n              <span v-else>{{ scope.row.accidentHazard }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"隐患标识\"\r\n            align=\"center\"\r\n            prop=\"hazardIdentification\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"hazardStatus\" />\r\n\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"150\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改隐患问题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <!-- 隐患类别 -->\r\n        <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n          <!-- <el-select\r\n            ref=\"selectRef\"\r\n            v-model=\"form.hazardCategory\"\r\n            placeholder=\"请选择隐患类别\"\r\n            style=\"width: 100%\"\r\n            clearable\r\n            :popper-append-to-body=\"false\"\r\n            @visible-change=\"handleSelectVisibleChange\"\r\n          >\r\n            <el-option\r\n              :value=\"form.hazardCategory\"\r\n              style=\"height: auto; padding: 0; border: none\"\r\n            >\r\n              <el-tree\r\n                :node-key=\"'id'\"\r\n                v-loading=\"treeLoading\"\r\n                :data=\"treeData\"\r\n                :props=\"defaultProps\"\r\n                :load=\"loadNode\"\r\n                lazy\r\n                @node-click=\"handleNodeClick\"\r\n                :expand-on-click-node=\"false\"\r\n                :highlight-current=\"true\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">{{ node.label }}</span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </el-option>\r\n          </el-select> -->\r\n          <selectHazardCategoryTree\r\n            ref=\"hazardCategorySelect\"\r\n            v-model=\"form.hazardCategory\"\r\n            :categoryList=\"treeData\"\r\n            placeholder=\"请选择隐患类别\"\r\n            @change=\"handleHazardCategoryChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患级别\" prop=\"hazardLevel\">\r\n          <el-select\r\n            v-model=\"form.hazardLevel\"\r\n            placeholder=\"请选择隐患级别\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一级\" value=\"1\" />\r\n            <el-option label=\"二级\" value=\"2\" />\r\n            <el-option label=\"三级\" value=\"3\" />\r\n            <el-option label=\"四级\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <!-- 事故隐患 -->\r\n        <el-form-item label=\"事故隐患\" prop=\"accidentHazard\">\r\n          <!-- 一般 常见 -->\r\n          <el-select\r\n            v-model=\"form.accidentHazard\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一般\" value=\"1\" />\r\n            <el-option label=\"常见\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患标识\" prop=\"hazardIdentification\">\r\n          <el-input\r\n            v-model=\"form.hazardIdentification\"\r\n            placeholder=\"请输入隐患标识\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n          <el-input\r\n            v-model=\"form.rectificationDeadline\"\r\n            type=\"Number\"\r\n            placeholder=\"请输入整改时限(天)\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"hazardStatus\">\r\n          <el-select\r\n            v-model=\"form.hazardStatus\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"启用\" value=\"启用\" />\r\n            <el-option label=\"禁用\" value=\"禁用\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n          <el-input\r\n            v-model=\"form.investigationContent\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n          <el-input\r\n            v-model=\"form.rectificationRequirements\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listHazard,\r\n  getHazard,\r\n  delHazard,\r\n  addHazard,\r\n  updateHazard,\r\n  treeHazard,\r\n  treeHazardFirst,\r\n  getHazardHazardCategory,\r\n} from \"@/api/inspection/hazard\";\r\nimport selectHazardCategoryTree from \"@/views/components/selectHazardCategoryTree.vue\";\r\n\r\nexport default {\r\n  name: \"Hazard\",\r\n  components: {\r\n    selectHazardCategoryTree,\r\n  },\r\n  data() {\r\n    return {\r\n      treeLoading: true,\r\n      // 遮罩层\r\n      loading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 隐患问题库表格数据\r\n      hazardList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        hazardName: null,\r\n        hazardId: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        hazardStatus: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" },\r\n        ],\r\n        investigationContent: [\r\n          { required: true, message: \"排查内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        rectificationRequirements: [\r\n          { required: true, message: \"整改要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      treeData: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        isLeaf: \"isLeaf\",\r\n      },\r\n      selectId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getHazardHazardCategory();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    handleHazardCategoryChange(selectedItem) {\r\n      if (selectedItem) {\r\n        console.log(selectedItem, \"selectedItem\");\r\n        this.form.hazardCategory = selectedItem.label || selectedItem.value;\r\n        this.form.parentId = selectedItem.id;\r\n      } else {\r\n        this.form.hazardCategory = null;\r\n        this.form.parentId = null;\r\n      }\r\n    },\r\n\r\n    // 设置隐患类别回显\r\n    setHazardCategoryEcho() {\r\n      console.log('开始设置隐患类别回显');\r\n      console.log('form.hazardCategory:', this.form.hazardCategory);\r\n      console.log('treeData:', this.treeData);\r\n\r\n      if (!this.form.hazardCategory) {\r\n        console.log('隐患类别值为空，跳过回显');\r\n        return;\r\n      }\r\n\r\n      const trySetEcho = (retryCount = 0) => {\r\n        console.log(`尝试设置回显，第${retryCount + 1}次`);\r\n\r\n        this.$nextTick(() => {\r\n          const component = this.$refs.hazardCategorySelect;\r\n          console.log('组件引用:', component);\r\n\r\n          if (component) {\r\n            console.log('组件存在，开始设置回显');\r\n\r\n            // 设置显示值\r\n            component.displayValue = this.form.hazardCategory;\r\n            console.log('设置 displayValue:', component.displayValue);\r\n\r\n            // 检查树数据是否已加载\r\n            if (this.treeData && this.treeData.length > 0) {\r\n              console.log('树数据已加载，设置选中状态');\r\n\r\n              // 等待树数据加载完成后设置选中状态\r\n              setTimeout(() => {\r\n                if (component.setTreeSelection) {\r\n                  component.setTreeSelection();\r\n                } else {\r\n                  console.warn('setTreeSelection 方法不存在');\r\n                }\r\n              }, 200);\r\n            } else {\r\n              console.log('树数据未加载，等待数据加载');\r\n              if (retryCount < 10) {\r\n                setTimeout(() => {\r\n                  trySetEcho(retryCount + 1);\r\n                }, 300);\r\n              }\r\n            }\r\n\r\n            console.log('隐患类别回显设置完成:', this.form.hazardCategory);\r\n          } else if (retryCount < 10) {\r\n            // 如果组件还没准备好，重试最多10次\r\n            console.log('组件未准备好，等待重试');\r\n            setTimeout(() => {\r\n              trySetEcho(retryCount + 1);\r\n            }, 300);\r\n          } else {\r\n            console.warn('隐患类别回显设置失败，组件未准备好');\r\n          }\r\n        });\r\n      };\r\n\r\n      trySetEcho();\r\n    },\r\n    expandToNode(node) {\r\n      if (node && node.parent && !node.parent.isRoot) {\r\n        this.expandToNode(node.parent);\r\n        this.$refs.tree.setExpandedKey(node.parent.id, true);\r\n      }\r\n    },\r\n    handleSelectVisibleChange(visible) {\r\n      if (visible) {\r\n        this.$nextTick(() => {\r\n          if (this.form.hazardCategory) {\r\n            // 确保节点已加载\r\n            this.$refs.tree.setCurrentKey(this.form.hazardCategory, () => {\r\n              const currentNode = this.$refs.tree.getCurrentNode();\r\n              if (currentNode) {\r\n                this.expandToNode(currentNode);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCommand(command) {\r\n      if (this.ids.length <= 0) {\r\n        this.$message({\r\n          message: \"请选择隐患条目\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      // console.log(this.ids, \"this.ids\");\r\n      const status = command === \"enable\" ? \"正常\" : \"禁用\";\r\n      const promises = this.ids.map((item) => {\r\n        return updateHazard({\r\n          hazardId: item.hazardId,\r\n          hazardStatus: status,\r\n        });\r\n      });\r\n      Promise.allSettled(promises).then((res) => {\r\n        const successCount = res.filter(\r\n          (item) => item.status === \"fulfilled\"\r\n        ).length;\r\n        const failedResults = res.filter((item) => item.status === \"rejected\");\r\n        if (successCount > 0) {\r\n          this.$message({\r\n            message: `${command === \"enable\" ? \"启用\" : \"废弃\"}成功`,\r\n            type: \"success\",\r\n          });\r\n          this.handleQuery();\r\n        } else {\r\n          const errorMessages = failedResults\r\n            .map((result, index) => {\r\n              const id = this.ids[index].serialNumber;\r\n              const errorMsg = `${command === \"enable\" ? \"启用\" : \"废弃\"}失败`;\r\n              return `序号为 ${id} 的隐患条目：${errorMsg}`;\r\n            })\r\n            .join(\"\\n\");\r\n\r\n          this.$message({\r\n            message: `${errorMessages}`,\r\n            type: \"error\",\r\n            dangerouslyUseHTMLString: true,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    goToHazardCategory() {\r\n      this.$router.push({\r\n        path: \"hazardCategory\",\r\n      });\r\n    },\r\n    getHazardHazardCategory() {\r\n      this.treeLoading = true;\r\n      getHazardHazardCategory().then((res) => {\r\n        if (res.code === 200) {\r\n          // console.log(res, \"res\");\r\n          this.treeData = res.data;\r\n          this.treeLoading = false;\r\n          console.log(this.treeData, \"this.treeData\");\r\n        }\r\n      });\r\n    },\r\n    // loadNode(node, resolve) {\r\n    //   if (node.level === 0) {\r\n    //     return resolve([]);\r\n    //   }\r\n\r\n    //   const currentNode = node.data;\r\n    //   // 若该节点已有 children，说明已请求过，直接解析现有 children\r\n    //   if (currentNode.children) {\r\n    //     return resolve(currentNode.children);\r\n    //   }\r\n    //   const parentId = currentNode.id;\r\n    //   treeHazard({ parentId })\r\n    //     .then((res) => {\r\n    //       if (res.code === 200) {\r\n    //         const children = res.rows.map((item) => ({\r\n    //           id: item.hazardId,\r\n    //           label: item.hazardName,\r\n    //           // 根据子节点是否存在判断是否为叶子节点\r\n    //           isLeaf: !item.children || item.children.length === 0,\r\n    //         }));\r\n    //         // 将子节点数据赋值给当前节点的 children 属性\r\n    //         currentNode.children = children;\r\n    //         resolve(children);\r\n    //       } else {\r\n    //         resolve([]);\r\n    //       }\r\n    //     })\r\n    //     .catch(() => {\r\n    //       resolve([]);\r\n    //     });\r\n    // },\r\n    // transformChildren(children) {\r\n    //   if (!children || children.length === 0) return [];\r\n    //   return children.map((child) => ({\r\n    //     label: child.hazardName,\r\n    //     id: child.hazardId,\r\n    //     children: this.transformChildren(child.children),\r\n    //   }));\r\n    // },\r\n    // handleNodeClick(nodeData, node) {\r\n    //   console.log(node, \"nodeData\");\r\n    //   // this.queryParams.hazardId = nodeData.id;\r\n    //   if (node.isLeaf) {\r\n    //     this.selectId = nodeData.id;\r\n    //     this.form.hazardCategory = nodeData.id; // 绑定ID而非label\r\n    //     this.$refs.selectRef.blur();\r\n    //   }\r\n    // },\r\n    // setLabelRef(el, node) {\r\n    //   if (el) {\r\n    //     this.labelRefs.set(node.id || node.label, el);\r\n    //   }\r\n    // },\r\n    /** 查询隐患问题库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listHazard(this.queryParams).then((res) => {\r\n        const data = res.rows;\r\n        // data.sort((a, b) => {\r\n        //   return new Date(b.createTime) - new Date(a.createTime);\r\n        // });\r\n        this.hazardList = data;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        hazardId: null,\r\n        hazardName: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n        accidentHazard: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.parentId = this.selectId;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item, index) => {\r\n        const serialNumber =\r\n          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +\r\n          selection.indexOf(item) +\r\n          1;\r\n        return {\r\n          serialNumber,\r\n          hazardId: item.hazardId,\r\n        };\r\n      });\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加隐患问题库\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const hazardId = row.hazardId || this.ids;\r\n      getHazard(hazardId).then((res) => {\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改隐患问题库\";\r\n\r\n        console.log('获取到的表单数据:', this.form);\r\n        console.log('隐患类别值:', this.form.hazardCategory);\r\n\r\n        // 延迟设置，确保对话框和组件都已经渲染完成\r\n        setTimeout(() => {\r\n          this.setHazardCategoryEcho();\r\n        }, 500);\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.hazardId != null) {\r\n            updateHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // this.form.parentId = this.selectId;\r\n            this.form.hazardStatus = \"启用\";\r\n            // if (this.form.parentId == null) {\r\n            //   this.$modal.msgError(\r\n            //     \"请先在左侧树形结构中选择具体的隐患类别，然后再新增隐患问题库\"\r\n            //   );\r\n            //   return;\r\n            // }\r\n            addHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let hazardIds;\r\n      if (row && row.hazardId) {\r\n        // 单行删除\r\n        hazardIds = [row.hazardId];\r\n      } else {\r\n        // 多行删除，从this.ids中提取hazardId\r\n        hazardIds = this.ids.map((item) => item.hazardId);\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(\r\n          '是否确认删除隐患问题库编号为\"' + hazardIds.join(\",\") + '\"的数据项？'\r\n        )\r\n        .then(function () {\r\n          return delHazard(hazardIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/hazard/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `hazard_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.title {\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n::v-deep .el-tree-node__label {\r\n  display: inline-block;\r\n  max-width: 180px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #f0f7ff;\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 事故隐患标签样式 */\r\n.hazard-tag {\r\n  color: white !important;\r\n  border: none !important;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 20px;\r\n}\r\n\r\n/* 一般隐患 - 黄色背景 */\r\n.hazard-general {\r\n  background-color: #f39c12 !important;\r\n}\r\n\r\n/* 常见隐患 - 红色背景 */\r\n.hazard-common {\r\n  background-color: #e74c3c !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA2XA,IAAAA,OAAA,GAAAC,OAAA;AAUA,IAAAC,yBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,wBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,oBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,yBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,uBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,0BAAA,WAAAA,2BAAAC,YAAA;MACA,IAAAA,YAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,YAAA;QACA,KAAAhB,IAAA,CAAAmB,cAAA,GAAAH,YAAA,CAAAR,KAAA,IAAAQ,YAAA,CAAAI,KAAA;QACA,KAAApB,IAAA,CAAAZ,QAAA,GAAA4B,YAAA,CAAAK,EAAA;MACA;QACA,KAAArB,IAAA,CAAAmB,cAAA;QACA,KAAAnB,IAAA,CAAAZ,QAAA;MACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACAN,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,8BAAAlB,IAAA,CAAAmB,cAAA;MACAF,OAAA,CAAAC,GAAA,mBAAAb,QAAA;MAEA,UAAAL,IAAA,CAAAmB,cAAA;QACAF,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAAM,WAAA,YAAAA,WAAA;QAAA,IAAAC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;QACAT,OAAA,CAAAC,GAAA,oDAAAW,MAAA,CAAAJ,UAAA;QAEAF,KAAA,CAAAO,SAAA;UACA,IAAAC,SAAA,GAAAR,KAAA,CAAAS,KAAA,CAAAC,oBAAA;UACAhB,OAAA,CAAAC,GAAA,UAAAa,SAAA;UAEA,IAAAA,SAAA;YACAd,OAAA,CAAAC,GAAA;;YAEA;YACAa,SAAA,CAAAG,YAAA,GAAAX,KAAA,CAAAvB,IAAA,CAAAmB,cAAA;YACAF,OAAA,CAAAC,GAAA,qBAAAa,SAAA,CAAAG,YAAA;;YAEA;YACA,IAAAX,KAAA,CAAAlB,QAAA,IAAAkB,KAAA,CAAAlB,QAAA,CAAAsB,MAAA;cACAV,OAAA,CAAAC,GAAA;;cAEA;cACAiB,UAAA;gBACA,IAAAJ,SAAA,CAAAK,gBAAA;kBACAL,SAAA,CAAAK,gBAAA;gBACA;kBACAnB,OAAA,CAAAoB,IAAA;gBACA;cACA;YACA;cACApB,OAAA,CAAAC,GAAA;cACA,IAAAO,UAAA;gBACAU,UAAA;kBACAX,WAAA,CAAAC,UAAA;gBACA;cACA;YACA;YAEAR,OAAA,CAAAC,GAAA,gBAAAK,KAAA,CAAAvB,IAAA,CAAAmB,cAAA;UACA,WAAAM,UAAA;YACA;YACAR,OAAA,CAAAC,GAAA;YACAiB,UAAA;cACAX,WAAA,CAAAC,UAAA;YACA;UACA;YACAR,OAAA,CAAAoB,IAAA;UACA;QACA;MACA;MAEAb,WAAA;IACA;IACAc,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,MAAA,KAAAD,IAAA,CAAAC,MAAA,CAAAC,MAAA;QACA,KAAAH,YAAA,CAAAC,IAAA,CAAAC,MAAA;QACA,KAAAR,KAAA,CAAAU,IAAA,CAAAC,cAAA,CAAAJ,IAAA,CAAAC,MAAA,CAAAnB,EAAA;MACA;IACA;IACAuB,yBAAA,WAAAA,0BAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAAf,SAAA;UACA,IAAAgB,MAAA,CAAA9C,IAAA,CAAAmB,cAAA;YACA;YACA2B,MAAA,CAAAd,KAAA,CAAAU,IAAA,CAAAK,aAAA,CAAAD,MAAA,CAAA9C,IAAA,CAAAmB,cAAA;cACA,IAAA6B,WAAA,GAAAF,MAAA,CAAAd,KAAA,CAAAU,IAAA,CAAAO,cAAA;cACA,IAAAD,WAAA;gBACAF,MAAA,CAAAR,YAAA,CAAAU,WAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAE,aAAA,WAAAA,cAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA7E,GAAA,CAAAoD,MAAA;QACA,KAAA0B,QAAA;UACAlD,OAAA;UACAmD,IAAA;QACA;QACA;MACA;MACA;MACA,IAAAC,MAAA,GAAAJ,OAAA;MACA,IAAAK,QAAA,QAAAjF,GAAA,CAAAkF,GAAA,WAAAC,IAAA;QACA,WAAAC,oBAAA;UACAxE,QAAA,EAAAuE,IAAA,CAAAvE,QAAA;UACAS,YAAA,EAAA2D;QACA;MACA;MACAK,OAAA,CAAAC,UAAA,CAAAL,QAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAC,YAAA,GAAAD,GAAA,CAAAE,MAAA,CACA,UAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA,CACA,EAAA5B,MAAA;QACA,IAAAuC,aAAA,GAAAH,GAAA,CAAAE,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA;QACA,IAAAS,YAAA;UACAZ,MAAA,CAAAC,QAAA;YACAlD,OAAA,KAAA0B,MAAA,CAAAsB,OAAA;YACAG,IAAA;UACA;UACAF,MAAA,CAAAe,WAAA;QACA;UACA,IAAAC,aAAA,GAAAF,aAAA,CACAT,GAAA,WAAAY,MAAA,EAAAC,KAAA;YACA,IAAAjD,EAAA,GAAA+B,MAAA,CAAA7E,GAAA,CAAA+F,KAAA,EAAAC,YAAA;YACA,IAAAC,QAAA,MAAA3C,MAAA,CAAAsB,OAAA;YACA,6BAAAtB,MAAA,CAAAR,EAAA,2CAAAQ,MAAA,CAAA2C,QAAA;UACA,GACAC,IAAA;UAEArB,MAAA,CAAAC,QAAA;YACAlD,OAAA,KAAA0B,MAAA,CAAAuC,aAAA;YACAd,IAAA;YACAoB,wBAAA;UACA;QACA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAlE,uBAAA,WAAAA,wBAAA;MAAA,IAAAmE,MAAA;MACA,KAAA1G,WAAA;MACA,IAAAuC,+BAAA,IAAAkD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAiB,IAAA;UACA;UACAD,MAAA,CAAA1E,QAAA,GAAA0D,GAAA,CAAA3F,IAAA;UACA2G,MAAA,CAAA1G,WAAA;UACA4C,OAAA,CAAAC,GAAA,CAAA6D,MAAA,CAAA1E,QAAA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAQ,OAAA,WAAAA,QAAA;MAAA,IAAAoE,MAAA;MACA,KAAA3G,OAAA;MACA,IAAA4G,kBAAA,OAAAnG,WAAA,EAAA+E,IAAA,WAAAC,GAAA;QACA,IAAA3F,IAAA,GAAA2F,GAAA,CAAAoB,IAAA;QACA;QACA;QACA;QACAF,MAAA,CAAArG,UAAA,GAAAR,IAAA;QACA6G,MAAA,CAAAtG,KAAA,GAAAoF,GAAA,CAAApF,KAAA;QACAsG,MAAA,CAAA3G,OAAA;MACA;IACA;IACA;IACA8G,MAAA,WAAAA,OAAA;MACA,KAAAtG,IAAA;MACA,KAAAuG,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArF,IAAA;QACAb,QAAA;QACAD,UAAA;QACAE,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACA+F,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAlG,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;QACA4F,cAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAzB,WAAA,WAAAA,YAAA;MACA,KAAApF,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAK,QAAA,QAAAsB,QAAA;MACA,KAAAG,OAAA;IACA;IACA,aACAgF,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MACA,KAAAzB,WAAA;IACA;IACA;IACA2B,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAzH,GAAA,GAAAwH,SAAA,CAAAtC,GAAA,WAAAC,IAAA,EAAAY,KAAA;QACA,IAAAC,YAAA,GACA,CAAAyB,MAAA,CAAAjH,WAAA,CAAAC,OAAA,QAAAgH,MAAA,CAAAjH,WAAA,CAAAE,QAAA,GACA8G,SAAA,CAAAE,OAAA,CAAAvC,IAAA,IACA;QACA;UACAa,YAAA,EAAAA,YAAA;UACApF,QAAA,EAAAuE,IAAA,CAAAvE;QACA;MACA;MACA,KAAAX,MAAA,GAAAuH,SAAA,CAAApE,MAAA;MACA,KAAAlD,QAAA,IAAAsH,SAAA,CAAApE,MAAA;IACA;IACA,aACAuE,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAvG,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsH,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAlG,QAAA,GAAAiH,GAAA,CAAAjH,QAAA,SAAAZ,GAAA;MACA,IAAA+H,iBAAA,EAAAnH,QAAA,EAAA2E,IAAA,WAAAC,GAAA;QACAsC,MAAA,CAAArG,IAAA,GAAA+D,GAAA,CAAA3F,IAAA;QACAiI,MAAA,CAAAvH,IAAA;QACAuH,MAAA,CAAAxH,KAAA;QAEAoC,OAAA,CAAAC,GAAA,cAAAmF,MAAA,CAAArG,IAAA;QACAiB,OAAA,CAAAC,GAAA,WAAAmF,MAAA,CAAArG,IAAA,CAAAmB,cAAA;;QAEA;QACAgB,UAAA;UACAkE,MAAA,CAAA/E,qBAAA;QACA;MACA;IACA;IACA,WACAiF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAxE,KAAA,SAAAyE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAxG,IAAA,CAAAb,QAAA;YACA,IAAAwE,oBAAA,EAAA6C,MAAA,CAAAxG,IAAA,EAAA8D,IAAA,WAAAC,GAAA;cACAyC,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA1H,IAAA;cACA0H,MAAA,CAAA3F,OAAA;YACA;UACA;YACA;YACA2F,MAAA,CAAAxG,IAAA,CAAAJ,YAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAAiH,iBAAA,EAAAL,MAAA,CAAAxG,IAAA,EAAA8D,IAAA,WAAAC,GAAA;cACAyC,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA1H,IAAA;cACA0H,MAAA,CAAA3F,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiG,YAAA,WAAAA,aAAAV,GAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,SAAA;MACA,IAAAZ,GAAA,IAAAA,GAAA,CAAAjH,QAAA;QACA;QACA6H,SAAA,IAAAZ,GAAA,CAAAjH,QAAA;MACA;QACA;QACA6H,SAAA,QAAAzI,GAAA,CAAAkF,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAvE,QAAA;QAAA;MACA;MAEA,KAAAwH,MAAA,CACAM,OAAA,CACA,oBAAAD,SAAA,CAAAvC,IAAA,gBACA,EACAX,IAAA;QACA,WAAAoD,iBAAA,EAAAF,SAAA;MACA,GACAlD,IAAA;QACAiD,MAAA,CAAAlG,OAAA;QACAkG,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,gCAAAC,cAAA,CAAAC,OAAA,MAEA,KAAAxI,WAAA,aAAA8C,MAAA,CAEA,IAAA2F,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}