{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue", "mtime": 1757426209918}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_hazard", "require", "_selectHazardCategoryTree", "_interopRequireDefault", "name", "components", "selectHazardCategoryTree", "data", "treeLoading", "loading", "ids", "single", "multiple", "showSearch", "total", "hazardList", "title", "open", "queryParams", "pageNum", "pageSize", "hazardName", "hazardId", "parentId", "hazardCode", "orderNum", "target", "examid", "categoryType", "investigationContent", "hazardLevel", "hazardStatus", "rectificationDeadline", "rectificationRequirements", "hazardIdentification", "form", "rules", "required", "message", "trigger", "treeData", "defaultProps", "children", "label", "<PERSON><PERSON><PERSON><PERSON>", "selectId", "created", "getHazardHazardCategory", "getList", "methods", "handleHazardCategoryChange", "selectedItem", "console", "log", "hazardCategory", "value", "id", "setHazardCategoryEcho", "_this", "trySetEcho", "retryCount", "arguments", "length", "undefined", "concat", "$nextTick", "component", "$refs", "hazardCategorySelect", "displayValue", "setTimeout", "setTreeSelection", "warn", "expandToNode", "node", "parent", "isRoot", "tree", "setExpandedKey", "handleSelectVisibleChange", "visible", "_this2", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentNode", "getCurrentNode", "handleCommand", "command", "_this3", "$message", "type", "status", "promises", "map", "item", "updateHazard", "Promise", "allSettled", "then", "res", "successCount", "filter", "failedResults", "handleQuery", "errorMessages", "result", "index", "serialNumber", "errorMsg", "join", "dangerouslyUseHTMLString", "goToHazardCategory", "$router", "push", "path", "_this4", "code", "_this5", "listHazard", "rows", "cancel", "reset", "createBy", "createTime", "updateBy", "updateTime", "remark", "<PERSON><PERSON><PERSON><PERSON>", "resetForm", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "_this6", "indexOf", "handleAdd", "handleUpdate", "row", "_this7", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "submitForm", "_this8", "validate", "valid", "$modal", "msgSuccess", "add<PERSON><PERSON><PERSON>", "handleDelete", "_this9", "hazardIds", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "Date", "getTime"], "sources": ["src/views/inspection/hazard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- 左侧树结构 -->\r\n      <!-- <el-col :span=\"3\">\r\n        <div class=\"title mb-2\" @click=\"goToHazardCategory\">隐患类别管理</div>\r\n\r\n        <el-tree\r\n                  ref=\"tree\"\r\n          v-loading=\"treeLoading\"\r\n          :data=\"treeData\"\r\n          :props=\"defaultProps\"\r\n          :load=\"loadNode\"\r\n          lazy\r\n          @node-click=\"handleNodeClick\"\r\n        >\r\n          <template #default=\"{ node, data }\">\r\n            <el-tooltip effect=\"dark\" :content=\"data.label\" placement=\"top\">\r\n              <span\r\n                :ref=\"(el) => setLabelRef(el, node)\"\r\n                class=\"el-tree-node__label\"\r\n              >\r\n                {{ node.label }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-tree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 10px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <!-- 排查内容 -->\r\n          <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n            <el-input\r\n              v-model=\"queryParams.investigationContent\"\r\n              placeholder=\"请输入排查内容\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n            <el-input\r\n              v-model=\"queryParams.hazardCategory\"\r\n              placeholder=\"请输入隐患类别\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-dropdown @command=\"handleCommand\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                type=\"success\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                >修改状态<i class=\"el-icon-arrow-down el-icon--right\"\r\n              /></el-button>\r\n              <template #dropdown>\r\n                <el-dropdown-menu style=\"width: 100px; text-align: center\">\r\n                  <!-- 下拉选项，可根据实际需求修改 -->\r\n                  <el-dropdown-item command=\"enable\">启用</el-dropdown-item>\r\n                  <el-dropdown-item command=\"disable\">禁用</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"hazardList\"\r\n          height=\"calc(100vh - 250px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"隐患明细 id\" align=\"center\" prop=\"hazardId\" /> -->\r\n          <!-- <el-table-column label=\"隐患名称\" align=\"center\" prop=\"hazardName\" /> -->\r\n          <!-- <el-table-column label=\"父菜单ID\" align=\"center\" prop=\"parentId\" /> -->\r\n          <!-- <el-table-column label=\"隐患编号\" align=\"center\" prop=\"hazardCode\" /> -->\r\n          <!-- <el-table-column label=\"显示顺序\" align=\"center\" prop=\"orderNum\" /> -->\r\n          <!-- <el-table-column label=\" 打开方式\" align=\"center\" prop=\"target\" /> -->\r\n          <!-- <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" /> -->\r\n          <!-- <el-table-column label=\"检查类型\" align=\"center\" prop=\"examid\" /> -->\r\n          <!-- <el-table-column\r\n        label=\"大类类型：1：类型  2：行为\"\r\n        align=\"center\"\r\n        prop=\"categoryType\"\r\n      /> -->\r\n          <!-- 序号 -->\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                scope.$index +\r\n                1\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- 隐患类别 -->\r\n          <el-table-column\r\n            label=\"隐患类别\"\r\n            align=\"center\"\r\n            prop=\"hazardCategory\"\r\n            width=\"150\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"排查内容\"\r\n            align=\"center\"\r\n            prop=\"investigationContent\"\r\n            width=\"250\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column label=\"隐患级别\" align=\"center\" prop=\"hazardLevel\" /> -->\r\n          <el-table-column\r\n            label=\"整改要求\"\r\n            align=\"center\"\r\n            prop=\"rectificationRequirements\"\r\n            width=\"300\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"整改时限(天)\"\r\n            align=\"center\"\r\n            prop=\"rectificationDeadline\"\r\n            width=\"100\"\r\n          />\r\n          <!-- 事故隐患 -->\r\n          <el-table-column\r\n            label=\"事故隐患\"\r\n            align=\"center\"\r\n            prop=\"accidentHazard\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                v-if=\"scope.row.accidentHazard === '1'\"\r\n                class=\"hazard-tag hazard-general\"\r\n                size=\"small\"\r\n              >\r\n                一般\r\n              </el-tag>\r\n              <el-tag\r\n                v-else-if=\"scope.row.accidentHazard === '2'\"\r\n                class=\"hazard-tag hazard-common\"\r\n                size=\"small\"\r\n              >\r\n                常见\r\n              </el-tag>\r\n              <span v-else>{{ scope.row.accidentHazard }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"隐患标识\"\r\n            align=\"center\"\r\n            prop=\"hazardIdentification\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"hazardStatus\" />\r\n\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"150\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改隐患问题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <!-- 隐患类别 -->\r\n        <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n          <!-- <el-select\r\n            ref=\"selectRef\"\r\n            v-model=\"form.hazardCategory\"\r\n            placeholder=\"请选择隐患类别\"\r\n            style=\"width: 100%\"\r\n            clearable\r\n            :popper-append-to-body=\"false\"\r\n            @visible-change=\"handleSelectVisibleChange\"\r\n          >\r\n            <el-option\r\n              :value=\"form.hazardCategory\"\r\n              style=\"height: auto; padding: 0; border: none\"\r\n            >\r\n              <el-tree\r\n                :node-key=\"'id'\"\r\n                v-loading=\"treeLoading\"\r\n                :data=\"treeData\"\r\n                :props=\"defaultProps\"\r\n                :load=\"loadNode\"\r\n                lazy\r\n                @node-click=\"handleNodeClick\"\r\n                :expand-on-click-node=\"false\"\r\n                :highlight-current=\"true\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">{{ node.label }}</span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </el-option>\r\n          </el-select> -->\r\n          <selectHazardCategoryTree\r\n            ref=\"hazardCategorySelect\"\r\n            v-model=\"form.hazardCategory\"\r\n            :categoryList=\"treeData\"\r\n            placeholder=\"请选择隐患类别\"\r\n            @change=\"handleHazardCategoryChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患级别\" prop=\"hazardLevel\">\r\n          <el-select\r\n            v-model=\"form.hazardLevel\"\r\n            placeholder=\"请选择隐患级别\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一级\" value=\"1\" />\r\n            <el-option label=\"二级\" value=\"2\" />\r\n            <el-option label=\"三级\" value=\"3\" />\r\n            <el-option label=\"四级\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <!-- 事故隐患 -->\r\n        <el-form-item label=\"事故隐患\" prop=\"accidentHazard\">\r\n          <!-- 一般 常见 -->\r\n          <el-select\r\n            v-model=\"form.accidentHazard\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一般\" value=\"1\" />\r\n            <el-option label=\"常见\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患标识\" prop=\"hazardIdentification\">\r\n          <el-input\r\n            v-model=\"form.hazardIdentification\"\r\n            placeholder=\"请输入隐患标识\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n          <el-input\r\n            v-model=\"form.rectificationDeadline\"\r\n            type=\"Number\"\r\n            placeholder=\"请输入整改时限(天)\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"hazardStatus\">\r\n          <el-select\r\n            v-model=\"form.hazardStatus\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"启用\" value=\"启用\" />\r\n            <el-option label=\"禁用\" value=\"禁用\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n          <el-input\r\n            v-model=\"form.investigationContent\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n          <el-input\r\n            v-model=\"form.rectificationRequirements\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listHazard,\r\n  getHazard,\r\n  delHazard,\r\n  addHazard,\r\n  updateHazard,\r\n  treeHazard,\r\n  treeHazardFirst,\r\n  getHazardHazardCategory,\r\n} from \"@/api/inspection/hazard\";\r\nimport selectHazardCategoryTree from \"@/views/components/selectHazardCategoryTree.vue\";\r\n\r\nexport default {\r\n  name: \"Hazard\",\r\n  components: {\r\n    selectHazardCategoryTree,\r\n  },\r\n  data() {\r\n    return {\r\n      treeLoading: true,\r\n      // 遮罩层\r\n      loading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 隐患问题库表格数据\r\n      hazardList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        hazardName: null,\r\n        hazardId: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        hazardStatus: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" },\r\n        ],\r\n        investigationContent: [\r\n          { required: true, message: \"排查内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        rectificationRequirements: [\r\n          { required: true, message: \"整改要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      treeData: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        isLeaf: \"isLeaf\",\r\n      },\r\n      selectId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getHazardHazardCategory();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    handleHazardCategoryChange(selectedItem) {\r\n      if (selectedItem) {\r\n        console.log(selectedItem, \"selectedItem\");\r\n        this.form.hazardCategory = selectedItem.label || selectedItem.value;\r\n        this.form.parentId = selectedItem.id;\r\n      } else {\r\n        this.form.hazardCategory = null;\r\n        this.form.parentId = null;\r\n      }\r\n    },\r\n\r\n    // 设置隐患类别回显\r\n    setHazardCategoryEcho() {\r\n      console.log('开始设置隐患类别回显');\r\n      console.log('form.hazardCategory:', this.form.hazardCategory);\r\n      console.log('treeData:', this.treeData);\r\n\r\n      if (!this.form.hazardCategory) {\r\n        console.log('隐患类别值为空，跳过回显');\r\n        return;\r\n      }\r\n\r\n      const trySetEcho = (retryCount = 0) => {\r\n        console.log(`尝试设置回显，第${retryCount + 1}次`);\r\n\r\n        this.$nextTick(() => {\r\n          const component = this.$refs.hazardCategorySelect;\r\n          console.log('组件引用:', component);\r\n\r\n          if (component) {\r\n            console.log('组件存在，开始设置回显');\r\n\r\n            // 设置显示值\r\n            component.displayValue = this.form.hazardCategory;\r\n            console.log('设置 displayValue:', component.displayValue);\r\n\r\n            // 检查树数据是否已加载\r\n            if (this.treeData && this.treeData.length > 0) {\r\n              console.log('树数据已加载，设置选中状态');\r\n\r\n              // 等待树数据加载完成后设置选中状态\r\n              setTimeout(() => {\r\n                if (component.setTreeSelection) {\r\n                  component.setTreeSelection();\r\n                } else {\r\n                  console.warn('setTreeSelection 方法不存在');\r\n                }\r\n              }, 200);\r\n            } else {\r\n              console.log('树数据未加载，等待数据加载');\r\n              if (retryCount < 10) {\r\n                setTimeout(() => {\r\n                  trySetEcho(retryCount + 1);\r\n                }, 300);\r\n              }\r\n            }\r\n\r\n            console.log('隐患类别回显设置完成:', this.form.hazardCategory);\r\n          } else if (retryCount < 10) {\r\n            // 如果组件还没准备好，重试最多10次\r\n            console.log('组件未准备好，等待重试');\r\n            setTimeout(() => {\r\n              trySetEcho(retryCount + 1);\r\n            }, 300);\r\n          } else {\r\n            console.warn('隐患类别回显设置失败，组件未准备好');\r\n          }\r\n        });\r\n      };\r\n\r\n      trySetEcho();\r\n    },\r\n    expandToNode(node) {\r\n      if (node && node.parent && !node.parent.isRoot) {\r\n        this.expandToNode(node.parent);\r\n        this.$refs.tree.setExpandedKey(node.parent.id, true);\r\n      }\r\n    },\r\n    handleSelectVisibleChange(visible) {\r\n      if (visible) {\r\n        this.$nextTick(() => {\r\n          if (this.form.hazardCategory) {\r\n            // 确保节点已加载\r\n            this.$refs.tree.setCurrentKey(this.form.hazardCategory, () => {\r\n              const currentNode = this.$refs.tree.getCurrentNode();\r\n              if (currentNode) {\r\n                this.expandToNode(currentNode);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCommand(command) {\r\n      if (this.ids.length <= 0) {\r\n        this.$message({\r\n          message: \"请选择隐患条目\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      // console.log(this.ids, \"this.ids\");\r\n      const status = command === \"enable\" ? \"正常\" : \"禁用\";\r\n      const promises = this.ids.map((item) => {\r\n        return updateHazard({\r\n          hazardId: item.hazardId,\r\n          hazardStatus: status,\r\n        });\r\n      });\r\n      Promise.allSettled(promises).then((res) => {\r\n        const successCount = res.filter(\r\n          (item) => item.status === \"fulfilled\"\r\n        ).length;\r\n        const failedResults = res.filter((item) => item.status === \"rejected\");\r\n        if (successCount > 0) {\r\n          this.$message({\r\n            message: `${command === \"enable\" ? \"启用\" : \"废弃\"}成功`,\r\n            type: \"success\",\r\n          });\r\n          this.handleQuery();\r\n        } else {\r\n          const errorMessages = failedResults\r\n            .map((result, index) => {\r\n              const id = this.ids[index].serialNumber;\r\n              const errorMsg = `${command === \"enable\" ? \"启用\" : \"废弃\"}失败`;\r\n              return `序号为 ${id} 的隐患条目：${errorMsg}`;\r\n            })\r\n            .join(\"\\n\");\r\n\r\n          this.$message({\r\n            message: `${errorMessages}`,\r\n            type: \"error\",\r\n            dangerouslyUseHTMLString: true,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    goToHazardCategory() {\r\n      this.$router.push({\r\n        path: \"hazardCategory\",\r\n      });\r\n    },\r\n    getHazardHazardCategory() {\r\n      this.treeLoading = true;\r\n      getHazardHazardCategory().then((res) => {\r\n        if (res.code === 200) {\r\n          // console.log(res, \"res\");\r\n          this.treeData = res.data;\r\n          this.treeLoading = false;\r\n          console.log(this.treeData, \"this.treeData\");\r\n        }\r\n      });\r\n    },\r\n    // loadNode(node, resolve) {\r\n    //   if (node.level === 0) {\r\n    //     return resolve([]);\r\n    //   }\r\n\r\n    //   const currentNode = node.data;\r\n    //   // 若该节点已有 children，说明已请求过，直接解析现有 children\r\n    //   if (currentNode.children) {\r\n    //     return resolve(currentNode.children);\r\n    //   }\r\n    //   const parentId = currentNode.id;\r\n    //   treeHazard({ parentId })\r\n    //     .then((res) => {\r\n    //       if (res.code === 200) {\r\n    //         const children = res.rows.map((item) => ({\r\n    //           id: item.hazardId,\r\n    //           label: item.hazardName,\r\n    //           // 根据子节点是否存在判断是否为叶子节点\r\n    //           isLeaf: !item.children || item.children.length === 0,\r\n    //         }));\r\n    //         // 将子节点数据赋值给当前节点的 children 属性\r\n    //         currentNode.children = children;\r\n    //         resolve(children);\r\n    //       } else {\r\n    //         resolve([]);\r\n    //       }\r\n    //     })\r\n    //     .catch(() => {\r\n    //       resolve([]);\r\n    //     });\r\n    // },\r\n    // transformChildren(children) {\r\n    //   if (!children || children.length === 0) return [];\r\n    //   return children.map((child) => ({\r\n    //     label: child.hazardName,\r\n    //     id: child.hazardId,\r\n    //     children: this.transformChildren(child.children),\r\n    //   }));\r\n    // },\r\n    // handleNodeClick(nodeData, node) {\r\n    //   console.log(node, \"nodeData\");\r\n    //   // this.queryParams.hazardId = nodeData.id;\r\n    //   if (node.isLeaf) {\r\n    //     this.selectId = nodeData.id;\r\n    //     this.form.hazardCategory = nodeData.id; // 绑定ID而非label\r\n    //     this.$refs.selectRef.blur();\r\n    //   }\r\n    // },\r\n    // setLabelRef(el, node) {\r\n    //   if (el) {\r\n    //     this.labelRefs.set(node.id || node.label, el);\r\n    //   }\r\n    // },\r\n    /** 查询隐患问题库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listHazard(this.queryParams).then((res) => {\r\n        const data = res.rows;\r\n        // data.sort((a, b) => {\r\n        //   return new Date(b.createTime) - new Date(a.createTime);\r\n        // });\r\n        this.hazardList = data;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        hazardId: null,\r\n        hazardName: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n        accidentHazard: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.parentId = this.selectId;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item, index) => {\r\n        const serialNumber =\r\n          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +\r\n          selection.indexOf(item) +\r\n          1;\r\n        return {\r\n          serialNumber,\r\n          hazardId: item.hazardId,\r\n        };\r\n      });\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加隐患问题库\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const hazardId = row.hazardId || this.ids;\r\n      getHazard(hazardId).then((res) => {\r\n        console.log('API 返回的原始数据:', res);\r\n        console.log('API 返回的 data 字段:', res.data);\r\n\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改隐患问题库\";\r\n\r\n        console.log('设置后的表单数据:', JSON.stringify(this.form, null, 2));\r\n        console.log('隐患类别值:', this.form.hazardCategory);\r\n        console.log('parentId 值:', this.form.parentId);\r\n\r\n        // 延迟设置，确保对话框和组件都已经渲染完成\r\n        setTimeout(() => {\r\n          this.setHazardCategoryEcho();\r\n        }, 500);\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.hazardId != null) {\r\n            updateHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // this.form.parentId = this.selectId;\r\n            this.form.hazardStatus = \"启用\";\r\n            // if (this.form.parentId == null) {\r\n            //   this.$modal.msgError(\r\n            //     \"请先在左侧树形结构中选择具体的隐患类别，然后再新增隐患问题库\"\r\n            //   );\r\n            //   return;\r\n            // }\r\n            addHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let hazardIds;\r\n      if (row && row.hazardId) {\r\n        // 单行删除\r\n        hazardIds = [row.hazardId];\r\n      } else {\r\n        // 多行删除，从this.ids中提取hazardId\r\n        hazardIds = this.ids.map((item) => item.hazardId);\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(\r\n          '是否确认删除隐患问题库编号为\"' + hazardIds.join(\",\") + '\"的数据项？'\r\n        )\r\n        .then(function () {\r\n          return delHazard(hazardIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/hazard/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `hazard_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.title {\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n::v-deep .el-tree-node__label {\r\n  display: inline-block;\r\n  max-width: 180px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #f0f7ff;\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 事故隐患标签样式 */\r\n.hazard-tag {\r\n  color: white !important;\r\n  border: none !important;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 20px;\r\n}\r\n\r\n/* 一般隐患 - 黄色背景 */\r\n.hazard-general {\r\n  background-color: #f39c12 !important;\r\n}\r\n\r\n/* 常见隐患 - 红色背景 */\r\n.hazard-common {\r\n  background-color: #e74c3c !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA2XA,IAAAA,OAAA,GAAAC,OAAA;AAUA,IAAAC,yBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,wBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,oBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,yBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,uBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,0BAAA,WAAAA,2BAAAC,YAAA;MACA,IAAAA,YAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,YAAA;QACA,KAAAhB,IAAA,CAAAmB,cAAA,GAAAH,YAAA,CAAAR,KAAA,IAAAQ,YAAA,CAAAI,KAAA;QACA,KAAApB,IAAA,CAAAZ,QAAA,GAAA4B,YAAA,CAAAK,EAAA;MACA;QACA,KAAArB,IAAA,CAAAmB,cAAA;QACA,KAAAnB,IAAA,CAAAZ,QAAA;MACA;IACA;IAEA;IACAkC,qBAAA,WAAAA,sBAAA;MAAA,IAAAC,KAAA;MACAN,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,8BAAAlB,IAAA,CAAAmB,cAAA;MACAF,OAAA,CAAAC,GAAA,mBAAAb,QAAA;MAEA,UAAAL,IAAA,CAAAmB,cAAA;QACAF,OAAA,CAAAC,GAAA;QACA;MACA;MAEA,IAAAM,WAAA,YAAAA,WAAA;QAAA,IAAAC,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;QACAT,OAAA,CAAAC,GAAA,oDAAAW,MAAA,CAAAJ,UAAA;QAEAF,KAAA,CAAAO,SAAA;UACA,IAAAC,SAAA,GAAAR,KAAA,CAAAS,KAAA,CAAAC,oBAAA;UACAhB,OAAA,CAAAC,GAAA,UAAAa,SAAA;UAEA,IAAAA,SAAA;YACAd,OAAA,CAAAC,GAAA;;YAEA;YACAa,SAAA,CAAAG,YAAA,GAAAX,KAAA,CAAAvB,IAAA,CAAAmB,cAAA;YACAF,OAAA,CAAAC,GAAA,qBAAAa,SAAA,CAAAG,YAAA;;YAEA;YACA,IAAAX,KAAA,CAAAlB,QAAA,IAAAkB,KAAA,CAAAlB,QAAA,CAAAsB,MAAA;cACAV,OAAA,CAAAC,GAAA;;cAEA;cACAiB,UAAA;gBACA,IAAAJ,SAAA,CAAAK,gBAAA;kBACAL,SAAA,CAAAK,gBAAA;gBACA;kBACAnB,OAAA,CAAAoB,IAAA;gBACA;cACA;YACA;cACApB,OAAA,CAAAC,GAAA;cACA,IAAAO,UAAA;gBACAU,UAAA;kBACAX,WAAA,CAAAC,UAAA;gBACA;cACA;YACA;YAEAR,OAAA,CAAAC,GAAA,gBAAAK,KAAA,CAAAvB,IAAA,CAAAmB,cAAA;UACA,WAAAM,UAAA;YACA;YACAR,OAAA,CAAAC,GAAA;YACAiB,UAAA;cACAX,WAAA,CAAAC,UAAA;YACA;UACA;YACAR,OAAA,CAAAoB,IAAA;UACA;QACA;MACA;MAEAb,WAAA;IACA;IACAc,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,MAAA,KAAAD,IAAA,CAAAC,MAAA,CAAAC,MAAA;QACA,KAAAH,YAAA,CAAAC,IAAA,CAAAC,MAAA;QACA,KAAAR,KAAA,CAAAU,IAAA,CAAAC,cAAA,CAAAJ,IAAA,CAAAC,MAAA,CAAAnB,EAAA;MACA;IACA;IACAuB,yBAAA,WAAAA,0BAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,OAAA;QACA,KAAAf,SAAA;UACA,IAAAgB,MAAA,CAAA9C,IAAA,CAAAmB,cAAA;YACA;YACA2B,MAAA,CAAAd,KAAA,CAAAU,IAAA,CAAAK,aAAA,CAAAD,MAAA,CAAA9C,IAAA,CAAAmB,cAAA;cACA,IAAA6B,WAAA,GAAAF,MAAA,CAAAd,KAAA,CAAAU,IAAA,CAAAO,cAAA;cACA,IAAAD,WAAA;gBACAF,MAAA,CAAAR,YAAA,CAAAU,WAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAE,aAAA,WAAAA,cAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA7E,GAAA,CAAAoD,MAAA;QACA,KAAA0B,QAAA;UACAlD,OAAA;UACAmD,IAAA;QACA;QACA;MACA;MACA;MACA,IAAAC,MAAA,GAAAJ,OAAA;MACA,IAAAK,QAAA,QAAAjF,GAAA,CAAAkF,GAAA,WAAAC,IAAA;QACA,WAAAC,oBAAA;UACAxE,QAAA,EAAAuE,IAAA,CAAAvE,QAAA;UACAS,YAAA,EAAA2D;QACA;MACA;MACAK,OAAA,CAAAC,UAAA,CAAAL,QAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAC,YAAA,GAAAD,GAAA,CAAAE,MAAA,CACA,UAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA,CACA,EAAA5B,MAAA;QACA,IAAAuC,aAAA,GAAAH,GAAA,CAAAE,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA;QACA,IAAAS,YAAA;UACAZ,MAAA,CAAAC,QAAA;YACAlD,OAAA,KAAA0B,MAAA,CAAAsB,OAAA;YACAG,IAAA;UACA;UACAF,MAAA,CAAAe,WAAA;QACA;UACA,IAAAC,aAAA,GAAAF,aAAA,CACAT,GAAA,WAAAY,MAAA,EAAAC,KAAA;YACA,IAAAjD,EAAA,GAAA+B,MAAA,CAAA7E,GAAA,CAAA+F,KAAA,EAAAC,YAAA;YACA,IAAAC,QAAA,MAAA3C,MAAA,CAAAsB,OAAA;YACA,6BAAAtB,MAAA,CAAAR,EAAA,2CAAAQ,MAAA,CAAA2C,QAAA;UACA,GACAC,IAAA;UAEArB,MAAA,CAAAC,QAAA;YACAlD,OAAA,KAAA0B,MAAA,CAAAuC,aAAA;YACAd,IAAA;YACAoB,wBAAA;UACA;QACA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAlE,uBAAA,WAAAA,wBAAA;MAAA,IAAAmE,MAAA;MACA,KAAA1G,WAAA;MACA,IAAAuC,+BAAA,IAAAkD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAiB,IAAA;UACA;UACAD,MAAA,CAAA1E,QAAA,GAAA0D,GAAA,CAAA3F,IAAA;UACA2G,MAAA,CAAA1G,WAAA;UACA4C,OAAA,CAAAC,GAAA,CAAA6D,MAAA,CAAA1E,QAAA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAQ,OAAA,WAAAA,QAAA;MAAA,IAAAoE,MAAA;MACA,KAAA3G,OAAA;MACA,IAAA4G,kBAAA,OAAAnG,WAAA,EAAA+E,IAAA,WAAAC,GAAA;QACA,IAAA3F,IAAA,GAAA2F,GAAA,CAAAoB,IAAA;QACA;QACA;QACA;QACAF,MAAA,CAAArG,UAAA,GAAAR,IAAA;QACA6G,MAAA,CAAAtG,KAAA,GAAAoF,GAAA,CAAApF,KAAA;QACAsG,MAAA,CAAA3G,OAAA;MACA;IACA;IACA;IACA8G,MAAA,WAAAA,OAAA;MACA,KAAAtG,IAAA;MACA,KAAAuG,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArF,IAAA;QACAb,QAAA;QACAD,UAAA;QACAE,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACA+F,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAlG,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;QACA4F,cAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAzB,WAAA,WAAAA,YAAA;MACA,KAAApF,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAK,QAAA,QAAAsB,QAAA;MACA,KAAAG,OAAA;IACA;IACA,aACAgF,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MACA,KAAAzB,WAAA;IACA;IACA;IACA2B,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAzH,GAAA,GAAAwH,SAAA,CAAAtC,GAAA,WAAAC,IAAA,EAAAY,KAAA;QACA,IAAAC,YAAA,GACA,CAAAyB,MAAA,CAAAjH,WAAA,CAAAC,OAAA,QAAAgH,MAAA,CAAAjH,WAAA,CAAAE,QAAA,GACA8G,SAAA,CAAAE,OAAA,CAAAvC,IAAA,IACA;QACA;UACAa,YAAA,EAAAA,YAAA;UACApF,QAAA,EAAAuE,IAAA,CAAAvE;QACA;MACA;MACA,KAAAX,MAAA,GAAAuH,SAAA,CAAApE,MAAA;MACA,KAAAlD,QAAA,IAAAsH,SAAA,CAAApE,MAAA;IACA;IACA,aACAuE,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAAvG,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsH,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAlG,QAAA,GAAAiH,GAAA,CAAAjH,QAAA,SAAAZ,GAAA;MACA,IAAA+H,iBAAA,EAAAnH,QAAA,EAAA2E,IAAA,WAAAC,GAAA;QACA9C,OAAA,CAAAC,GAAA,iBAAA6C,GAAA;QACA9C,OAAA,CAAAC,GAAA,qBAAA6C,GAAA,CAAA3F,IAAA;QAEAiI,MAAA,CAAArG,IAAA,GAAA+D,GAAA,CAAA3F,IAAA;QACAiI,MAAA,CAAAvH,IAAA;QACAuH,MAAA,CAAAxH,KAAA;QAEAoC,OAAA,CAAAC,GAAA,cAAAqF,IAAA,CAAAC,SAAA,CAAAH,MAAA,CAAArG,IAAA;QACAiB,OAAA,CAAAC,GAAA,WAAAmF,MAAA,CAAArG,IAAA,CAAAmB,cAAA;QACAF,OAAA,CAAAC,GAAA,gBAAAmF,MAAA,CAAArG,IAAA,CAAAZ,QAAA;;QAEA;QACA+C,UAAA;UACAkE,MAAA,CAAA/E,qBAAA;QACA;MACA;IACA;IACA,WACAmF,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,KAAA,SAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA1G,IAAA,CAAAb,QAAA;YACA,IAAAwE,oBAAA,EAAA+C,MAAA,CAAA1G,IAAA,EAAA8D,IAAA,WAAAC,GAAA;cACA2C,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA5H,IAAA;cACA4H,MAAA,CAAA7F,OAAA;YACA;UACA;YACA;YACA6F,MAAA,CAAA1G,IAAA,CAAAJ,YAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAAmH,iBAAA,EAAAL,MAAA,CAAA1G,IAAA,EAAA8D,IAAA,WAAAC,GAAA;cACA2C,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA5H,IAAA;cACA4H,MAAA,CAAA7F,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAmG,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,SAAA;MACA,IAAAd,GAAA,IAAAA,GAAA,CAAAjH,QAAA;QACA;QACA+H,SAAA,IAAAd,GAAA,CAAAjH,QAAA;MACA;QACA;QACA+H,SAAA,QAAA3I,GAAA,CAAAkF,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAvE,QAAA;QAAA;MACA;MAEA,KAAA0H,MAAA,CACAM,OAAA,CACA,oBAAAD,SAAA,CAAAzC,IAAA,gBACA,EACAX,IAAA;QACA,WAAAsD,iBAAA,EAAAF,SAAA;MACA,GACApD,IAAA;QACAmD,MAAA,CAAApG,OAAA;QACAoG,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,gCAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA1I,WAAA,aAAA8C,MAAA,CAEA,IAAA6F,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}