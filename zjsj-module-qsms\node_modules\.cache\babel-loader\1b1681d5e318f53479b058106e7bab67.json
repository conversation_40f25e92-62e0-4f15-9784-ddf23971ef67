{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue", "mtime": 1757425716740}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_hazard", "require", "_selectHazardCategoryTree", "_interopRequireDefault", "name", "components", "selectHazardCategoryTree", "data", "treeLoading", "loading", "ids", "single", "multiple", "showSearch", "total", "hazardList", "title", "open", "queryParams", "pageNum", "pageSize", "hazardName", "hazardId", "parentId", "hazardCode", "orderNum", "target", "examid", "categoryType", "investigationContent", "hazardLevel", "hazardStatus", "rectificationDeadline", "rectificationRequirements", "hazardIdentification", "form", "rules", "required", "message", "trigger", "treeData", "defaultProps", "children", "label", "<PERSON><PERSON><PERSON><PERSON>", "selectId", "created", "getHazardHazardCategory", "getList", "methods", "handleHazardCategoryChange", "selectedItem", "console", "log", "hazardCategory", "value", "id", "expandToNode", "node", "parent", "isRoot", "$refs", "tree", "setExpandedKey", "handleSelectVisibleChange", "visible", "_this", "$nextTick", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentNode", "getCurrentNode", "handleCommand", "command", "_this2", "length", "$message", "type", "status", "promises", "map", "item", "updateHazard", "Promise", "allSettled", "then", "res", "successCount", "filter", "failedResults", "concat", "handleQuery", "errorMessages", "result", "index", "serialNumber", "errorMsg", "join", "dangerouslyUseHTMLString", "goToHazardCategory", "$router", "push", "path", "_this3", "code", "_this4", "listHazard", "rows", "cancel", "reset", "createBy", "createTime", "updateBy", "updateTime", "remark", "<PERSON><PERSON><PERSON><PERSON>", "resetForm", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "_this5", "indexOf", "handleAdd", "handleUpdate", "row", "_this6", "<PERSON><PERSON><PERSON><PERSON>", "setHazardCategoryEcho", "submitForm", "_this7", "validate", "valid", "$modal", "msgSuccess", "add<PERSON><PERSON><PERSON>", "handleDelete", "_this8", "hazardIds", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "Date", "getTime"], "sources": ["src/views/inspection/hazard/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- 左侧树结构 -->\r\n      <!-- <el-col :span=\"3\">\r\n        <div class=\"title mb-2\" @click=\"goToHazardCategory\">隐患类别管理</div>\r\n\r\n        <el-tree\r\n                  ref=\"tree\"\r\n          v-loading=\"treeLoading\"\r\n          :data=\"treeData\"\r\n          :props=\"defaultProps\"\r\n          :load=\"loadNode\"\r\n          lazy\r\n          @node-click=\"handleNodeClick\"\r\n        >\r\n          <template #default=\"{ node, data }\">\r\n            <el-tooltip effect=\"dark\" :content=\"data.label\" placement=\"top\">\r\n              <span\r\n                :ref=\"(el) => setLabelRef(el, node)\"\r\n                class=\"el-tree-node__label\"\r\n              >\r\n                {{ node.label }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-tree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 10px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <!-- 排查内容 -->\r\n          <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n            <el-input\r\n              v-model=\"queryParams.investigationContent\"\r\n              placeholder=\"请输入排查内容\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n            <el-input\r\n              v-model=\"queryParams.hazardCategory\"\r\n              placeholder=\"请输入隐患类别\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-dropdown @command=\"handleCommand\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                type=\"success\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                >修改状态<i class=\"el-icon-arrow-down el-icon--right\"\r\n              /></el-button>\r\n              <template #dropdown>\r\n                <el-dropdown-menu style=\"width: 100px; text-align: center\">\r\n                  <!-- 下拉选项，可根据实际需求修改 -->\r\n                  <el-dropdown-item command=\"enable\">启用</el-dropdown-item>\r\n                  <el-dropdown-item command=\"disable\">禁用</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"hazardList\"\r\n          height=\"calc(100vh - 250px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"隐患明细 id\" align=\"center\" prop=\"hazardId\" /> -->\r\n          <!-- <el-table-column label=\"隐患名称\" align=\"center\" prop=\"hazardName\" /> -->\r\n          <!-- <el-table-column label=\"父菜单ID\" align=\"center\" prop=\"parentId\" /> -->\r\n          <!-- <el-table-column label=\"隐患编号\" align=\"center\" prop=\"hazardCode\" /> -->\r\n          <!-- <el-table-column label=\"显示顺序\" align=\"center\" prop=\"orderNum\" /> -->\r\n          <!-- <el-table-column label=\" 打开方式\" align=\"center\" prop=\"target\" /> -->\r\n          <!-- <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" /> -->\r\n          <!-- <el-table-column label=\"检查类型\" align=\"center\" prop=\"examid\" /> -->\r\n          <!-- <el-table-column\r\n        label=\"大类类型：1：类型  2：行为\"\r\n        align=\"center\"\r\n        prop=\"categoryType\"\r\n      /> -->\r\n          <!-- 序号 -->\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                scope.$index +\r\n                1\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- 隐患类别 -->\r\n          <el-table-column\r\n            label=\"隐患类别\"\r\n            align=\"center\"\r\n            prop=\"hazardCategory\"\r\n            width=\"150\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"排查内容\"\r\n            align=\"center\"\r\n            prop=\"investigationContent\"\r\n            width=\"250\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column label=\"隐患级别\" align=\"center\" prop=\"hazardLevel\" /> -->\r\n          <el-table-column\r\n            label=\"整改要求\"\r\n            align=\"center\"\r\n            prop=\"rectificationRequirements\"\r\n            width=\"300\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"整改时限(天)\"\r\n            align=\"center\"\r\n            prop=\"rectificationDeadline\"\r\n            width=\"100\"\r\n          />\r\n          <!-- 事故隐患 -->\r\n          <el-table-column\r\n            label=\"事故隐患\"\r\n            align=\"center\"\r\n            prop=\"accidentHazard\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                v-if=\"scope.row.accidentHazard === '1'\"\r\n                class=\"hazard-tag hazard-general\"\r\n                size=\"small\"\r\n              >\r\n                一般\r\n              </el-tag>\r\n              <el-tag\r\n                v-else-if=\"scope.row.accidentHazard === '2'\"\r\n                class=\"hazard-tag hazard-common\"\r\n                size=\"small\"\r\n              >\r\n                常见\r\n              </el-tag>\r\n              <span v-else>{{ scope.row.accidentHazard }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"隐患标识\"\r\n            align=\"center\"\r\n            prop=\"hazardIdentification\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"hazardStatus\" />\r\n\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"150\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改隐患问题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <!-- 隐患类别 -->\r\n        <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n          <!-- <el-select\r\n            ref=\"selectRef\"\r\n            v-model=\"form.hazardCategory\"\r\n            placeholder=\"请选择隐患类别\"\r\n            style=\"width: 100%\"\r\n            clearable\r\n            :popper-append-to-body=\"false\"\r\n            @visible-change=\"handleSelectVisibleChange\"\r\n          >\r\n            <el-option\r\n              :value=\"form.hazardCategory\"\r\n              style=\"height: auto; padding: 0; border: none\"\r\n            >\r\n              <el-tree\r\n                :node-key=\"'id'\"\r\n                v-loading=\"treeLoading\"\r\n                :data=\"treeData\"\r\n                :props=\"defaultProps\"\r\n                :load=\"loadNode\"\r\n                lazy\r\n                @node-click=\"handleNodeClick\"\r\n                :expand-on-click-node=\"false\"\r\n                :highlight-current=\"true\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">{{ node.label }}</span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </el-option>\r\n          </el-select> -->\r\n          <selectHazardCategoryTree\r\n            ref=\"hazardCategorySelect\"\r\n            v-model=\"form.hazardCategory\"\r\n            :categoryList=\"treeData\"\r\n            placeholder=\"请选择隐患类别\"\r\n            @change=\"handleHazardCategoryChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患级别\" prop=\"hazardLevel\">\r\n          <el-select\r\n            v-model=\"form.hazardLevel\"\r\n            placeholder=\"请选择隐患级别\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一级\" value=\"1\" />\r\n            <el-option label=\"二级\" value=\"2\" />\r\n            <el-option label=\"三级\" value=\"3\" />\r\n            <el-option label=\"四级\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <!-- 事故隐患 -->\r\n        <el-form-item label=\"事故隐患\" prop=\"accidentHazard\">\r\n          <!-- 一般 常见 -->\r\n          <el-select\r\n            v-model=\"form.accidentHazard\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一般\" value=\"1\" />\r\n            <el-option label=\"常见\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患标识\" prop=\"hazardIdentification\">\r\n          <el-input\r\n            v-model=\"form.hazardIdentification\"\r\n            placeholder=\"请输入隐患标识\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n          <el-input\r\n            v-model=\"form.rectificationDeadline\"\r\n            type=\"Number\"\r\n            placeholder=\"请输入整改时限(天)\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"hazardStatus\">\r\n          <el-select\r\n            v-model=\"form.hazardStatus\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"启用\" value=\"启用\" />\r\n            <el-option label=\"禁用\" value=\"禁用\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n          <el-input\r\n            v-model=\"form.investigationContent\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n          <el-input\r\n            v-model=\"form.rectificationRequirements\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listHazard,\r\n  getHazard,\r\n  delHazard,\r\n  addHazard,\r\n  updateHazard,\r\n  treeHazard,\r\n  treeHazardFirst,\r\n  getHazardHazardCategory,\r\n} from \"@/api/inspection/hazard\";\r\nimport selectHazardCategoryTree from \"@/views/components/selectHazardCategoryTree.vue\";\r\n\r\nexport default {\r\n  name: \"Hazard\",\r\n  components: {\r\n    selectHazardCategoryTree,\r\n  },\r\n  data() {\r\n    return {\r\n      treeLoading: true,\r\n      // 遮罩层\r\n      loading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 隐患问题库表格数据\r\n      hazardList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        hazardName: null,\r\n        hazardId: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        hazardStatus: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" },\r\n        ],\r\n        investigationContent: [\r\n          { required: true, message: \"排查内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        rectificationRequirements: [\r\n          { required: true, message: \"整改要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      treeData: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        isLeaf: \"isLeaf\",\r\n      },\r\n      selectId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getHazardHazardCategory();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    handleHazardCategoryChange(selectedItem) {\r\n      if (selectedItem) {\r\n        console.log(selectedItem, \"selectedItem\");\r\n        this.form.hazardCategory = selectedItem.label || selectedItem.value;\r\n        this.form.parentId = selectedItem.id;\r\n      } else {\r\n        this.form.hazardCategory = null;\r\n        this.form.parentId = null;\r\n      }\r\n    },\r\n    expandToNode(node) {\r\n      if (node && node.parent && !node.parent.isRoot) {\r\n        this.expandToNode(node.parent);\r\n        this.$refs.tree.setExpandedKey(node.parent.id, true);\r\n      }\r\n    },\r\n    handleSelectVisibleChange(visible) {\r\n      if (visible) {\r\n        this.$nextTick(() => {\r\n          if (this.form.hazardCategory) {\r\n            // 确保节点已加载\r\n            this.$refs.tree.setCurrentKey(this.form.hazardCategory, () => {\r\n              const currentNode = this.$refs.tree.getCurrentNode();\r\n              if (currentNode) {\r\n                this.expandToNode(currentNode);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCommand(command) {\r\n      if (this.ids.length <= 0) {\r\n        this.$message({\r\n          message: \"请选择隐患条目\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      // console.log(this.ids, \"this.ids\");\r\n      const status = command === \"enable\" ? \"正常\" : \"禁用\";\r\n      const promises = this.ids.map((item) => {\r\n        return updateHazard({\r\n          hazardId: item.hazardId,\r\n          hazardStatus: status,\r\n        });\r\n      });\r\n      Promise.allSettled(promises).then((res) => {\r\n        const successCount = res.filter(\r\n          (item) => item.status === \"fulfilled\"\r\n        ).length;\r\n        const failedResults = res.filter((item) => item.status === \"rejected\");\r\n        if (successCount > 0) {\r\n          this.$message({\r\n            message: `${command === \"enable\" ? \"启用\" : \"废弃\"}成功`,\r\n            type: \"success\",\r\n          });\r\n          this.handleQuery();\r\n        } else {\r\n          const errorMessages = failedResults\r\n            .map((result, index) => {\r\n              const id = this.ids[index].serialNumber;\r\n              const errorMsg = `${command === \"enable\" ? \"启用\" : \"废弃\"}失败`;\r\n              return `序号为 ${id} 的隐患条目：${errorMsg}`;\r\n            })\r\n            .join(\"\\n\");\r\n\r\n          this.$message({\r\n            message: `${errorMessages}`,\r\n            type: \"error\",\r\n            dangerouslyUseHTMLString: true,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    goToHazardCategory() {\r\n      this.$router.push({\r\n        path: \"hazardCategory\",\r\n      });\r\n    },\r\n    getHazardHazardCategory() {\r\n      this.treeLoading = true;\r\n      getHazardHazardCategory().then((res) => {\r\n        if (res.code === 200) {\r\n          // console.log(res, \"res\");\r\n          this.treeData = res.data;\r\n          this.treeLoading = false;\r\n          console.log(this.treeData, \"this.treeData\");\r\n        }\r\n      });\r\n    },\r\n    // loadNode(node, resolve) {\r\n    //   if (node.level === 0) {\r\n    //     return resolve([]);\r\n    //   }\r\n\r\n    //   const currentNode = node.data;\r\n    //   // 若该节点已有 children，说明已请求过，直接解析现有 children\r\n    //   if (currentNode.children) {\r\n    //     return resolve(currentNode.children);\r\n    //   }\r\n    //   const parentId = currentNode.id;\r\n    //   treeHazard({ parentId })\r\n    //     .then((res) => {\r\n    //       if (res.code === 200) {\r\n    //         const children = res.rows.map((item) => ({\r\n    //           id: item.hazardId,\r\n    //           label: item.hazardName,\r\n    //           // 根据子节点是否存在判断是否为叶子节点\r\n    //           isLeaf: !item.children || item.children.length === 0,\r\n    //         }));\r\n    //         // 将子节点数据赋值给当前节点的 children 属性\r\n    //         currentNode.children = children;\r\n    //         resolve(children);\r\n    //       } else {\r\n    //         resolve([]);\r\n    //       }\r\n    //     })\r\n    //     .catch(() => {\r\n    //       resolve([]);\r\n    //     });\r\n    // },\r\n    // transformChildren(children) {\r\n    //   if (!children || children.length === 0) return [];\r\n    //   return children.map((child) => ({\r\n    //     label: child.hazardName,\r\n    //     id: child.hazardId,\r\n    //     children: this.transformChildren(child.children),\r\n    //   }));\r\n    // },\r\n    // handleNodeClick(nodeData, node) {\r\n    //   console.log(node, \"nodeData\");\r\n    //   // this.queryParams.hazardId = nodeData.id;\r\n    //   if (node.isLeaf) {\r\n    //     this.selectId = nodeData.id;\r\n    //     this.form.hazardCategory = nodeData.id; // 绑定ID而非label\r\n    //     this.$refs.selectRef.blur();\r\n    //   }\r\n    // },\r\n    // setLabelRef(el, node) {\r\n    //   if (el) {\r\n    //     this.labelRefs.set(node.id || node.label, el);\r\n    //   }\r\n    // },\r\n    /** 查询隐患问题库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listHazard(this.queryParams).then((res) => {\r\n        const data = res.rows;\r\n        // data.sort((a, b) => {\r\n        //   return new Date(b.createTime) - new Date(a.createTime);\r\n        // });\r\n        this.hazardList = data;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        hazardId: null,\r\n        hazardName: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n        accidentHazard: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.parentId = this.selectId;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item, index) => {\r\n        const serialNumber =\r\n          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +\r\n          selection.indexOf(item) +\r\n          1;\r\n        return {\r\n          serialNumber,\r\n          hazardId: item.hazardId,\r\n        };\r\n      });\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加隐患问题库\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const hazardId = row.hazardId || this.ids;\r\n      getHazard(hazardId).then((res) => {\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改隐患问题库\";\r\n        \r\n        // 延迟设置，确保组件已经渲染完成\r\n        this.setHazardCategoryEcho();\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.hazardId != null) {\r\n            updateHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // this.form.parentId = this.selectId;\r\n            this.form.hazardStatus = \"启用\";\r\n            // if (this.form.parentId == null) {\r\n            //   this.$modal.msgError(\r\n            //     \"请先在左侧树形结构中选择具体的隐患类别，然后再新增隐患问题库\"\r\n            //   );\r\n            //   return;\r\n            // }\r\n            addHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let hazardIds;\r\n      if (row && row.hazardId) {\r\n        // 单行删除\r\n        hazardIds = [row.hazardId];\r\n      } else {\r\n        // 多行删除，从this.ids中提取hazardId\r\n        hazardIds = this.ids.map((item) => item.hazardId);\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(\r\n          '是否确认删除隐患问题库编号为\"' + hazardIds.join(\",\") + '\"的数据项？'\r\n        )\r\n        .then(function () {\r\n          return delHazard(hazardIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/hazard/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `hazard_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.title {\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n::v-deep .el-tree-node__label {\r\n  display: inline-block;\r\n  max-width: 180px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #f0f7ff;\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 事故隐患标签样式 */\r\n.hazard-tag {\r\n  color: white !important;\r\n  border: none !important;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 20px;\r\n}\r\n\r\n/* 一般隐患 - 黄色背景 */\r\n.hazard-general {\r\n  background-color: #f39c12 !important;\r\n}\r\n\r\n/* 常见隐患 - 红色背景 */\r\n.hazard-common {\r\n  background-color: #e74c3c !important;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA2XA,IAAAA,OAAA,GAAAC,OAAA;AAUA,IAAAC,yBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IACAC,wBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAL,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,oBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,yBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,uBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,0BAAA,WAAAA,2BAAAC,YAAA;MACA,IAAAA,YAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,YAAA;QACA,KAAAhB,IAAA,CAAAmB,cAAA,GAAAH,YAAA,CAAAR,KAAA,IAAAQ,YAAA,CAAAI,KAAA;QACA,KAAApB,IAAA,CAAAZ,QAAA,GAAA4B,YAAA,CAAAK,EAAA;MACA;QACA,KAAArB,IAAA,CAAAmB,cAAA;QACA,KAAAnB,IAAA,CAAAZ,QAAA;MACA;IACA;IACAkC,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAA,IAAA,IAAAA,IAAA,CAAAC,MAAA,KAAAD,IAAA,CAAAC,MAAA,CAAAC,MAAA;QACA,KAAAH,YAAA,CAAAC,IAAA,CAAAC,MAAA;QACA,KAAAE,KAAA,CAAAC,IAAA,CAAAC,cAAA,CAAAL,IAAA,CAAAC,MAAA,CAAAH,EAAA;MACA;IACA;IACAQ,yBAAA,WAAAA,0BAAAC,OAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,OAAA;QACA,KAAAE,SAAA;UACA,IAAAD,KAAA,CAAA/B,IAAA,CAAAmB,cAAA;YACA;YACAY,KAAA,CAAAL,KAAA,CAAAC,IAAA,CAAAM,aAAA,CAAAF,KAAA,CAAA/B,IAAA,CAAAmB,cAAA;cACA,IAAAe,WAAA,GAAAH,KAAA,CAAAL,KAAA,CAAAC,IAAA,CAAAQ,cAAA;cACA,IAAAD,WAAA;gBACAH,KAAA,CAAAT,YAAA,CAAAY,WAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAE,aAAA,WAAAA,cAAAC,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA/D,GAAA,CAAAgE,MAAA;QACA,KAAAC,QAAA;UACArC,OAAA;UACAsC,IAAA;QACA;QACA;MACA;MACA;MACA,IAAAC,MAAA,GAAAL,OAAA;MACA,IAAAM,QAAA,QAAApE,GAAA,CAAAqE,GAAA,WAAAC,IAAA;QACA,WAAAC,oBAAA;UACA3D,QAAA,EAAA0D,IAAA,CAAA1D,QAAA;UACAS,YAAA,EAAA8C;QACA;MACA;MACAK,OAAA,CAAAC,UAAA,CAAAL,QAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAC,YAAA,GAAAD,GAAA,CAAAE,MAAA,CACA,UAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA,CACA,EAAAH,MAAA;QACA,IAAAc,aAAA,GAAAH,GAAA,CAAAE,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,CAAAH,MAAA;QAAA;QACA,IAAAS,YAAA;UACAb,MAAA,CAAAE,QAAA;YACArC,OAAA,KAAAmD,MAAA,CAAAjB,OAAA;YACAI,IAAA;UACA;UACAH,MAAA,CAAAiB,WAAA;QACA;UACA,IAAAC,aAAA,GAAAH,aAAA,CACAT,GAAA,WAAAa,MAAA,EAAAC,KAAA;YACA,IAAArC,EAAA,GAAAiB,MAAA,CAAA/D,GAAA,CAAAmF,KAAA,EAAAC,YAAA;YACA,IAAAC,QAAA,MAAAN,MAAA,CAAAjB,OAAA;YACA,6BAAAiB,MAAA,CAAAjC,EAAA,2CAAAiC,MAAA,CAAAM,QAAA;UACA,GACAC,IAAA;UAEAvB,MAAA,CAAAE,QAAA;YACArC,OAAA,KAAAmD,MAAA,CAAAE,aAAA;YACAf,IAAA;YACAqB,wBAAA;UACA;QACA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAtD,uBAAA,WAAAA,wBAAA;MAAA,IAAAuD,MAAA;MACA,KAAA9F,WAAA;MACA,IAAAuC,+BAAA,IAAAqC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAkB,IAAA;UACA;UACAD,MAAA,CAAA9D,QAAA,GAAA6C,GAAA,CAAA9E,IAAA;UACA+F,MAAA,CAAA9F,WAAA;UACA4C,OAAA,CAAAC,GAAA,CAAAiD,MAAA,CAAA9D,QAAA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAQ,OAAA,WAAAA,QAAA;MAAA,IAAAwD,MAAA;MACA,KAAA/F,OAAA;MACA,IAAAgG,kBAAA,OAAAvF,WAAA,EAAAkE,IAAA,WAAAC,GAAA;QACA,IAAA9E,IAAA,GAAA8E,GAAA,CAAAqB,IAAA;QACA;QACA;QACA;QACAF,MAAA,CAAAzF,UAAA,GAAAR,IAAA;QACAiG,MAAA,CAAA1F,KAAA,GAAAuE,GAAA,CAAAvE,KAAA;QACA0F,MAAA,CAAA/F,OAAA;MACA;IACA;IACA;IACAkG,MAAA,WAAAA,OAAA;MACA,KAAA1F,IAAA;MACA,KAAA2F,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzE,IAAA;QACAb,QAAA;QACAD,UAAA;QACAE,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAmF,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,MAAA;QACAtF,MAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,qBAAA;QACAC,yBAAA;QACAC,oBAAA;QACAgF,cAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAzB,WAAA,WAAAA,YAAA;MACA,KAAAxE,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAK,QAAA,QAAAsB,QAAA;MACA,KAAAG,OAAA;IACA;IACA,aACAoE,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MACA,KAAAzB,WAAA;IACA;IACA;IACA2B,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,KAAA7G,GAAA,GAAA4G,SAAA,CAAAvC,GAAA,WAAAC,IAAA,EAAAa,KAAA;QACA,IAAAC,YAAA,GACA,CAAAyB,MAAA,CAAArG,WAAA,CAAAC,OAAA,QAAAoG,MAAA,CAAArG,WAAA,CAAAE,QAAA,GACAkG,SAAA,CAAAE,OAAA,CAAAxC,IAAA,IACA;QACA;UACAc,YAAA,EAAAA,YAAA;UACAxE,QAAA,EAAA0D,IAAA,CAAA1D;QACA;MACA;MACA,KAAAX,MAAA,GAAA2G,SAAA,CAAA5C,MAAA;MACA,KAAA9D,QAAA,IAAA0G,SAAA,CAAA5C,MAAA;IACA;IACA,aACA+C,SAAA,WAAAA,UAAA;MACA,KAAAb,KAAA;MACA,KAAA3F,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA0G,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAhB,KAAA;MACA,IAAAtF,QAAA,GAAAqG,GAAA,CAAArG,QAAA,SAAAZ,GAAA;MACA,IAAAmH,iBAAA,EAAAvG,QAAA,EAAA8D,IAAA,WAAAC,GAAA;QACAuC,MAAA,CAAAzF,IAAA,GAAAkD,GAAA,CAAA9E,IAAA;QACAqH,MAAA,CAAA3G,IAAA;QACA2G,MAAA,CAAA5G,KAAA;;QAEA;QACA4G,MAAA,CAAAE,qBAAA;MACA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAnE,KAAA,SAAAoE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA7F,IAAA,CAAAb,QAAA;YACA,IAAA2D,oBAAA,EAAA+C,MAAA,CAAA7F,IAAA,EAAAiD,IAAA,WAAAC,GAAA;cACA2C,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA/G,IAAA;cACA+G,MAAA,CAAAhF,OAAA;YACA;UACA;YACA;YACAgF,MAAA,CAAA7F,IAAA,CAAAJ,YAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,IAAAsG,iBAAA,EAAAL,MAAA,CAAA7F,IAAA,EAAAiD,IAAA,WAAAC,GAAA;cACA2C,MAAA,CAAAG,MAAA,CAAAC,UAAA;cACAJ,MAAA,CAAA/G,IAAA;cACA+G,MAAA,CAAAhF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAsF,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAAC,SAAA;MACA,IAAAb,GAAA,IAAAA,GAAA,CAAArG,QAAA;QACA;QACAkH,SAAA,IAAAb,GAAA,CAAArG,QAAA;MACA;QACA;QACAkH,SAAA,QAAA9H,GAAA,CAAAqE,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAA1D,QAAA;QAAA;MACA;MAEA,KAAA6G,MAAA,CACAM,OAAA,CACA,oBAAAD,SAAA,CAAAxC,IAAA,gBACA,EACAZ,IAAA;QACA,WAAAsD,iBAAA,EAAAF,SAAA;MACA,GACApD,IAAA;QACAmD,MAAA,CAAAvF,OAAA;QACAuF,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAO,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,gCAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA7H,WAAA,aAAAuE,MAAA,CAEA,IAAAuD,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}