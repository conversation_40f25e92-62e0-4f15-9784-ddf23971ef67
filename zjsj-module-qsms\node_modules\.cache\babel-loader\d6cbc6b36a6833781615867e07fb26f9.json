{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue", "mtime": 1757424290727}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zjContractorBlaklist", "require", "_zjContractorInfo", "name", "dicts", "data", "activeTab", "loading", "ids", "selectedRows", "single", "multiple", "showSearch", "total", "zjContractorBlaklistList", "title", "open", "contractorOptions", "availableContractorList", "managerOptions", "personnelLoading", "selectedPersonnelRows", "personnelIds", "personnelTotal", "personnelList", "personnelQueryParams", "pageNum", "pageSize", "contractorName", "personnelStatus", "queryParams", "administratorId", "form", "rules", "contractorId", "required", "message", "trigger", "blacklistReason", "min", "max", "personnelOpen", "personnelTitle", "personnelForm", "personnelRules", "personnelName", "personnelPhone", "idNumber", "blacklistDialogOpen", "blacklistForm", "personnelId", "blacklistRules", "availablePersonnelList", "computed", "hasSelection", "length", "hasPersonnelSelection", "created", "getPersonnelList", "getList", "loadManagerOptions", "loadContractorOptions", "methods", "initPersonnelMockData", "id", "initMockData", "creditCode", "contractorType", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "_this", "params", "_objectSpread2", "default", "blacklistStatus", "listZjContractorInfo", "then", "response", "rows", "catch", "cancel", "reset", "resetForm", "handleContractorSelectChange", "selectedContractor", "find", "contractor", "handleQuery", "reset<PERSON><PERSON>y", "_this2", "getUserInfo", "map", "manager", "value", "userId", "label", "nick<PERSON><PERSON>", "userName", "error", "console", "$modal", "msgError", "_this3", "loadContractorOptionsFallback", "_this4", "getContractorInfo", "handleSelectionChange", "selection", "item", "handleAdd", "loadAvailableContractors", "_this5", "Array", "isArray", "<PERSON><PERSON><PERSON>", "legalRepresentative", "handleUpdate", "row", "_this6", "getZjContractorInfo", "submitForm", "_this7", "$refs", "validate", "valid", "updateData", "updateZjContractorInfo", "msgSuccess", "handleDelete", "_this8", "confirm", "delZjContractorB<PERSON><PERSON>", "handleExport", "download", "concat", "Date", "getTime", "handleRemoveFromBlacklist", "_this9", "_this0", "listZjContractorBlaklist", "warn", "handlePersonnelQuery", "reset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlePersonnelSelectionChange", "handleAddPersonnel", "_this1", "initAvailablePersonnelList", "$nextTick", "resetBlacklistForm", "handleAddNewPersonnel", "resetPersonnel", "handleEditPersonnel", "handleDeletePersonnel", "_this10", "handleExportPersonnel", "formatIdNumber", "substring", "cancelPersonnel", "submitPersonnelForm", "_this11", "_this12", "filter", "person", "_this13", "resetFields", "clearValidate", "cancelBlacklist", "handlePersonnelSelectChange", "selectedId", "log", "validateField", "submitBlacklistForm", "_this14", "addToBlacklist", "$message", "warning", "_this15", "<PERSON><PERSON><PERSON>", "requestData", "status", "phonenumber", "blacklistState", "addZjContractorBlaklist", "setTimeout"], "sources": ["src/views/contractor/zjContractorBlaklist/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 标签页 -->\r\n    <el-tabs v-model=\"activeTab\" class=\"contractor-tabs\">\r\n      <el-tab-pane label=\"承包商\" name=\"contractor\"></el-tab-pane>\r\n      <el-tab-pane label=\"承包商人员\" name=\"personnel\"></el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <!-- 承包商人员标签页内容 -->\r\n    <div v-show=\"activeTab === 'personnel'\">\r\n      <!-- 人员搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"personnelQueryParams\"\r\n          ref=\"personnelQueryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"所属承包商：\" prop=\"contractorName\">\r\n            <el-select\r\n              v-model=\"personnelQueryParams.contractorName\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in contractorOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.label\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"关键信息：\" prop=\"keyword\">\r\n            <el-input\r\n              v-model=\"personnelQueryParams.keyword\"\r\n              placeholder=\"请输入人员\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handlePersonnelQuery\"\r\n            />\r\n          </el-form-item> -->\r\n\r\n          <el-form-item label=\"人员状态：\" prop=\"personnelStatus\">\r\n            <el-radio-group v-model=\"personnelQueryParams.personnelStatus\">\r\n              <el-radio label=\"0\">在职</el-radio>\r\n              <el-radio label=\"1\">离职</el-radio>\r\n              <el-radio label=\"\">全部</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetPersonnelQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handlePersonnelQuery\"\r\n              >查询</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商人员表格 -->\r\n      <div class=\"personnel-container\">\r\n        <div class=\"personnel-header\">\r\n          <div class=\"personnel-title\">\r\n            承包商人员列表\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedPersonnelRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"personnel-actions\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAddPersonnel\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasPersonnelSelection\"\r\n              @click=\"handleExportPersonnel\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"personnelLoading\"\r\n          :data=\"personnelList\"\r\n          @selection-change=\"handlePersonnelSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"人员姓名\"\r\n            align=\"center\"\r\n            prop=\"personnelName\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"personnelStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span\r\n                :class=\"\r\n                  scope.row.personnelStatus === '0'\r\n                    ? 'status-active'\r\n                    : 'status-inactive'\r\n                \"\r\n              >\r\n                {{ scope.row.personnelStatus === \"0\" ? \"在职\" : \"离职\" }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"联系电话\"\r\n            align=\"center\"\r\n            prop=\"personnelPhone\"\r\n          />\r\n          <el-table-column label=\"身份证号\" align=\"center\" prop=\"idNumber\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatIdNumber(scope.row.idNumber) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"所属承包商\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"150\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleEditPersonnel(scope.row)\"\r\n              >编辑</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDeletePersonnel(scope.row)\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"personnelTotal > 0\"\r\n        :total=\"personnelTotal\"\r\n        :page.sync=\"personnelQueryParams.pageNum\"\r\n        :limit.sync=\"personnelQueryParams.pageSize\"\r\n        @pagination=\"getPersonnelList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 承包商黑名单标签页内容 -->\r\n    <div v-show=\"activeTab === 'contractor'\">\r\n      <!-- 黑名单搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"承包商名称：\" prop=\"contractorName\">\r\n            <el-input\r\n              v-model=\"queryParams.contractorName\"\r\n              placeholder=\"请输入内容\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"管理人：\" prop=\"administratorId\">\r\n            <el-select\r\n              v-model=\"queryParams.administratorId\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in managerOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商黑名单区域 -->\r\n      <div class=\"blacklist-container\">\r\n        <!-- 承包商黑名单标题和操作按钮 -->\r\n        <div class=\"blacklist-header\">\r\n          <div class=\"blacklist-title\">\r\n            承包商黑名单\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"blacklist-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:add']\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasSelection\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:export']\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"zjContractorBlaklistList\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"承包商名称\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"统一社会信用代码\"\r\n            align=\"center\"\r\n            prop=\"creditCode\"\r\n          />\r\n          <el-table-column\r\n            label=\"承包商类型\"\r\n            align=\"center\"\r\n            prop=\"contractorType\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag\r\n                :options=\"dict.type.sys_contractor_type\"\r\n                :value=\"scope.row.contractorType\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"管理人\"\r\n            align=\"center\"\r\n            prop=\"administratorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"负责人\"\r\n            align=\"center\"\r\n            prop=\"contractorManager\"\r\n          />\r\n          <el-table-column\r\n            label=\"黑名单原因\"\r\n            align=\"center\"\r\n            prop=\"blacklistReason\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"100\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleRemoveFromBlacklist(scope.row)\"\r\n                v-hasPermi=\"['contractor:zjContractorBlaklist:remove']\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改承包商黑名单对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      class=\"contractor-blacklist-dialog\"\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商将不能被加入到应用项目中，取消对应项目人员权限。默认承包商管理人进行审批。\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"拉黑承包商\" prop=\"contractorId\" v-if=\"!form.id\">\r\n          <el-select\r\n            v-model=\"form.contractorId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handleContractorSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"contractor in availableContractorList\"\r\n              :key=\"contractor.id\"\r\n              :label=\"contractor.contractorName\"\r\n              :value=\"contractor.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"承包商名称\" prop=\"contractorName\" v-if=\"form.id\">\r\n          <el-input v-model=\"form.contractorName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"form.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改承包商人员对话框 -->\r\n    <el-dialog\r\n      :title=\"personnelTitle\"\r\n      :visible.sync=\"personnelOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"personnelForm\"\r\n        :model=\"personnelForm\"\r\n        :rules=\"personnelRules\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"人员姓名\" prop=\"personnelName\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelName\"\r\n            placeholder=\"请输入人员姓名\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系电话\" prop=\"personnelPhone\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelPhone\"\r\n            placeholder=\"请输入联系电话\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idNumber\">\r\n          <el-input\r\n            v-model=\"personnelForm.idNumber\"\r\n            placeholder=\"请输入身份证号\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"所属承包商\" prop=\"contractorId\">\r\n          <el-select\r\n            v-model=\"personnelForm.contractorId\"\r\n            placeholder=\"请选择承包商\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in contractorOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"人员状态\" prop=\"personnelStatus\">\r\n          <el-radio-group v-model=\"personnelForm.personnelStatus\">\r\n            <el-radio label=\"0\">在职</el-radio>\r\n            <el-radio label=\"1\">离职</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPersonnelForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPersonnel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 加入黑名单对话框 -->\r\n    <el-dialog\r\n      title=\"加入黑名单\"\r\n      :visible.sync=\"blacklistDialogOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        title=\"\"\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商人员将不能被新加入到项目中，并在现有项目中被列为黑名单人员，取消人工权限。默认承包商管理人进行审批\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form\r\n        ref=\"blacklistForm\"\r\n        :model=\"blacklistForm\"\r\n        :rules=\"blacklistRules\"\r\n        label-width=\"130px\"\r\n      >\r\n        <el-form-item label=\"拉黑承包商人员\" prop=\"personnelId\">\r\n          <el-select\r\n            v-model=\"blacklistForm.personnelId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handlePersonnelSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"person in availablePersonnelList\"\r\n              :key=\"person.userId\"\r\n              :label=\"person.nickName\"\r\n              :value=\"person.userId\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"blacklistForm.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBlacklist\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitBlacklistForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjContractorBlaklist,\r\n  getZjContractorBlaklist,\r\n  delZjContractorBlaklist,\r\n  addZjContractorBlaklist,\r\n  updateZjContractorBlaklist,\r\n  getUserInfo,\r\n} from \"@/api/contractor/zjContractorBlaklist\";\r\nimport {\r\n  listZjContractorInfo,\r\n  getZjContractorInfo,\r\n  updateZjContractorInfo,\r\n  getContractorInfo,\r\n} from \"@/api/contractor/zjContractorInfo\";\r\n\r\nexport default {\r\n  name: \"ZjContractorBlaklist\",\r\n  dicts: [\"sys_contractor_type\"],\r\n  data() {\r\n    return {\r\n      // 当前活动标签页\r\n      activeTab: \"contractor\",\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 承包商黑名单表格数据\r\n      zjContractorBlaklistList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 承包商选项\r\n      contractorOptions: [],\r\n      // 可添加到黑名单的承包商列表\r\n      availableContractorList: [],\r\n      // 管理人选项\r\n      managerOptions: [],\r\n      // 承包商人员相关数据\r\n      personnelLoading: true,\r\n      selectedPersonnelRows: [],\r\n      personnelIds: [],\r\n      personnelTotal: 0,\r\n      personnelList: [],\r\n      personnelQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        personnelStatus: \"\",\r\n      },\r\n      // 承包商黑名单查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        administratorId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contractorId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要加入黑名单的承包商\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          { required: true, message: \"请输入黑名单原因\", trigger: \"blur\" },\r\n          { min: 5, message: \"黑名单原因至少需要5个字符\", trigger: \"blur\" },\r\n          { max: 500, message: \"黑名单原因不能超过500个字符\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      // 人员弹窗相关\r\n      personnelOpen: false,\r\n      personnelTitle: \"\",\r\n      personnelForm: {},\r\n      personnelRules: {\r\n        personnelName: [\r\n          { required: true, message: \"人员姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personnelPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        idNumber: [\r\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractorId: [\r\n          { required: true, message: \"请选择所属承包商\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      // 加入黑名单弹窗相关\r\n      blacklistDialogOpen: false,\r\n      blacklistForm: {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      },\r\n      blacklistRules: {\r\n        personnelId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要拉黑的承包商人员\",\r\n            trigger: [\"change\", \"blur\"],\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          {\r\n            required: true,\r\n            message: \"请输入拉黑原因\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            min: 5,\r\n            message: \"拉黑原因至少需要5个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            max: 500,\r\n            message: \"拉黑原因不能超过500个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n        ],\r\n      },\r\n      // 可选择的人员列表（未在黑名单中的人员）\r\n      availablePersonnelList: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否有选中项\r\n    hasSelection() {\r\n      return this.ids.length > 0;\r\n    },\r\n    // 是否有选中的人员项\r\n    hasPersonnelSelection() {\r\n      return this.personnelIds.length > 0;\r\n    },\r\n  },\r\n  created() {\r\n    // 初始化两个标签页的数据\r\n    this.getPersonnelList();\r\n    this.getList();\r\n    // 加载管理人选项\r\n    this.loadManagerOptions();\r\n    // 加载承包商选项\r\n    this.loadContractorOptions();\r\n  },\r\n  methods: {\r\n    /** 初始化承包商人员模拟数据 */\r\n    initPersonnelMockData() {\r\n      this.personnelList = [\r\n        {\r\n          id: 1,\r\n          personnelName: \"张三\",\r\n          personnelStatus: \"0\",\r\n          personnelPhone: \"13800138000\",\r\n          idNumber: \"320123199001011234\",\r\n          contractorName: \"智创机械集团\",\r\n        },\r\n        {\r\n          id: 2,\r\n          personnelName: \"李四\",\r\n          personnelStatus: \"1\",\r\n          personnelPhone: \"13900139000\",\r\n          idNumber: \"320123199002021235\",\r\n          contractorName: \"精工电子设备制造集团\",\r\n        },\r\n      ];\r\n      this.personnelTotal = 2;\r\n      this.personnelLoading = false;\r\n    },\r\n    /** 初始化承包商黑名单模拟数据 */\r\n    initMockData() {\r\n      this.zjContractorBlaklistList = [\r\n        {\r\n          id: 1,\r\n          contractorName: \"广东明华工程有限公司\",\r\n          creditCode: \"91320736617893075\",\r\n          contractorType: \"1\",\r\n          managerName: \"智威科技智慧工厂\",\r\n          responsiblePerson: \"王佳明\",\r\n          blacklistReason: \"1\",\r\n        },\r\n      ];\r\n      this.total = 1;\r\n      this.loading = false;\r\n    },\r\n    /** 查询承包商黑名单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 使用承包商信息接口，传blacklistStatus=2查询黑名单\r\n      const params = {\r\n        ...this.queryParams,\r\n        blacklistStatus: 2,\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          this.zjContractorBlaklistList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据\r\n          this.initMockData();\r\n        });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        contractorId: null,\r\n        contractorName: null,\r\n        creditCode: null,\r\n        contractorType: null,\r\n        managerName: null,\r\n        responsiblePerson: null,\r\n        blacklistReason: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 承包商选择变化处理 */\r\n    handleContractorSelectChange(contractorId) {\r\n      if (contractorId) {\r\n        // 根据选择的承包商ID填充承包商信息\r\n        const selectedContractor = this.availableContractorList.find(\r\n          (contractor) => contractor.id === contractorId\r\n        );\r\n        if (selectedContractor) {\r\n          this.form.contractorName = selectedContractor.contractorName;\r\n          this.form.creditCode = selectedContractor.creditCode;\r\n          this.form.contractorType = selectedContractor.contractorType;\r\n          this.form.managerName = selectedContractor.managerName;\r\n          this.form.responsiblePerson = selectedContractor.responsiblePerson;\r\n        }\r\n      } else {\r\n        // 清空承包商信息\r\n        this.form.contractorName = null;\r\n        this.form.creditCode = null;\r\n        this.form.contractorType = null;\r\n        this.form.managerName = null;\r\n        this.form.responsiblePerson = null;\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 加载管理人选项 */\r\n    loadManagerOptions() {\r\n      // 使用getUserInfo接口获取管理人员数据，传递type=1表示获取管理人员\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.data || response.rows || response || [];\r\n          this.managerOptions = data.map((manager) => ({\r\n            value: manager.userId || manager.id,\r\n            label: manager.nickName || manager.name || manager.userName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取管理人选项失败:\", error);\r\n          this.$modal.msgError(\"获取管理人选项失败\");\r\n          // 失败时使用原有硬编码数据作为备选\r\n          this.managerOptions = [\r\n            { value: \"1\", label: \"王佳明\" },\r\n            { value: \"2\", label: \"李东\" },\r\n            { value: \"3\", label: \"张伟\" },\r\n          ];\r\n        });\r\n    },\r\n    /** 加载承包商选项 */\r\n    loadContractorOptions() {\r\n      // 使用承包商信息查询接口获取所有承包商数据\r\n      const params = {\r\n        pageNum: 1,\r\n        pageSize: 1000, // 获取足够多的数据\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.rows || response.data || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败:\", error);\r\n          // 失败时使用备用接口\r\n          this.loadContractorOptionsFallback();\r\n        });\r\n    },\r\n    /** 备用方法：使用另一个接口加载承包商选项 */\r\n    loadContractorOptionsFallback() {\r\n      // 使用getContractorInfo接口作为备选，参数为null获取所有承包商\r\n      getContractorInfo(null)\r\n        .then((response) => {\r\n          const data = response.data || response.rows || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败（备用接口）:\", error);\r\n          this.$modal.msgError(\"获取承包商选项失败\");\r\n          // 最终失败时使用硬编码数据作为备选\r\n          this.contractorOptions = [\r\n            { value: \"1\", label: \"智创机械集团\" },\r\n            { value: \"2\", label: \"精工电子设备制造集团\" },\r\n          ];\r\n        });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.loadAvailableContractors();\r\n      this.open = true;\r\n      this.title = \"添加承包商黑名单\";\r\n    },\r\n    /** 加载可用的承包商列表（可加入黑名单的承包商） */\r\n    loadAvailableContractors() {\r\n      // 使用新接口获取可加入黑名单的承包商\r\n      getContractorInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.availableContractorList = response.data.map((contractor) => ({\r\n              id: contractor.id,\r\n              contractorName: contractor.contractorName,\r\n              creditCode: contractor.creditCode,\r\n              contractorType: contractor.contractorType,\r\n              managerName: contractor.administratorName,\r\n              responsiblePerson: contractor.legalRepresentative,\r\n            }));\r\n          } else {\r\n            this.availableContractorList = [];\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可用承包商列表失败:\", error);\r\n          this.$modal.msgError(\"获取可用承包商列表失败\");\r\n          // 失败时使用原有接口作为备选\r\n          const params = {\r\n            pageNum: 1,\r\n            pageSize: 1000,\r\n            blacklistStatus: 1,\r\n          };\r\n          listZjContractorInfo(params)\r\n            .then((response) => {\r\n              this.availableContractorList = response.rows || [];\r\n            })\r\n            .catch(() => {\r\n              this.availableContractorList = [];\r\n            });\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      // 使用承包商信息接口获取数据\r\n      getZjContractorInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改承包商黑名单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // 修改黑名单原因\r\n            const updateData = {\r\n              id: this.form.id,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // 新增到黑名单：使用修改承包商信息接口\r\n            const updateData = {\r\n              id: this.form.contractorId,\r\n              blacklistStatus: 2,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData)\r\n              .then((response) => {\r\n                this.$modal.msgSuccess(\"已成功将承包商加入黑名单\");\r\n                this.open = false;\r\n                this.getList();\r\n              })\r\n              .catch((error) => {\r\n                console.error(\"加入黑名单失败:\", error);\r\n                this.$modal.msgError(\"加入黑名单失败，请稍后重试\");\r\n              });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除承包商黑名单编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjContractorBlaklist(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjContractorBlaklist_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n\r\n    /** 移出黑名单操作 */\r\n    handleRemoveFromBlacklist(row) {\r\n      this.$modal\r\n        .confirm('确定要将\"' + row.contractorName + '\"移出黑名单吗？')\r\n        .then(() => {\r\n          // 使用修改承包商信息接口，将blacklistStatus设为1（正常状态）\r\n          const updateData = {\r\n            id: row.id,\r\n            blacklistStatus: 1,\r\n          };\r\n          return updateZjContractorInfo(updateData);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"移出成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n\r\n    // ===== 承包商人员相关方法 =====\r\n    /** 查询承包商人员列表 */\r\n    getPersonnelList() {\r\n      this.personnelLoading = true;\r\n      listZjContractorBlaklist(this.personnelQueryParams)\r\n        .then((response) => {\r\n          this.personnelList = response.rows;\r\n          this.personnelTotal = response.total;\r\n          this.personnelLoading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据作为fallback\r\n          console.warn(\"承包商人员黑名单API调用失败，使用模拟数据\");\r\n          this.initPersonnelMockData();\r\n        });\r\n    },\r\n    /** 人员搜索按钮操作 */\r\n    handlePersonnelQuery() {\r\n      this.personnelQueryParams.pageNum = 1;\r\n      this.getPersonnelList();\r\n    },\r\n    /** 人员重置按钮操作 */\r\n    resetPersonnelQuery() {\r\n      this.resetForm(\"personnelQueryForm\");\r\n      this.handlePersonnelQuery();\r\n    },\r\n    // 人员多选框选中数据\r\n    handlePersonnelSelectionChange(selection) {\r\n      this.selectedPersonnelRows = selection;\r\n      this.personnelIds = selection.map((item) => item.id);\r\n    },\r\n    /** 添加人员到黑名单操作 */\r\n    handleAddPersonnel() {\r\n      this.initAvailablePersonnelList();\r\n      this.blacklistDialogOpen = true;\r\n      // 在下一个tick中重置表单，确保DOM已渲染\r\n      this.$nextTick(() => {\r\n        this.resetBlacklistForm();\r\n      });\r\n    },\r\n    /** 添加承包商人员操作 */\r\n    handleAddNewPersonnel() {\r\n      this.resetPersonnel();\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"添加承包商人员\";\r\n    },\r\n    /** 编辑人员操作 */\r\n    handleEditPersonnel(row) {\r\n      this.resetPersonnel();\r\n      this.personnelForm = {\r\n        id: row.id,\r\n        personnelName: row.personnelName,\r\n        personnelPhone: row.personnelPhone,\r\n        idNumber: row.idNumber,\r\n        contractorId: row.contractorId || \"1\",\r\n        personnelStatus: row.personnelStatus,\r\n      };\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"修改承包商人员\";\r\n    },\r\n    /** 删除人员操作 */\r\n    handleDeletePersonnel(row) {\r\n      this.$modal\r\n        .confirm('确定要删除人员\"' + row.personnelName + '\"吗？')\r\n        .then(() => {\r\n          return delZjContractorBlaklist(row.id);\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          if (error !== \"cancel\") {\r\n            this.$modal.msgError(\"删除失败，请稍后重试\");\r\n          }\r\n        });\r\n    },\r\n    /** 导出人员操作 */\r\n    handleExportPersonnel() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.personnelQueryParams,\r\n        },\r\n        `承包商人员黑名单_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    /** 格式化身份证号 - 中间用*号隐藏 */\r\n    formatIdNumber(idNumber) {\r\n      if (!idNumber) return \"\";\r\n      if (idNumber.length < 8) return idNumber;\r\n      return (\r\n        idNumber.substring(0, 3) +\r\n        \"***********\" +\r\n        idNumber.substring(idNumber.length - 4)\r\n      );\r\n    },\r\n\r\n    // ===== 人员弹窗相关方法 =====\r\n    /** 取消人员弹窗 */\r\n    cancelPersonnel() {\r\n      this.personnelOpen = false;\r\n      this.resetPersonnel();\r\n    },\r\n    /** 重置人员表单 */\r\n    resetPersonnel() {\r\n      this.personnelForm = {\r\n        id: null,\r\n        personnelName: null,\r\n        personnelPhone: null,\r\n        idNumber: null,\r\n        contractorId: null,\r\n        personnelStatus: \"0\",\r\n      };\r\n      this.resetForm(\"personnelForm\");\r\n    },\r\n    /** 提交人员表单 */\r\n    submitPersonnelForm() {\r\n      this.$refs[\"personnelForm\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.personnelForm.id != null) {\r\n            // 修改\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          } else {\r\n            // 新增\r\n            this.$modal.msgSuccess(\"新增成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // ===== 加入黑名单相关方法 =====\r\n    /** 初始化可选择的人员列表 */\r\n    initAvailablePersonnelList() {\r\n      // 调用API获取可拉黑的承包商人员信息\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          this.availablePersonnelList =\r\n            response.data || response.rows || response || [];\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可选择人员列表失败:\", error);\r\n          this.$modal.msgError(\"获取可选择人员列表失败\");\r\n          // 失败时使用现有人员列表作为备选\r\n          this.availablePersonnelList = this.personnelList.filter((person) => {\r\n            return person.personnelStatus === \"0\"; // 只显示在职人员\r\n          });\r\n        });\r\n    },\r\n    /** 重置黑名单表单 */\r\n    resetBlacklistForm() {\r\n      this.blacklistForm = {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      };\r\n      this.$nextTick(() => {\r\n        if (this.$refs.blacklistForm) {\r\n          this.$refs.blacklistForm.resetFields();\r\n          this.$refs.blacklistForm.clearValidate();\r\n        }\r\n      });\r\n    },\r\n    /** 取消黑名单弹窗 */\r\n    cancelBlacklist() {\r\n      this.blacklistDialogOpen = false;\r\n      this.resetBlacklistForm();\r\n    },\r\n    /** 人员选择变化处理 */\r\n    handlePersonnelSelectChange(selectedId) {\r\n      console.log(\"选择的人员ID:\", selectedId);\r\n      // 立即验证personnel字段，如果有值则清除错误\r\n      if (this.$refs.blacklistForm) {\r\n        if (selectedId) {\r\n          this.$refs.blacklistForm.clearValidate(\"personnelId\");\r\n        } else {\r\n          // 如果清空选择，立即触发验证显示错误\r\n          this.$refs.blacklistForm.validateField(\"personnelId\");\r\n        }\r\n      }\r\n    },\r\n    /** 提交黑名单表单 */\r\n    submitBlacklistForm() {\r\n      this.$refs[\"blacklistForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 直接调用添加接口，不需要确认提示\r\n          this.addToBlacklist();\r\n        } else {\r\n          this.$message.warning(\"请检查表单填写是否正确\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    /** 执行加入黑名单操作 */\r\n    addToBlacklist() {\r\n      const selectedPerson = this.availablePersonnelList.find(\r\n        (person) => person.userId === this.blacklistForm.personnelId\r\n      );\r\n\r\n      if (!selectedPerson) {\r\n        this.$modal.msgError(\"请选择要拉黑的人员\");\r\n        return;\r\n      }\r\n\r\n      const requestData = {\r\n        personnelId: this.blacklistForm.personnelId,\r\n        blacklistReason: this.blacklistForm.blacklistReason,\r\n        personnelName: selectedPerson.nickName,\r\n        status: selectedPerson.status,\r\n        personnelPhone: selectedPerson.phonenumber,\r\n        idNumber: selectedPerson.idNumber,\r\n        blacklistState: \"2\",\r\n      };\r\n\r\n      // 调用API将人员加入黑名单\r\n      addZjContractorBlaklist(requestData)\r\n        .then((response) => {\r\n          this.$modal.msgSuccess(\r\n            `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n          );\r\n          this.blacklistDialogOpen = false;\r\n          this.resetBlacklistForm();\r\n          // 刷新人员列表\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          // 如果API调用失败，使用模拟数据响应\r\n          console.warn(\"API调用失败，使用模拟响应:\", error);\r\n          setTimeout(() => {\r\n            this.$modal.msgSuccess(\r\n              `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n            );\r\n            this.blacklistDialogOpen = false;\r\n            this.resetBlacklistForm();\r\n            // 刷新人员列表\r\n            this.getPersonnelList();\r\n          }, 500);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 标签页样式 */\r\n.contractor-tabs {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__header {\r\n  margin: 0 0 15px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item.is-active {\r\n  color: #409eff;\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form-content {\r\n  margin: 0;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item {\r\n  margin-bottom: 0;\r\n  margin-right: 32px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item__label {\r\n  color: #606266;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-select .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n}\r\n\r\n.search-form-content ::v-deep .el-button {\r\n  padding: 8px 24px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 黑名单容器 */\r\n.blacklist-container,\r\n.personnel-container {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n/* 黑名单标题和操作区域 */\r\n.blacklist-header,\r\n.personnel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.blacklist-title,\r\n.personnel-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.record-count {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  font-weight: 400;\r\n}\r\n\r\n.blacklist-actions,\r\n.personnel-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.blacklist-actions .el-button,\r\n.personnel-actions .el-button {\r\n  padding: 6px 16px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格样式 */\r\n.blacklist-container .el-table,\r\n.personnel-container .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header th {\r\n  background-color: #fafafa !important;\r\n  color: #595959;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  height: 48px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body td {\r\n  font-size: 14px;\r\n  color: #262626;\r\n  height: 56px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body tr:hover > td {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-active {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-inactive {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.el-table ::v-deep .el-button--text {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-table ::v-deep .el-button--text:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination {\r\n  text-align: center;\r\n  margin-top: 24px;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination {\r\n  text-align: center;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination__total {\r\n  color: #595959;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 承包商黑名单弹窗样式 */\r\n.contractor-blacklist-dialog ::v-deep .el-dialog {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__header {\r\n  background-color: #f8f9fa;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  margin: 0;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert {\r\n  border-radius: 6px;\r\n  border: 1px solid #fadb14;\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert__icon {\r\n  color: #fa8c16;\r\n  font-size: 16px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner:focus,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner:focus {\r\n  border-color: #1890ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.contractor-blacklist-dialog\r\n  ::v-deep\r\n  .el-select\r\n  .el-input.is-focus\r\n  .el-input__inner {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-textarea .el-input__count {\r\n  background: transparent;\r\n  color: #8c8c8c;\r\n  font-size: 12px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer {\r\n  text-align: right;\r\n  padding: 16px 24px;\r\n  background-color: #fafafa;\r\n  border-top: 1px solid #e9ecef;\r\n  margin: 0 -24px -24px -24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button {\r\n  margin-left: 8px;\r\n  padding: 8px 24px;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary:hover {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default {\r\n  border-color: #d9d9d9;\r\n  color: #595959;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default:hover {\r\n  border-color: #40a9ff;\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-content {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form-content ::v-deep .el-form-item {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .blacklist-container {\r\n    padding: 15px;\r\n  }\r\n\r\n  .blacklist-header,\r\n  .personnel-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .blacklist-actions,\r\n  .personnel-actions {\r\n    width: 100%;\r\n    justify-content: flex-end;\r\n  }\r\n\r\n  .contractor-blacklist-dialog ::v-deep .el-dialog {\r\n    width: 90% !important;\r\n    margin: 0 auto !important;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAkeA,IAAAA,qBAAA,GAAAC,OAAA;AAQA,IAAAC,iBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAOA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,YAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,wBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,iBAAA;MACA;MACAC,uBAAA;MACA;MACAC,cAAA;MACA;MACAC,gBAAA;MACAC,qBAAA;MACAC,YAAA;MACAC,cAAA;MACAC,aAAA;MACAC,oBAAA;QACAC,OAAA;QACAC,QAAA;QACAC,cAAA;QACAC,eAAA;MACA;MACA;MACAC,WAAA;QACAJ,OAAA;QACAC,QAAA;QACAC,cAAA;QACAG,eAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,YAAA,GACA;UACAC,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,eAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAE,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAG,GAAA;UAAAJ,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAI,aAAA;MACAC,cAAA;MACAC,aAAA;MACAC,cAAA;QACAC,aAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,cAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAU,QAAA,GACA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAH,YAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAW,mBAAA;MACAC,aAAA;QACAC,WAAA;QACAZ,eAAA;MACA;MACAa,cAAA;QACAD,WAAA,GACA;UACAf,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,eAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,GACA;UACAE,GAAA;UACAH,OAAA;UACAC,OAAA;QACA,GACA;UACAG,GAAA;UACAJ,OAAA;UACAC,OAAA;QACA;MAEA;MACA;MACAe,sBAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAA9C,GAAA,CAAA+C,MAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA,YAAAlC,YAAA,CAAAiC,MAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,gBAAA;IACA,KAAAC,OAAA;IACA;IACA,KAAAC,kBAAA;IACA;IACA,KAAAC,qBAAA;EACA;EACAC,OAAA;IACA,mBACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAvC,aAAA,IACA;QACAwC,EAAA;QACAnB,aAAA;QACAhB,eAAA;QACAiB,cAAA;QACAC,QAAA;QACAnB,cAAA;MACA,GACA;QACAoC,EAAA;QACAnB,aAAA;QACAhB,eAAA;QACAiB,cAAA;QACAC,QAAA;QACAnB,cAAA;MACA,EACA;MACA,KAAAL,cAAA;MACA,KAAAH,gBAAA;IACA;IACA,oBACA6C,YAAA,WAAAA,aAAA;MACA,KAAAnD,wBAAA,IACA;QACAkD,EAAA;QACApC,cAAA;QACAsC,UAAA;QACAC,cAAA;QACAC,WAAA;QACAC,iBAAA;QACA/B,eAAA;MACA,EACA;MACA,KAAAzB,KAAA;MACA,KAAAN,OAAA;IACA;IACA,iBACAoD,OAAA,WAAAA,QAAA;MAAA,IAAAW,KAAA;MACA,KAAA/D,OAAA;MACA;MACA,IAAAgE,MAAA,OAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAA3C,WAAA;QACA4C,eAAA;MAAA,EACA;MACA,IAAAC,sCAAA,EAAAJ,MAAA,EACAK,IAAA,WAAAC,QAAA;QACAP,KAAA,CAAAxD,wBAAA,GAAA+D,QAAA,CAAAC,IAAA;QACAR,KAAA,CAAAzD,KAAA,GAAAgE,QAAA,CAAAhE,KAAA;QACAyD,KAAA,CAAA/D,OAAA;MACA,GACAwE,KAAA;QACA;QACAT,KAAA,CAAAL,YAAA;MACA;IACA;IACA;IACAe,MAAA,WAAAA,OAAA;MACA,KAAAhE,IAAA;MACA,KAAAiE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAjD,IAAA;QACAgC,EAAA;QACA9B,YAAA;QACAN,cAAA;QACAsC,UAAA;QACAC,cAAA;QACAC,WAAA;QACAC,iBAAA;QACA/B,eAAA;MACA;MACA,KAAA4C,SAAA;IACA;IACA,gBACAC,4BAAA,WAAAA,6BAAAjD,YAAA;MACA,IAAAA,YAAA;QACA;QACA,IAAAkD,kBAAA,QAAAlE,uBAAA,CAAAmE,IAAA,CACA,UAAAC,UAAA;UAAA,OAAAA,UAAA,CAAAtB,EAAA,KAAA9B,YAAA;QAAA,CACA;QACA,IAAAkD,kBAAA;UACA,KAAApD,IAAA,CAAAJ,cAAA,GAAAwD,kBAAA,CAAAxD,cAAA;UACA,KAAAI,IAAA,CAAAkC,UAAA,GAAAkB,kBAAA,CAAAlB,UAAA;UACA,KAAAlC,IAAA,CAAAmC,cAAA,GAAAiB,kBAAA,CAAAjB,cAAA;UACA,KAAAnC,IAAA,CAAAoC,WAAA,GAAAgB,kBAAA,CAAAhB,WAAA;UACA,KAAApC,IAAA,CAAAqC,iBAAA,GAAAe,kBAAA,CAAAf,iBAAA;QACA;MACA;QACA;QACA,KAAArC,IAAA,CAAAJ,cAAA;QACA,KAAAI,IAAA,CAAAkC,UAAA;QACA,KAAAlC,IAAA,CAAAmC,cAAA;QACA,KAAAnC,IAAA,CAAAoC,WAAA;QACA,KAAApC,IAAA,CAAAqC,iBAAA;MACA;IACA;IACA,aACAkB,WAAA,WAAAA,YAAA;MACA,KAAAzD,WAAA,CAAAJ,OAAA;MACA,KAAAiC,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAN,SAAA;MACA,KAAAK,WAAA;IACA;IACA,cACA3B,kBAAA,WAAAA,mBAAA;MAAA,IAAA6B,MAAA;MACA;MACA,IAAAC,iCAAA,KACAd,IAAA,WAAAC,QAAA;QACA;QACA,IAAAxE,IAAA,GAAAwE,QAAA,CAAAxE,IAAA,IAAAwE,QAAA,CAAAC,IAAA,IAAAD,QAAA;QACAY,MAAA,CAAAtE,cAAA,GAAAd,IAAA,CAAAsF,GAAA,WAAAC,OAAA;UAAA;YACAC,KAAA,EAAAD,OAAA,CAAAE,MAAA,IAAAF,OAAA,CAAA5B,EAAA;YACA+B,KAAA,EAAAH,OAAA,CAAAI,QAAA,IAAAJ,OAAA,CAAAzF,IAAA,IAAAyF,OAAA,CAAAK;UACA;QAAA;MACA,GACAlB,KAAA,WAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACAT,MAAA,CAAAW,MAAA,CAAAC,QAAA;QACA;QACAZ,MAAA,CAAAtE,cAAA,IACA;UAAA0E,KAAA;UAAAE,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAE,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAE,KAAA;QAAA,EACA;MACA;IACA;IACA,cACAlC,qBAAA,WAAAA,sBAAA;MAAA,IAAAyC,MAAA;MACA;MACA,IAAA/B,MAAA;QACA7C,OAAA;QACAC,QAAA;MACA;MACA,IAAAgD,sCAAA,EAAAJ,MAAA,EACAK,IAAA,WAAAC,QAAA;QACA;QACA,IAAAxE,IAAA,GAAAwE,QAAA,CAAAC,IAAA,IAAAD,QAAA,CAAAxE,IAAA,IAAAwE,QAAA;QACAyB,MAAA,CAAArF,iBAAA,GAAAZ,IAAA,CAAAsF,GAAA,WAAAL,UAAA;UAAA;YACAO,KAAA,EAAAP,UAAA,CAAAtB,EAAA;YACA+B,KAAA,EAAAT,UAAA,CAAA1D;UACA;QAAA;MACA,GACAmD,KAAA,WAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,eAAAA,KAAA;QACA;QACAI,MAAA,CAAAC,6BAAA;MACA;IACA;IACA,0BACAA,6BAAA,WAAAA,8BAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,mCAAA,QACA7B,IAAA,WAAAC,QAAA;QACA,IAAAxE,IAAA,GAAAwE,QAAA,CAAAxE,IAAA,IAAAwE,QAAA,CAAAC,IAAA,IAAAD,QAAA;QACA2B,MAAA,CAAAvF,iBAAA,GAAAZ,IAAA,CAAAsF,GAAA,WAAAL,UAAA;UAAA;YACAO,KAAA,EAAAP,UAAA,CAAAtB,EAAA;YACA+B,KAAA,EAAAT,UAAA,CAAA1D;UACA;QAAA;MACA,GACAmD,KAAA,WAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,qBAAAA,KAAA;QACAM,MAAA,CAAAJ,MAAA,CAAAC,QAAA;QACA;QACAG,MAAA,CAAAvF,iBAAA,IACA;UAAA4E,KAAA;UAAAE,KAAA;QAAA,GACA;UAAAF,KAAA;UAAAE,KAAA;QAAA,EACA;MACA;IACA;IACA;IACAW,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAlG,YAAA,GAAAkG,SAAA;MACA,KAAAnG,GAAA,GAAAmG,SAAA,CAAAhB,GAAA,WAAAiB,IAAA;QAAA,OAAAA,IAAA,CAAA5C,EAAA;MAAA;MACA,KAAAtD,MAAA,GAAAiG,SAAA,CAAApD,MAAA;MACA,KAAA5C,QAAA,IAAAgG,SAAA,CAAApD,MAAA;IACA;IACA,aACAsD,SAAA,WAAAA,UAAA;MACA,KAAA5B,KAAA;MACA,KAAA6B,wBAAA;MACA,KAAA9F,IAAA;MACA,KAAAD,KAAA;IACA;IACA,6BACA+F,wBAAA,WAAAA,yBAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAN,mCAAA,KACA7B,IAAA,WAAAC,QAAA;QACA;QACA,IAAAA,QAAA,CAAAxE,IAAA,IAAA2G,KAAA,CAAAC,OAAA,CAAApC,QAAA,CAAAxE,IAAA;UACA0G,MAAA,CAAA7F,uBAAA,GAAA2D,QAAA,CAAAxE,IAAA,CAAAsF,GAAA,WAAAL,UAAA;YAAA;cACAtB,EAAA,EAAAsB,UAAA,CAAAtB,EAAA;cACApC,cAAA,EAAA0D,UAAA,CAAA1D,cAAA;cACAsC,UAAA,EAAAoB,UAAA,CAAApB,UAAA;cACAC,cAAA,EAAAmB,UAAA,CAAAnB,cAAA;cACAC,WAAA,EAAAkB,UAAA,CAAA4B,iBAAA;cACA7C,iBAAA,EAAAiB,UAAA,CAAA6B;YACA;UAAA;QACA;UACAJ,MAAA,CAAA7F,uBAAA;QACA;MACA,GACA6D,KAAA,WAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;QACAa,MAAA,CAAAX,MAAA,CAAAC,QAAA;QACA;QACA,IAAA9B,MAAA;UACA7C,OAAA;UACAC,QAAA;UACA+C,eAAA;QACA;QACA,IAAAC,sCAAA,EAAAJ,MAAA,EACAK,IAAA,WAAAC,QAAA;UACAkC,MAAA,CAAA7F,uBAAA,GAAA2D,QAAA,CAAAC,IAAA;QACA,GACAC,KAAA;UACAgC,MAAA,CAAA7F,uBAAA;QACA;MACA;IACA;IACA,aACAkG,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAArC,KAAA;MACA,IAAAjB,EAAA,GAAAqD,GAAA,CAAArD,EAAA,SAAAxD,GAAA;MACA;MACA,IAAA+G,qCAAA,EAAAvD,EAAA,EAAAY,IAAA,WAAAC,QAAA;QACAyC,MAAA,CAAAtF,IAAA,GAAA6C,QAAA,CAAAxE,IAAA;QACAiH,MAAA,CAAAtG,IAAA;QACAsG,MAAA,CAAAvG,KAAA;MACA;IACA;IACA,WACAyG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAzF,IAAA,CAAAgC,EAAA;YACA;YACA,IAAA6D,UAAA;cACA7D,EAAA,EAAAyD,MAAA,CAAAzF,IAAA,CAAAgC,EAAA;cACA1B,eAAA,EAAAmF,MAAA,CAAAzF,IAAA,CAAAM;YACA;YACA,IAAAwF,wCAAA,EAAAD,UAAA,EAAAjD,IAAA,WAAAC,QAAA;cACA4C,MAAA,CAAArB,MAAA,CAAA2B,UAAA;cACAN,MAAA,CAAAzG,IAAA;cACAyG,MAAA,CAAA9D,OAAA;YACA;UACA;YACA;YACA,IAAAkE,WAAA;cACA7D,EAAA,EAAAyD,MAAA,CAAAzF,IAAA,CAAAE,YAAA;cACAwC,eAAA;cACApC,eAAA,EAAAmF,MAAA,CAAAzF,IAAA,CAAAM;YACA;YACA,IAAAwF,wCAAA,EAAAD,WAAA,EACAjD,IAAA,WAAAC,QAAA;cACA4C,MAAA,CAAArB,MAAA,CAAA2B,UAAA;cACAN,MAAA,CAAAzG,IAAA;cACAyG,MAAA,CAAA9D,OAAA;YACA,GACAoB,KAAA,WAAAmB,KAAA;cACAC,OAAA,CAAAD,KAAA,aAAAA,KAAA;cACAuB,MAAA,CAAArB,MAAA,CAAAC,QAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2B,YAAA,WAAAA,aAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,IAAAzH,GAAA,GAAA6G,GAAA,CAAArD,EAAA,SAAAxD,GAAA;MACA,KAAA4F,MAAA,CACA8B,OAAA,sBAAA1H,GAAA,aACAoE,IAAA;QACA,WAAAuD,6CAAA,EAAA3H,GAAA;MACA,GACAoE,IAAA;QACAqD,MAAA,CAAAtE,OAAA;QACAsE,MAAA,CAAA7B,MAAA,CAAA2B,UAAA;MACA,GACAhD,KAAA;IACA;IACA,aACAqD,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,8CAAA7D,cAAA,CAAAC,OAAA,MAEA,KAAA3C,WAAA,2BAAAwG,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;IAEA,cACAC,yBAAA,WAAAA,0BAAApB,GAAA;MAAA,IAAAqB,MAAA;MACA,KAAAtC,MAAA,CACA8B,OAAA,WAAAb,GAAA,CAAAzF,cAAA,eACAgD,IAAA;QACA;QACA,IAAAiD,UAAA;UACA7D,EAAA,EAAAqD,GAAA,CAAArD,EAAA;UACAU,eAAA;QACA;QACA,WAAAoD,wCAAA,EAAAD,UAAA;MACA,GACAjD,IAAA;QACA8D,MAAA,CAAA/E,OAAA;QACA+E,MAAA,CAAAtC,MAAA,CAAA2B,UAAA;MACA,GACAhD,KAAA;IACA;IAEA;IACA;IACArB,gBAAA,WAAAA,iBAAA;MAAA,IAAAiF,MAAA;MACA,KAAAvH,gBAAA;MACA,IAAAwH,8CAAA,OAAAnH,oBAAA,EACAmD,IAAA,WAAAC,QAAA;QACA8D,MAAA,CAAAnH,aAAA,GAAAqD,QAAA,CAAAC,IAAA;QACA6D,MAAA,CAAApH,cAAA,GAAAsD,QAAA,CAAAhE,KAAA;QACA8H,MAAA,CAAAvH,gBAAA;MACA,GACA2D,KAAA;QACA;QACAoB,OAAA,CAAA0C,IAAA;QACAF,MAAA,CAAA5E,qBAAA;MACA;IACA;IACA,eACA+E,oBAAA,WAAAA,qBAAA;MACA,KAAArH,oBAAA,CAAAC,OAAA;MACA,KAAAgC,gBAAA;IACA;IACA,eACAqF,mBAAA,WAAAA,oBAAA;MACA,KAAA7D,SAAA;MACA,KAAA4D,oBAAA;IACA;IACA;IACAE,8BAAA,WAAAA,+BAAArC,SAAA;MACA,KAAAtF,qBAAA,GAAAsF,SAAA;MACA,KAAArF,YAAA,GAAAqF,SAAA,CAAAhB,GAAA,WAAAiB,IAAA;QAAA,OAAAA,IAAA,CAAA5C,EAAA;MAAA;IACA;IACA,iBACAiF,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,0BAAA;MACA,KAAAnG,mBAAA;MACA;MACA,KAAAoG,SAAA;QACAF,MAAA,CAAAG,kBAAA;MACA;IACA;IACA,gBACAC,qBAAA,WAAAA,sBAAA;MACA,KAAAC,cAAA;MACA,KAAA9G,aAAA;MACA,KAAAC,cAAA;IACA;IACA,aACA8G,mBAAA,WAAAA,oBAAAnC,GAAA;MACA,KAAAkC,cAAA;MACA,KAAA5G,aAAA;QACAqB,EAAA,EAAAqD,GAAA,CAAArD,EAAA;QACAnB,aAAA,EAAAwE,GAAA,CAAAxE,aAAA;QACAC,cAAA,EAAAuE,GAAA,CAAAvE,cAAA;QACAC,QAAA,EAAAsE,GAAA,CAAAtE,QAAA;QACAb,YAAA,EAAAmF,GAAA,CAAAnF,YAAA;QACAL,eAAA,EAAAwF,GAAA,CAAAxF;MACA;MACA,KAAAY,aAAA;MACA,KAAAC,cAAA;IACA;IACA,aACA+G,qBAAA,WAAAA,sBAAApC,GAAA;MAAA,IAAAqC,OAAA;MACA,KAAAtD,MAAA,CACA8B,OAAA,cAAAb,GAAA,CAAAxE,aAAA,UACA+B,IAAA;QACA,WAAAuD,6CAAA,EAAAd,GAAA,CAAArD,EAAA;MACA,GACAY,IAAA;QACA8E,OAAA,CAAAtD,MAAA,CAAA2B,UAAA;QACA2B,OAAA,CAAAhG,gBAAA;MACA,GACAqB,KAAA,WAAAmB,KAAA;QACA,IAAAA,KAAA;UACAwD,OAAA,CAAAtD,MAAA,CAAAC,QAAA;QACA;MACA;IACA;IACA,aACAsD,qBAAA,WAAAA,sBAAA;MACA,KAAAtB,QAAA,CACA,8CAAA7D,cAAA,CAAAC,OAAA,MAEA,KAAAhD,oBAAA,uDAAA6G,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;IACA,wBACAoB,cAAA,WAAAA,eAAA7G,QAAA;MACA,KAAAA,QAAA;MACA,IAAAA,QAAA,CAAAQ,MAAA,aAAAR,QAAA;MACA,OACAA,QAAA,CAAA8G,SAAA,SACA,gBACA9G,QAAA,CAAA8G,SAAA,CAAA9G,QAAA,CAAAQ,MAAA;IAEA;IAEA;IACA;IACAuG,eAAA,WAAAA,gBAAA;MACA,KAAArH,aAAA;MACA,KAAA8G,cAAA;IACA;IACA,aACAA,cAAA,WAAAA,eAAA;MACA,KAAA5G,aAAA;QACAqB,EAAA;QACAnB,aAAA;QACAC,cAAA;QACAC,QAAA;QACAb,YAAA;QACAL,eAAA;MACA;MACA,KAAAqD,SAAA;IACA;IACA,aACA6E,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAtC,KAAA,kBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAoC,OAAA,CAAArH,aAAA,CAAAqB,EAAA;YACA;YACAgG,OAAA,CAAA5D,MAAA,CAAA2B,UAAA;YACAiC,OAAA,CAAAvH,aAAA;YACAuH,OAAA,CAAAtG,gBAAA;UACA;YACA;YACAsG,OAAA,CAAA5D,MAAA,CAAA2B,UAAA;YACAiC,OAAA,CAAAvH,aAAA;YACAuH,OAAA,CAAAtG,gBAAA;UACA;QACA;MACA;IACA;IAEA;IACA;IACAyF,0BAAA,WAAAA,2BAAA;MAAA,IAAAc,OAAA;MACA;MACA,IAAAvE,iCAAA,KACAd,IAAA,WAAAC,QAAA;QACAoF,OAAA,CAAA7G,sBAAA,GACAyB,QAAA,CAAAxE,IAAA,IAAAwE,QAAA,CAAAC,IAAA,IAAAD,QAAA;MACA,GACAE,KAAA,WAAAmB,KAAA;QACAC,OAAA,CAAAD,KAAA,iBAAAA,KAAA;QACA+D,OAAA,CAAA7D,MAAA,CAAAC,QAAA;QACA;QACA4D,OAAA,CAAA7G,sBAAA,GAAA6G,OAAA,CAAAzI,aAAA,CAAA0I,MAAA,WAAAC,MAAA;UACA,OAAAA,MAAA,CAAAtI,eAAA;QACA;MACA;IACA;IACA,cACAwH,kBAAA,WAAAA,mBAAA;MAAA,IAAAe,OAAA;MACA,KAAAnH,aAAA;QACAC,WAAA;QACAZ,eAAA;MACA;MACA,KAAA8G,SAAA;QACA,IAAAgB,OAAA,CAAA1C,KAAA,CAAAzE,aAAA;UACAmH,OAAA,CAAA1C,KAAA,CAAAzE,aAAA,CAAAoH,WAAA;UACAD,OAAA,CAAA1C,KAAA,CAAAzE,aAAA,CAAAqH,aAAA;QACA;MACA;IACA;IACA,cACAC,eAAA,WAAAA,gBAAA;MACA,KAAAvH,mBAAA;MACA,KAAAqG,kBAAA;IACA;IACA,eACAmB,2BAAA,WAAAA,4BAAAC,UAAA;MACAtE,OAAA,CAAAuE,GAAA,aAAAD,UAAA;MACA;MACA,SAAA/C,KAAA,CAAAzE,aAAA;QACA,IAAAwH,UAAA;UACA,KAAA/C,KAAA,CAAAzE,aAAA,CAAAqH,aAAA;QACA;UACA;UACA,KAAA5C,KAAA,CAAAzE,aAAA,CAAA0H,aAAA;QACA;MACA;IACA;IACA,cACAC,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,OAAA;MACA,KAAAnD,KAAA,kBAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACAiD,OAAA,CAAAC,cAAA;QACA;UACAD,OAAA,CAAAE,QAAA,CAAAC,OAAA;UACA;QACA;MACA;IACA;IACA,gBACAF,cAAA,WAAAA,eAAA;MAAA,IAAAG,OAAA;MACA,IAAAC,cAAA,QAAA9H,sBAAA,CAAAiC,IAAA,CACA,UAAA8E,MAAA;QAAA,OAAAA,MAAA,CAAArE,MAAA,KAAAmF,OAAA,CAAAhI,aAAA,CAAAC,WAAA;MAAA,CACA;MAEA,KAAAgI,cAAA;QACA,KAAA9E,MAAA,CAAAC,QAAA;QACA;MACA;MAEA,IAAA8E,WAAA;QACAjI,WAAA,OAAAD,aAAA,CAAAC,WAAA;QACAZ,eAAA,OAAAW,aAAA,CAAAX,eAAA;QACAO,aAAA,EAAAqI,cAAA,CAAAlF,QAAA;QACAoF,MAAA,EAAAF,cAAA,CAAAE,MAAA;QACAtI,cAAA,EAAAoI,cAAA,CAAAG,WAAA;QACAtI,QAAA,EAAAmI,cAAA,CAAAnI,QAAA;QACAuI,cAAA;MACA;;MAEA;MACA,IAAAC,6CAAA,EAAAJ,WAAA,EACAvG,IAAA,WAAAC,QAAA;QACAoG,OAAA,CAAA7E,MAAA,CAAA2B,UAAA,6BAAAO,MAAA,CACA4C,cAAA,CAAAlF,QAAA,oCACA;QACAiF,OAAA,CAAAjI,mBAAA;QACAiI,OAAA,CAAA5B,kBAAA;QACA;QACA4B,OAAA,CAAAvH,gBAAA;MACA,GACAqB,KAAA,WAAAmB,KAAA;QACA;QACAC,OAAA,CAAA0C,IAAA,oBAAA3C,KAAA;QACAsF,UAAA;UACAP,OAAA,CAAA7E,MAAA,CAAA2B,UAAA,6BAAAO,MAAA,CACA4C,cAAA,CAAAlF,QAAA,oCACA;UACAiF,OAAA,CAAAjI,mBAAA;UACAiI,OAAA,CAAA5B,kBAAA;UACA;UACA4B,OAAA,CAAAvH,gBAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}