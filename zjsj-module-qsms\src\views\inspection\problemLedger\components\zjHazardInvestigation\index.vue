<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- 检查结果 -->
      <el-form-item label="检查结果" prop="statusDesc">
        <!-- <el-input v-model="queryParams.statusDesc" placeholder="请输入检查结果" clearable @keyup.enter.native="handleQuery" /> -->
        <el-select
          v-model="queryParams.statusDesc"
          placeholder="请选择检查结果"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          />
        </el-select>
      </el-form-item>
      <!-- 项目名称 -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 检查人 -->
      <el-form-item label="检查人" prop="creatorName">
        <el-input
          v-model="queryParams.creatorName"
          placeholder="请输入检查人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 隐患信息 -->
      <!--
      <el-form-item label="隐患信息" prop="dangerItemContent">
        <el-input
          v-model="queryParams.dangerItemContent"
          placeholder="请输入隐患信息"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 分包单位 -->
      <!-- <el-form-item label="分包单位" prop="vendorName">
        <el-input
          v-model="queryParams.vendorName"
          placeholder="请输入分包单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 整改人 -->
      <!-- <el-form-item label="整改人" prop="changeName">
        <el-input
          v-model="queryParams.changeName"
          placeholder="请输入整改人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 复查人 -->
      <!-- <el-form-item label="复查人" prop="reviewName">
        <el-input
          v-model="queryParams.reviewName"
          placeholder="请输入复查人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 检查类型 -->
      <!-- <el-form-item label="检查类型" prop="checkType">
        <el-input
          v-model="queryParams.checkType"
          placeholder="请输入检查类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjHazardInvestigation:add']"
          >新增</el-button
        >
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjHazardInvestigation:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjHazardInvestigation:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjHazardInvestigation:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjHazardInvestigationList"
      height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="left" />
      <el-table-column type="" align="left" label="序号" width="55">
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="检查结果/截至时限"
        align="left"
        prop="statusDesc"
        width="160"
        class-name="bg-highlight-column"
      >
        <template slot-scope="{ row }">
          <div class="font-12">
            <!-- 1.待核查 2 已整改 3 已合格 -->
            <span class="circle" :class="getStatusClass(row.status)" />{{
              row.statusDesc
            }}
          </div>
          <div class="font-12">
            复查时限:{{
              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : ""
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="责任区域/项目名称"
        prop="projectName"
        width="250"
        show-overflow-tooltip
        class-name="bg-highlight-column"
      />
      <el-table-column
        align="left"
        label="检查人"
        prop="creatorName"
        class-name="bg-highlight-column"
      />
      <el-table-column
        align="left"
        label="检查时间"
        prop="createTime"
        width="140"
        class-name="bg-highlight-column"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.createTime ? scope.row.createTime.slice(0, 16) : "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="隐患信息"
        prop="dangerItemContent"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; gap: 8px">
            <el-tag
              v-if="scope.row.accidentHazard === '1'"
              class="hazard-tag hazard-general"
              size="small"
            >
              一般
            </el-tag>
            <el-tag
              v-else-if="scope.row.accidentHazard === '2'"
              class="hazard-tag hazard-common"
              size="small"
            >
              常见
            </el-tag>
            <span>{{ scope.row.dangerItemContent }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        align="left"
        label="分包单位"
        prop="vendorName"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="scope.row.vendorName">
            {{ scope.row.vendorName }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="整改状态"
        prop="status"
        width="100"
        class-name="bg-highlight-column"
      >
        <template slot-scope="scope">
          <div class="font-12">
            <span class="circle" :class="getStatusClass(scope.row.status)" />
            {{ getRectificationStatusText(scope.row.status) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="整改时间"
        prop="changeTime"
        width="140"
        class-name="bg-highlight-column"
      >
        <template slot-scope="scope">
          <span>{{
            scope.row.changeTime ? scope.row.changeTime.slice(0, 16) : "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="left"
        label="整改人"
        prop="changeName"
        class-name="bg-highlight-column"
      />
      <el-table-column align="left" label="复查人" prop="reviewName" />
      <!-- 领导带班检查 日常检查 第三方巡检 -->
      <el-table-column align="left" label="检查类型" prop="" />

      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <el-button
            size="mini"
            type="text"
            disabled
            @click="handlePrint(scope.row)"
            >打印</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 详情弹窗 -->
    <el-dialog
      title="查看详情"
      :visible.sync="detailDialog"
      width="1000px"
      append-to-body
      custom-class="detail-dialog"
    >
      <div v-if="detailData" class="detail-content">
        <!-- 隐患记录部分 -->
        <div class="independent-section">
          <h3 class="independent-title">隐患记录</h3>
          <!-- 左右两列布局 -->
          <div class="record-columns">
            <!-- 左列 -->
            <div class="left-column">
              <div class="field-row highlighted-field">
                <span class="field-label">检查结果:</span>
                <span
                  class="field-value status-tag"
                  :class="getDetailStatusClass(detailData.status)"
                >
                  {{ getStatusText(detailData.status) }}
                </span>
              </div>

              <div class="field-row highlighted-field">
                <span class="field-label">检查人:</span>
                <span class="field-value">{{
                  detailData.creatorName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">项目名称:</span>
                <span class="field-value">{{
                  detailData.projectName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">隐患明细:</span>
                <span class="field-value">{{
                  detailData.dangerItemContent ||
                  "未按规定正确着装或使用劳动防护用品"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">监管类型:</span>
                <span class="field-value">{{
                  detailData.supervisionType || "日常检查"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">分包单位:</span>
                <span class="field-value">{{
                  detailData.vendorName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">审核人:</span>
                <span class="field-value">{{
                  detailData.auditorName || detailData.reviewName || "-"
                }}</span>
              </div>
            </div>

            <!-- 右列 -->
            <div class="right-column">
              <div class="field-row highlighted-field">
                <span class="field-label">责任区域:</span>
                <span class="field-value">{{
                  detailData.regionName || "B区"
                }}</span>
              </div>

              <div class="field-row highlighted-field">
                <span class="field-label">检查时间:</span>
                <span
                  class="field-value"
                  >{{ detailData.createTime ? detailData.createTime.slice(0, 16) : '2025-08-27
                  18: 24'
                  }}</span
                >
              </div>

              <div class="field-row">
                <span class="field-label">隐患类别:</span>
                <span class="field-value">{{
                  detailData.dangerTypeName || "起重吊装/警戒防护"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">社区说明:</span>
                <span class="field-value">{{
                  detailData.communityDesc || "B6#北塔水池四期未设置警戒线"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">隐患级别:</span>
                <span class="field-value">{{
                  detailData.level || "四级"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">事故隐患:</span>
                <span class="field-value">{{
                  detailData.accidentHazard || ""
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">通知人:</span>
                <span class="field-value">{{
                  detailData.notifierName || "王希远"
                }}</span>
              </div>
            </div>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关照片:</span>
            </div>
            <div v-if="detailData.hiddenDangerPictures" class="photo-container">
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.hiddenDangerPictures
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="隐患照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 整改记录部分 -->
        <div v-if="detailData.status !== 1" class="independent-section">
          <h3 class="independent-title">整改记录</h3>
          <!-- 整改信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">整改状态:</span>
              <span class="field-value">{{
                detailData.rectificationStatus || "已整改"
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改人:</span>
              <span class="field-value">{{
                detailData.changeName || "王希远"
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改时间:</span>
              <span class="field-value">{{
                detailData.changeTime
                  ? detailData.changeTime.slice(0, 16)
                  : "2025-08-27 18: 26"
              }}</span>
            </div>
          </div>

          <!-- 说明 -->
          <div class="field-row full-width">
            <span class="field-label">说明:</span>
            <span class="field-value">{{
              detailData.rectificationDesc || "已整改"
            }}</span>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关照片:</span>
            </div>
            <div
              v-if="detailData.rectificationPictures"
              class="photo-container"
            >
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.rectificationPictures
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="整改照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 复查记录部分 -->
        <div
          v-if="detailData.status !== 1 && detailData.status !== 2"
          class="independent-section"
        >
          <h3 class="independent-title">复查记录</h3>
          <!-- 复查信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">复查状态:</span>
              <span class="field-value">{{
                getReviewStatusText(detailData.status)
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查人:</span>
              <span class="field-value">{{
                detailData.reviewName || "-"
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查时间:</span>
              <span class="field-value">{{
                detailData.reviewTime
                  ? detailData.reviewTime.slice(0, 16)
                  : detailData.againReviewTime
                  ? detailData.againReviewTime.slice(0, 16)
                  : "-"
              }}</span>
            </div>
          </div>

          <!-- 复查意见 -->
          <div class="field-row full-width">
            <span class="field-label">复查意见:</span>
            <span class="field-value">{{
              detailData.reviewComments || "-"
            }}</span>
          </div>

          <!-- 再次整改说明 -->
          <div
            v-if="detailData.againRectificationDesc"
            class="field-row full-width"
          >
            <span class="field-label">再次整改说明:</span>
            <span class="field-value">{{
              detailData.againRectificationDesc
            }}</span>
          </div>

          <!-- 复查照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">复查照片:</span>
            </div>
            <div v-if="detailData.reviewPicUrl" class="photo-container">
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.reviewPicUrl
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="复查照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>

          <!-- 再次整改照片 -->
          <div
            v-if="detailData.againRectificationPictures"
            class="photo-section"
          >
            <div class="field-row">
              <span class="field-label">再次整改照片:</span>
            </div>
            <div class="photo-container">
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.againRectificationPictures
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="再次整改照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加或修改隐患排查治理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="租户ID" prop="tenantId">
          <el-input v-model="form.tenantId" placeholder="请输入租户ID" />
        </el-form-item>
        <el-form-item label="项目ID" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目ID" />
        </el-form-item>
        <el-form-item label="创建人姓名" prop="creatorName">
          <el-input v-model="form.creatorName" placeholder="请输入创建人姓名" />
        </el-form-item>
        <el-form-item label="创建人ID " prop="creatorId">
          <el-input v-model="form.creatorId" placeholder="请输入创建人ID " />
        </el-form-item>
        <el-form-item label="创建人工号" prop="creatorWorkNum">
          <el-input
            v-model="form.creatorWorkNum"
            placeholder="请输入创建人工号"
          />
        </el-form-item>
        <el-form-item label="整改说明" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入整改说明" />
        </el-form-item>
        <el-form-item label="项目编号" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编号" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="离线记录ID" prop="recordId">
          <el-input v-model="form.recordId" placeholder="请输入离线记录ID" />
        </el-form-item>
        <el-form-item label="责任区域ID" prop="regionId">
          <el-input v-model="form.regionId" placeholder="请输入责任区域ID" />
        </el-form-item>
        <el-form-item label="责任区域名称" prop="regionName">
          <el-input
            v-model="form.regionName"
            placeholder="请输入责任区域名称"
          />
        </el-form-item>
        <el-form-item label="责任区域全名称" prop="regionFullName">
          <el-input
            v-model="form.regionFullName"
            placeholder="请输入责任区域全名称"
          />
        </el-form-item>
        <el-form-item label="隐患条目ID" prop="dangerItemId">
          <el-input
            v-model="form.dangerItemId"
            placeholder="请输入隐患条目ID"
          />
        </el-form-item>
        <el-form-item label="隐患条目级别" prop="dangerItemLevel">
          <el-input
            v-model="form.dangerItemLevel"
            placeholder="请输入隐患条目级别"
          />
        </el-form-item>
        <el-form-item label="隐患条目编号" prop="dangerItemCode">
          <el-input
            v-model="form.dangerItemCode"
            placeholder="请输入隐患条目编号"
          />
        </el-form-item>
        <el-form-item label="隐患条目排查内容">
          <editor v-model="form.dangerItemContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="隐患根类别ID" prop="dangerTypeId">
          <el-input
            v-model="form.dangerTypeId"
            placeholder="请输入隐患根类别ID"
          />
        </el-form-item>
        <el-form-item label="隐患根类别编码" prop="dangerTypeCode">
          <el-input
            v-model="form.dangerTypeCode"
            placeholder="请输入隐患根类别编码"
          />
        </el-form-item>
        <el-form-item label="隐患根类别" prop="dangerTypeName">
          <el-input
            v-model="form.dangerTypeName"
            placeholder="请输入隐患根类别"
          />
        </el-form-item>
        <el-form-item label="隐患类别全路径" prop="dangerTypeFullId">
          <el-input
            v-model="form.dangerTypeFullId"
            placeholder="请输入隐患类别全路径"
          />
        </el-form-item>
        <el-form-item label="隐患条目全名称" prop="dangerTypeFullName">
          <el-input
            v-model="form.dangerTypeFullName"
            placeholder="请输入隐患条目全名称"
          />
        </el-form-item>
        <el-form-item label="隐患说明" prop="dangerDesc">
          <el-input v-model="form.dangerDesc" placeholder="请输入隐患说明" />
        </el-form-item>
        <el-form-item label="整改时间" prop="changeTime">
          <el-date-picker
            clearable
            v-model="form.changeTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择整改时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="整改时限(天)" prop="changeLimitTime">
          <el-date-picker
            clearable
            v-model="form.changeLimitTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择整改时限(天)"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="级别" prop="level">
          <el-input v-model="form.level" placeholder="请输入级别" />
        </el-form-item>
        <el-form-item label="整改人ID" prop="changeId">
          <el-input v-model="form.changeId" placeholder="请输入整改人ID" />
        </el-form-item>
        <el-form-item label="整改人名称" prop="changeName">
          <el-input v-model="form.changeName" placeholder="请输入整改人名称" />
        </el-form-item>
        <el-form-item label="超时状态 整改是否超时" prop="overTimeStat">
          <el-input
            v-model="form.overTimeStat"
            placeholder="请输入超时状态 整改是否超时"
          />
        </el-form-item>
        <el-form-item label="分包商名称" prop="vendorName">
          <el-input v-model="form.vendorName" placeholder="请输入分包商名称" />
        </el-form-item>
        <el-form-item label="责任分包单位=队伍名称" prop="teamName">
          <el-input
            v-model="form.teamName"
            placeholder="请输入责任分包单位=队伍名称"
          />
        </el-form-item>
        <el-form-item label="复查人ID" prop="reviewId">
          <el-input v-model="form.reviewId" placeholder="请输入复查人ID" />
        </el-form-item>
        <el-form-item label="复查人名称" prop="reviewName">
          <el-input v-model="form.reviewName" placeholder="请输入复查人名称" />
        </el-form-item>
        <el-form-item label="复查时间" prop="reviewTime">
          <el-date-picker
            clearable
            v-model="form.reviewTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择复查时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="复查时限" prop="reviewLimitTime">
          <el-date-picker
            clearable
            v-model="form.reviewLimitTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择复查时限"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="评分是否生效" prop="activeScore">
          <el-input
            v-model="form.activeScore"
            placeholder="请输入评分是否生效"
          />
        </el-form-item>
        <el-form-item label="预防措施" prop="preventiveMeasures">
          <el-input
            v-model="form.preventiveMeasures"
            placeholder="请输入预防措施"
          />
        </el-form-item>
        <el-form-item label="状态备注" prop="statusDesc">
          <el-input v-model="form.statusDesc" placeholder="请输入状态备注" />
        </el-form-item>
        <el-form-item label="级别备注" prop="levelDesc">
          <el-input v-model="form.levelDesc" placeholder="请输入级别备注" />
        </el-form-item>
        <el-form-item label="项目状态备注" prop="projectStatusDesc">
          <el-input
            v-model="form.projectStatusDesc"
            placeholder="请输入项目状态备注"
          />
        </el-form-item>
        <el-form-item label="${comment}" prop="overTimeStatDesc">
          <el-input
            v-model="form.overTimeStatDesc"
            placeholder="请输入${comment}"
          />
        </el-form-item>
        <el-form-item label="${comment}" prop="reviewStatDesc">
          <el-input
            v-model="form.reviewStatDesc"
            placeholder="请输入${comment}"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjHazardInvestigation,
  getZjHazardInvestigation,
  delZjHazardInvestigation,
  addZjHazardInvestigation,
  updateZjHazardInvestigation,
} from "@/api/inspection/zjHazardInvestigation";

export default {
  name: "ZjHazardInvestigation",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患排查治理表格数据
      zjHazardInvestigationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenantId: null,
        projectId: null,
        creatorName: null,
        creatorId: null,
        creatorWorkNum: null,
        projectCode: null,
        projectName: null,
        recordId: null,
        regionId: null,
        regionName: null,
        regionFullName: null,
        dangerItemId: null,
        dangerItemLevel: null,
        dangerItemCode: null,
        dangerItemContent: null,
        dangerTypeId: null,
        dangerTypeCode: null,
        dangerTypeName: null,
        dangerTypeFullId: null,
        dangerTypeFullName: null,
        inspectType: null,
        dangerDesc: null,
        changeTime: null,
        changeLimitTime: null,
        level: null,
        status: null,
        changeId: null,
        changeName: null,
        overTimeStat: null,
        vendorName: null,
        teamName: null,
        delStatus: null,
        reviewId: null,
        reviewName: null,
        reviewTime: null,
        reviewLimitTime: null,
        recordType: null,
        activeScore: null,
        preventiveMeasures: null,
        statusDesc: null,
        levelDesc: null,
        projectStatusDesc: null,
        overTimeStatDesc: null,
        reviewStatDesc: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      // 详情弹窗
      detailDialog: false,
      // 详情数据
      detailData: {},
      // 检查结果

      // 0: "无需整改",
      // 1: "待整改",
      // 2: "已整改",
      // 3: "已合格",
      // 4: "不合格",
      // 7: "待核验",

      statusOptions: [
        {
          label: "无需整改",
          value: 0,
        },
        {
          label: "待整改",
          value: 1,
        },
        {
          label: "已整改",
          value: 2,
        },
        {
          label: "已合格",
          value: 3,
        },
        {
          label: "不合格",
          value: 4,
        },
        {
          label: "待核验",
          value: 7,
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getStatusClass(type) {
      const arr = ["bg-blue", "bg-green", "bg-orange"];
      return arr[type - 1];
    },
    // 查看详情
    handleDetail(row) {
      const id = row.id;
      getZjHazardInvestigation(id).then((response) => {
        this.detailData = response.data;
        this.detailDialog = true;
      });
    },
    handlePrint() {
      window.print();
    },
    /** 查询隐患排查治理列表 */
    getList() {
      this.loading = true;
      listZjHazardInvestigation(this.queryParams).then((response) => {
        console.log(response, "getList");
        this.zjHazardInvestigationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {
        id: null,
        tenantId: null,
        projectId: null,
        createTime: null,
        creatorName: null,
        creatorId: null,
        creatorWorkNum: null,
        updateTime: null,
        remark: null,
        projectCode: null,
        projectName: null,
        recordId: null,
        regionId: null,
        regionName: null,
        regionFullName: null,
        dangerItemId: null,
        dangerItemLevel: null,
        dangerItemCode: null,
        dangerItemContent: null,
        dangerTypeId: null,
        dangerTypeCode: null,
        dangerTypeName: null,
        dangerTypeFullId: null,
        dangerTypeFullName: null,
        inspectType: null,
        dangerDesc: null,
        changeTime: null,
        changeLimitTime: null,
        level: null,
        status: null,
        changeId: null,
        changeName: null,
        overTimeStat: null,
        vendorName: null,
        teamName: null,
        delStatus: null,
        reviewId: null,
        reviewName: null,
        reviewTime: null,
        reviewLimitTime: null,
        recordType: null,
        activeScore: null,
        preventiveMeasures: null,
        statusDesc: null,
        levelDesc: null,
        projectStatusDesc: null,
        overTimeStatDesc: null,
        reviewStatDesc: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隐患排查治理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjHazardInvestigation(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改隐患排查治理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjHazardInvestigation(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjHazardInvestigation(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除隐患排查治理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjHazardInvestigation(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjHazardInvestigation/export",
        {
          ...this.queryParams,
        },
        `zjHazardInvestigation_${new Date().getTime()}.xlsx`
      );
    },
    // 获取状态文字
    getStatusText(status) {
      const statusMap = {
        0: "无需整改",
        1: "待整改",
        2: "待复查",
        3: "合格",
        4: "不合格",
        7: "待核验",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取整改状态文字
    getRectificationStatusText(status) {
      const statusMap = {
        0: "无需整改",
        1: "待整改",
        2: "已整改",
        3: "已合格",
        4: "不合格",
        7: "待核验",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取复查状态文字
    getReviewStatusText(status) {
      const statusMap = {
        0: "无需复查",
        1: "待复查",
        2: "待复查",
        3: "复查合格",
        4: "复查不合格",
        7: "待核验",
      };
      return statusMap[status] || "未复查";
    },
    // 获取详情页状态样式类
    getDetailStatusClass(status) {
      const classMap = {
        0: "status-no-need",
        1: "status-pending",
        2: "status-rectified",
        3: "status-qualified",
        4: "status-unqualified",
        7: "status-verify",
      };
      return classMap[status] || "status-unknown";
    },
    // 解析图片URL列表
    parseImageUrls(imageUrl, dataSource) {
      if (!imageUrl) return [];

      // 当dataSource为2时，处理后端拼接好的URL
      if (dataSource === 2 || dataSource === "2") {
        // 移除开头的@符号，然后按逗号分割
        const urlStr = imageUrl.startsWith("@")
          ? imageUrl.substring(1)
          : imageUrl;
        return urlStr
          .split(",")
          .filter((url) => url.trim())
          .map((url) => url.trim());
      }

      // 默认情况：前端拼接
      if (imageUrl.startsWith("/")) {
        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`];
      }
      return [imageUrl];
    },
    // 获取图片URL（兼容原有逻辑）
    getImageUrl(imageUrl) {
      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource);
      return urls.length > 0 ? urls[0] : "";
    },
    // 获取所有图片URL
    getAllImageUrls(imageUrl) {
      return this.parseImageUrls(imageUrl, this.detailData?.dataSource);
    },
    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) return;
      // 使用element-ui的图片预览功能
      this.$alert(
        `<img src="${imageUrl}" style="width: 100%; max-width: 500px;" alt="预览图片">`,
        "图片预览",
        {
          dangerouslyUseHTMLString: true,
          customClass: "image-preview-dialog",
          showConfirmButton: false,
          showCancelButton: true,
          cancelButtonText: "关闭",
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.font-12 {
  font-size: 12px;
}

.circle {
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 2px;
}

.bg-orange {
  background-color: #ffa500;
}

.bg-blue {
  background-color: #007bff;
}

.bg-green {
  background-color: #28a745;
}

/* 隐患标签样式 */
.hazard-tag {
  color: white !important;
  border: none !important;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 20px;
  flex-shrink: 0;
}

/* 一般隐患 - 黄色背景 */
.hazard-general {
  background-color: #f39c12 !important;
}

/* 常见隐患 - 红色背景 */
.hazard-common {
  background-color: #e74c3c !important;
}

/* 详情弹窗样式 */
:deep(.detail-dialog) {
  .el-dialog__header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
    padding: 15px 20px;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .el-dialog__body {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0px 20px !important;
  }

  ::v-deep(.el-dialog__body) {
    padding: 0px 20px !important;
  }
}

.detail-content {
  padding: 0 20px;

  .independent-section {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .independent-title {
    margin: 0 0 20px 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 20px;

    &:after {
      content: "";
      flex: 1;
      height: 2px;
      border-top: 2px dashed #409eff;
    }
  }

  // 左右两列布局
  .record-columns {
    display: flex;
    gap: 40px;
  }

  .left-column,
  .right-column {
    flex: 1;
  }

  .field-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &.full-width {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &.highlighted-field {
      background-color: #ebecf0;
      padding: 8px 0;
      margin-bottom: 0;
      margin-left: -20px;
      margin-right: -20px;
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  // 非高亮字段与高亮字段之间的间距
  .field-row:not(.highlighted-field) + .field-row.highlighted-field,
  .field-row.highlighted-field + .field-row:not(.highlighted-field) {
    margin-top: 16px;
  }

  .field-label {
    min-width: 70px;
    font-weight: 400;
    color: #666;
    margin-right: 10px;
    white-space: nowrap;
    font-size: 14px;
    line-height: 1.5;
  }

  .field-value {
    color: #333;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
    flex: 1;

    &.status-tag {
      padding: 2px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  // 整改记录部分样式
  .rectification-info {
    display: flex;
    gap: 0;
    margin-bottom: 16px;
    background-color: #ebecf0;
    margin-left: -20px;
    margin-right: -20px;
    padding: 8px 20px;

    .field-row {
      margin-bottom: 0;
      white-space: nowrap;
      flex: 1;
      margin-left: 0;
      margin-right: 0;
      padding: 0 20px;
      background-color: transparent;

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        padding-right: 0;
      }
    }
  }

  // 状态标签颜色
  .status-no-need {
    background-color: #f0f9ff;
    color: #0369a1;
    border: 1px solid #7dd3fc;
  }

  .status-pending {
    background-color: transparent;
    color: #d97706;
    border: none;
  }

  .status-rectified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-qualified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-unqualified {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
  }

  .status-verify {
    background-color: #ede9fe;
    color: #7c3aed;
    border: 1px solid #a78bfa;
  }

  .status-unknown {
    background-color: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  // 照片相关样式
  .photo-section {
    // margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
  }

  .photo-container {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .photo-item,
  .photo-placeholder {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;

    &:hover {
      transform: scale(1.02);
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    img {
      width: 120px;
      height: 90px;
      object-fit: cover;
      display: block;
    }
  }

  .photo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border: 1px dashed #d1d5db;

    &:hover {
      transform: none;
      border-color: #d1d5db;
      box-shadow: none;
    }

    img {
      opacity: 0.3;
      background: transparent;
    }
  }

  .no-photo {
    color: #999;
    font-style: italic;
    font-size: 12px;
    margin-top: 8px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .record-columns {
      flex-direction: column;
      gap: 20px;
    }

    .rectification-info {
      flex-direction: column;
      gap: 10px;
    }
  }
}

// 图片预览弹窗样式
:deep(.image-preview-dialog) {
  .el-message-box__content {
    text-align: center;
  }

  .el-message-box__message {
    margin: 0;
  }
}
</style>
