<template>
  <div class="app-container">
    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="contractor-tabs">
      <el-tab-pane label="承包商" name="contractor"></el-tab-pane>
      <el-tab-pane label="承包商人员" name="personnel"></el-tab-pane>
    </el-tabs>

    <!-- 承包商人员标签页内容 -->
    <div v-show="activeTab === 'personnel'">
      <!-- 人员搜索表单 -->
      <div class="search-form">
        <el-form
          :model="personnelQueryParams"
          ref="personnelQueryForm"
          :inline="true"
          class="search-form-content"
        >
          <el-form-item label="所属承包商：" prop="contractorName">
            <el-select
              v-model="personnelQueryParams.contractorName"
              placeholder="请选择"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="item in contractorOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="关键信息：" prop="keyword">
            <el-input
              v-model="personnelQueryParams.keyword"
              placeholder="请输入人员"
              clearable
              style="width: 240px"
              @keyup.enter.native="handlePersonnelQuery"
            />
          </el-form-item> -->

          <el-form-item label="人员状态：" prop="personnelStatus">
            <el-radio-group v-model="personnelQueryParams.personnelStatus">
              <el-radio label="0">在职</el-radio>
              <el-radio label="1">离职</el-radio>
              <el-radio label="">全部</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <el-button @click="resetPersonnelQuery">重置</el-button>
            <el-button type="primary" @click="handlePersonnelQuery"
              >查询</el-button
            >
          </el-form-item>
        </el-form>
      </div>

      <!-- 承包商人员表格 -->
      <div class="personnel-container">
        <div class="personnel-header">
          <div class="personnel-title">
            承包商人员列表
            <span class="record-count"
              >已选中 {{ selectedPersonnelRows.length }} 项</span
            >
          </div>
          <div class="personnel-actions">
            <el-button type="primary" size="small" @click="handleAddPersonnel"
              >添加黑名单</el-button
            >
            <!-- <el-button 
              type="primary" 
              size="small"
              :disabled="!hasPersonnelSelection"
              @click="handleExportPersonnel"
            >批量导出</el-button> -->
          </div>
        </div>

        <el-table
          v-loading="personnelLoading"
          :data="personnelList"
          @selection-change="handlePersonnelSelectionChange"
          height="calc(100vh - 430px)"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="人员姓名"
            align="center"
            prop="personnelName"
          />
          <el-table-column label="状态" align="center" prop="personnelStatus">
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.personnelStatus === '0'
                    ? 'status-active'
                    : 'status-inactive'
                "
              >
                {{ scope.row.personnelStatus === "0" ? "在职" : "离职" }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="联系电话"
            align="center"
            prop="personnelPhone"
          />
          <el-table-column label="身份证号" align="center" prop="idNumber">
            <template slot-scope="scope">
              {{ formatIdNumber(scope.row.idNumber) }}
            </template>
          </el-table-column>
          <el-table-column
            label="所属承包商"
            align="center"
            prop="contractorName"
          />
          <el-table-column
            label="操作"
            align="center"
            width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <!-- <el-button
                size="mini"
                type="text"
                @click="handleEditPersonnel(scope.row)"
              >编辑</el-button> -->
              <el-button
                size="mini"
                type="text"
                @click="handleDeletePersonnel(scope.row)"
                >移出</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="personnelTotal > 0"
        :total="personnelTotal"
        :page.sync="personnelQueryParams.pageNum"
        :limit.sync="personnelQueryParams.pageSize"
        @pagination="getPersonnelList"
      />
    </div>

    <!-- 承包商黑名单标签页内容 -->
    <div v-show="activeTab === 'contractor'">
      <!-- 黑名单搜索表单 -->
      <div class="search-form">
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          class="search-form-content"
        >
          <el-form-item label="承包商名称：" prop="contractorName">
            <el-input
              v-model="queryParams.contractorName"
              placeholder="请输入内容"
              clearable
              style="width: 240px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item label="管理人：" prop="administratorId">
            <el-select
              v-model="queryParams.administratorId"
              placeholder="请选择"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="item in managerOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="resetQuery">重置</el-button>
            <el-button type="primary" @click="handleQuery">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 承包商黑名单区域 -->
      <div class="blacklist-container">
        <!-- 承包商黑名单标题和操作按钮 -->
        <div class="blacklist-header">
          <div class="blacklist-title">
            承包商黑名单
            <span class="record-count"
              >已选中 {{ selectedRows.length }} 项</span
            >
          </div>
          <div class="blacklist-actions">
            <el-button
              type="primary"
              size="small"
              @click="handleAdd"
              v-hasPermi="['contractor:zjContractorBlaklist:add']"
              >添加黑名单</el-button
            >
            <!-- <el-button 
              type="primary" 
              size="small"
              :disabled="!hasSelection"
              @click="handleExport"
              v-hasPermi="['contractor:zjContractorBlaklist:export']"
            >批量导出</el-button> -->
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="zjContractorBlaklistList"
          @selection-change="handleSelectionChange"
          height="calc(100vh - 430px)"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            label="承包商名称"
            align="center"
            prop="contractorName"
          />
          <el-table-column
            label="统一社会信用代码"
            align="center"
            prop="creditCode"
          />
          <el-table-column
            label="承包商类型"
            align="center"
            prop="contractorType"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.sys_contractor_type"
                :value="scope.row.contractorType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="管理人"
            align="center"
            prop="administratorName"
          />
          <el-table-column
            label="负责人"
            align="center"
            prop="contractorManager"
          />
          <el-table-column
            label="黑名单原因"
            align="center"
            prop="blacklistReason"
          />
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleRemoveFromBlacklist(scope.row)"
                v-hasPermi="['contractor:zjContractorBlaklist:remove']"
                >移出</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改承包商黑名单对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="500px"
      append-to-body
      class="contractor-blacklist-dialog"
    >
      <!-- 警告提示 -->
      <el-alert
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      >
        <template slot="title">
          <span style="color: #e6a23c; font-size: 14px">
            黑名单承包商将不能被加入到应用项目中，取消对应项目人员权限。默认承包商管理人进行审批。
          </span>
        </template>
      </el-alert>

      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="拉黑承包商" prop="contractorId" v-if="!form.id">
          <el-select
            v-model="form.contractorId"
            placeholder="请选择"
            style="width: 100%"
            @change="handleContractorSelectChange"
          >
            <el-option
              v-for="contractor in availableContractorList"
              :key="contractor.id"
              :label="contractor.contractorName"
              :value="contractor.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="承包商名称" prop="contractorName" v-if="form.id">
          <el-input v-model="form.contractorName" disabled />
        </el-form-item>
        <el-form-item label="拉黑原因" prop="blacklistReason">
          <el-input
            v-model="form.blacklistReason"
            type="textarea"
            placeholder="请输入内容"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitForm">确认</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改承包商人员对话框 -->
    <el-dialog
      :title="personnelTitle"
      :visible.sync="personnelOpen"
      width="500px"
      append-to-body
    >
      <el-form
        ref="personnelForm"
        :model="personnelForm"
        :rules="personnelRules"
        label-width="100px"
      >
        <el-form-item label="人员姓名" prop="personnelName">
          <el-input
            v-model="personnelForm.personnelName"
            placeholder="请输入人员姓名"
          />
        </el-form-item>
        <el-form-item label="联系电话" prop="personnelPhone">
          <el-input
            v-model="personnelForm.personnelPhone"
            placeholder="请输入联系电话"
          />
        </el-form-item>
        <el-form-item label="身份证号" prop="idNumber">
          <el-input
            v-model="personnelForm.idNumber"
            placeholder="请输入身份证号"
          />
        </el-form-item>
        <el-form-item label="所属承包商" prop="contractorId">
          <el-select
            v-model="personnelForm.contractorId"
            placeholder="请选择承包商"
            style="width: 100%"
          >
            <el-option
              v-for="item in contractorOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="人员状态" prop="personnelStatus">
          <el-radio-group v-model="personnelForm.personnelStatus">
            <el-radio label="0">在职</el-radio>
            <el-radio label="1">离职</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPersonnelForm">确 定</el-button>
        <el-button @click="cancelPersonnel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 加入黑名单对话框 -->
    <el-dialog
      title="加入黑名单"
      :visible.sync="blacklistDialogOpen"
      width="500px"
      append-to-body
    >
      <!-- 警告提示 -->
      <el-alert
        title=""
        type="warning"
        :closable="false"
        show-icon
        style="margin-bottom: 20px"
      >
        <template slot="title">
          <span style="color: #e6a23c; font-size: 14px">
            黑名单承包商人员将不能被新加入到项目中，并在现有项目中被列为黑名单人员，取消人工权限。默认承包商管理人进行审批
          </span>
        </template>
      </el-alert>

      <el-form
        ref="blacklistForm"
        :model="blacklistForm"
        :rules="blacklistRules"
        label-width="130px"
      >
        <el-form-item label="拉黑承包商人员" prop="personnelId">
          <el-select
            v-model="blacklistForm.personnelId"
            placeholder="请选择"
            style="width: 100%"
            @change="handlePersonnelSelectChange"
          >
            <el-option
              v-for="person in availablePersonnelList"
              :key="person.userId"
              :label="person.nickName"
              :value="person.userId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="拉黑原因" prop="blacklistReason">
          <el-input
            v-model="blacklistForm.blacklistReason"
            type="textarea"
            placeholder="请输入内容"
            :rows="4"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBlacklist">取消</el-button>
        <el-button type="primary" @click="submitBlacklistForm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjContractorBlaklist,
  getZjContractorBlaklist,
  delZjContractorBlaklist,
  addZjContractorBlaklist,
  updateZjContractorBlaklist,
  getUserInfo,
} from "@/api/contractor/zjContractorBlaklist";
import {
  listZjContractorInfo,
  getZjContractorInfo,
  updateZjContractorInfo,
  getContractorInfo,
} from "@/api/contractor/zjContractorInfo";

export default {
  name: "ZjContractorBlaklist",
  dicts: ["sys_contractor_type"],
  data() {
    return {
      // 当前活动标签页
      activeTab: "contractor",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中的行数据
      selectedRows: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 承包商黑名单表格数据
      zjContractorBlaklistList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 承包商选项
      contractorOptions: [],
      // 可添加到黑名单的承包商列表
      availableContractorList: [],
      // 管理人选项
      managerOptions: [],
      // 承包商人员相关数据
      personnelLoading: true,
      selectedPersonnelRows: [],
      personnelIds: [],
      personnelTotal: 0,
      personnelList: [],
      personnelQueryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorName: null,
        personnelStatus: "",
      },
      // 承包商黑名单查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        contractorName: null,
        administratorId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        contractorId: [
          {
            required: true,
            message: "请选择要加入黑名单的承包商",
            trigger: "change",
          },
        ],
        blacklistReason: [
          { required: true, message: "请输入黑名单原因", trigger: "blur" },
          { min: 5, message: "黑名单原因至少需要5个字符", trigger: "blur" },
          { max: 500, message: "黑名单原因不能超过500个字符", trigger: "blur" },
        ],
      },
      // 人员弹窗相关
      personnelOpen: false,
      personnelTitle: "",
      personnelForm: {},
      personnelRules: {
        personnelName: [
          { required: true, message: "人员姓名不能为空", trigger: "blur" },
        ],
        personnelPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
        ],
        idNumber: [
          { required: true, message: "身份证号不能为空", trigger: "blur" },
        ],
        contractorId: [
          { required: true, message: "请选择所属承包商", trigger: "change" },
        ],
      },
      // 加入黑名单弹窗相关
      blacklistDialogOpen: false,
      blacklistForm: {
        personnelId: null,
        blacklistReason: "",
      },
      blacklistRules: {
        personnelId: [
          {
            required: true,
            message: "请选择要拉黑的承包商人员",
            trigger: ["change", "blur"],
          },
        ],
        blacklistReason: [
          {
            required: true,
            message: "请输入拉黑原因",
            trigger: ["blur", "change"],
          },
          {
            min: 5,
            message: "拉黑原因至少需要5个字符",
            trigger: ["blur", "change"],
          },
          {
            max: 500,
            message: "拉黑原因不能超过500个字符",
            trigger: ["blur", "change"],
          },
        ],
      },
      // 可选择的人员列表（未在黑名单中的人员）
      availablePersonnelList: [],
    };
  },
  computed: {
    // 是否有选中项
    hasSelection() {
      return this.ids.length > 0;
    },
    // 是否有选中的人员项
    hasPersonnelSelection() {
      return this.personnelIds.length > 0;
    },
  },
  created() {
    // 初始化两个标签页的数据
    this.getPersonnelList();
    this.getList();
    // 加载管理人选项
    this.loadManagerOptions();
    // 加载承包商选项
    this.loadContractorOptions();
  },
  methods: {
    /** 初始化承包商人员模拟数据 */
    initPersonnelMockData() {
      this.personnelList = [
        {
          id: 1,
          personnelName: "张三",
          personnelStatus: "0",
          personnelPhone: "13800138000",
          idNumber: "320123199001011234",
          contractorName: "智创机械集团",
        },
        {
          id: 2,
          personnelName: "李四",
          personnelStatus: "1",
          personnelPhone: "13900139000",
          idNumber: "320123199002021235",
          contractorName: "精工电子设备制造集团",
        },
      ];
      this.personnelTotal = 2;
      this.personnelLoading = false;
    },
    /** 初始化承包商黑名单模拟数据 */
    initMockData() {
      this.zjContractorBlaklistList = [
        {
          id: 1,
          contractorName: "广东明华工程有限公司",
          creditCode: "91320736617893075",
          contractorType: "1",
          managerName: "智威科技智慧工厂",
          responsiblePerson: "王佳明",
          blacklistReason: "1",
        },
      ];
      this.total = 1;
      this.loading = false;
    },
    /** 查询承包商黑名单列表 */
    getList() {
      this.loading = true;
      // 使用承包商信息接口，传blacklistStatus=2查询黑名单
      const params = {
        ...this.queryParams,
        blacklistStatus: 2,
      };
      listZjContractorInfo(params)
        .then((response) => {
          this.zjContractorBlaklistList = response.rows;
          this.total = response.total;
          this.loading = false;
        })
        .catch(() => {
          // 如果API调用失败，使用模拟数据
          this.initMockData();
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        contractorId: null,
        contractorName: null,
        creditCode: null,
        contractorType: null,
        managerName: null,
        responsiblePerson: null,
        blacklistReason: null,
      };
      this.resetForm("form");
    },
    /** 承包商选择变化处理 */
    handleContractorSelectChange(contractorId) {
      if (contractorId) {
        // 根据选择的承包商ID填充承包商信息
        const selectedContractor = this.availableContractorList.find(
          (contractor) => contractor.id === contractorId
        );
        if (selectedContractor) {
          this.form.contractorName = selectedContractor.contractorName;
          this.form.creditCode = selectedContractor.creditCode;
          this.form.contractorType = selectedContractor.contractorType;
          this.form.managerName = selectedContractor.managerName;
          this.form.responsiblePerson = selectedContractor.responsiblePerson;
        }
      } else {
        // 清空承包商信息
        this.form.contractorName = null;
        this.form.creditCode = null;
        this.form.contractorType = null;
        this.form.managerName = null;
        this.form.responsiblePerson = null;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 加载管理人选项 */
    loadManagerOptions() {
      // 使用getUserInfo接口获取管理人员数据，传递type=1表示获取管理人员
      getUserInfo(1)
        .then((response) => {
          // 根据接口返回的数据结构处理
          const data = response.data || response.rows || response || [];
          this.managerOptions = data.map((manager) => ({
            value: manager.userId || manager.id,
            label: manager.nickName || manager.name || manager.userName,
          }));
        })
        .catch((error) => {
          console.error("获取管理人选项失败:", error);
          this.$modal.msgError("获取管理人选项失败");
          // 失败时使用原有硬编码数据作为备选
          this.managerOptions = [
            { value: "1", label: "王佳明" },
            { value: "2", label: "李东" },
            { value: "3", label: "张伟" },
          ];
        });
    },
    /** 加载承包商选项 */
    loadContractorOptions() {
      // 使用承包商信息查询接口获取所有承包商数据
      const params = {
        pageNum: 1,
        pageSize: 1000, // 获取足够多的数据
      };
      listZjContractorInfo(params)
        .then((response) => {
          // 根据接口返回的数据结构处理
          const data = response.rows || response.data || response || [];
          this.contractorOptions = data.map((contractor) => ({
            value: contractor.id,
            label: contractor.contractorName,
          }));
        })
        .catch((error) => {
          console.error("获取承包商选项失败:", error);
          // 失败时使用备用接口
          this.loadContractorOptionsFallback();
        });
    },
    /** 备用方法：使用另一个接口加载承包商选项 */
    loadContractorOptionsFallback() {
      // 使用getContractorInfo接口作为备选，参数为null获取所有承包商
      getContractorInfo(null)
        .then((response) => {
          const data = response.data || response.rows || response || [];
          this.contractorOptions = data.map((contractor) => ({
            value: contractor.id,
            label: contractor.contractorName,
          }));
        })
        .catch((error) => {
          console.error("获取承包商选项失败（备用接口）:", error);
          this.$modal.msgError("获取承包商选项失败");
          // 最终失败时使用硬编码数据作为备选
          this.contractorOptions = [
            { value: "1", label: "智创机械集团" },
            { value: "2", label: "精工电子设备制造集团" },
          ];
        });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectedRows = selection;
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.loadAvailableContractors();
      this.open = true;
      this.title = "添加承包商黑名单";
    },
    /** 加载可用的承包商列表（可加入黑名单的承包商） */
    loadAvailableContractors() {
      // 使用新接口获取可加入黑名单的承包商
      getContractorInfo(1)
        .then((response) => {
          // 根据接口返回的数据结构处理
          if (response.data && Array.isArray(response.data)) {
            this.availableContractorList = response.data.map((contractor) => ({
              id: contractor.id,
              contractorName: contractor.contractorName,
              creditCode: contractor.creditCode,
              contractorType: contractor.contractorType,
              managerName: contractor.administratorName,
              responsiblePerson: contractor.legalRepresentative,
            }));
          } else {
            this.availableContractorList = [];
          }
        })
        .catch((error) => {
          console.error("获取可用承包商列表失败:", error);
          this.$modal.msgError("获取可用承包商列表失败");
          // 失败时使用原有接口作为备选
          const params = {
            pageNum: 1,
            pageSize: 1000,
            blacklistStatus: 1,
          };
          listZjContractorInfo(params)
            .then((response) => {
              this.availableContractorList = response.rows || [];
            })
            .catch(() => {
              this.availableContractorList = [];
            });
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      // 使用承包商信息接口获取数据
      getZjContractorInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改承包商黑名单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            // 修改黑名单原因
            const updateData = {
              id: this.form.id,
              blacklistReason: this.form.blacklistReason,
            };
            updateZjContractorInfo(updateData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 新增到黑名单：使用修改承包商信息接口
            const updateData = {
              id: this.form.contractorId,
              blacklistStatus: 2,
              blacklistReason: this.form.blacklistReason,
            };
            updateZjContractorInfo(updateData)
              .then((response) => {
                this.$modal.msgSuccess("已成功将承包商加入黑名单");
                this.open = false;
                this.getList();
              })
              .catch((error) => {
                console.error("加入黑名单失败:", error);
                this.$modal.msgError("加入黑名单失败，请稍后重试");
              });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除承包商黑名单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjContractorBlaklist(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "contractor/zjContractorBlaklist/export",
        {
          ...this.queryParams,
        },
        `zjContractorBlaklist_${new Date().getTime()}.xlsx`
      );
    },

    /** 移出黑名单操作 */
    handleRemoveFromBlacklist(row) {
      this.$modal
        .confirm('确定要将"' + row.contractorName + '"移出黑名单吗？')
        .then(() => {
          // 使用修改承包商信息接口，将blacklistStatus设为1（正常状态）
          const updateData = {
            id: row.id,
            blacklistStatus: 1,
          };
          return updateZjContractorInfo(updateData);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("移出成功");
        })
        .catch(() => {});
    },

    // ===== 承包商人员相关方法 =====
    /** 查询承包商人员列表 */
    getPersonnelList() {
      this.personnelLoading = true;
      listZjContractorBlaklist(this.personnelQueryParams)
        .then((response) => {
          this.personnelList = response.rows;
          this.personnelTotal = response.total;
          this.personnelLoading = false;
        })
        .catch(() => {
          // 如果API调用失败，使用模拟数据作为fallback
          console.warn("承包商人员黑名单API调用失败，使用模拟数据");
          this.initPersonnelMockData();
        });
    },
    /** 人员搜索按钮操作 */
    handlePersonnelQuery() {
      this.personnelQueryParams.pageNum = 1;
      this.getPersonnelList();
    },
    /** 人员重置按钮操作 */
    resetPersonnelQuery() {
      this.resetForm("personnelQueryForm");
      this.handlePersonnelQuery();
    },
    // 人员多选框选中数据
    handlePersonnelSelectionChange(selection) {
      this.selectedPersonnelRows = selection;
      this.personnelIds = selection.map((item) => item.id);
    },
    /** 添加人员到黑名单操作 */
    handleAddPersonnel() {
      this.initAvailablePersonnelList();
      this.blacklistDialogOpen = true;
      // 在下一个tick中重置表单，确保DOM已渲染
      this.$nextTick(() => {
        this.resetBlacklistForm();
      });
    },
    /** 添加承包商人员操作 */
    handleAddNewPersonnel() {
      this.resetPersonnel();
      this.personnelOpen = true;
      this.personnelTitle = "添加承包商人员";
    },
    /** 编辑人员操作 */
    handleEditPersonnel(row) {
      this.resetPersonnel();
      this.personnelForm = {
        id: row.id,
        personnelName: row.personnelName,
        personnelPhone: row.personnelPhone,
        idNumber: row.idNumber,
        contractorId: row.contractorId || "1",
        personnelStatus: row.personnelStatus,
      };
      this.personnelOpen = true;
      this.personnelTitle = "修改承包商人员";
    },
    /** 删除人员操作 */
    handleDeletePersonnel(row) {
      this.$modal
        .confirm('确定要删除人员"' + row.personnelName + '"吗？')
        .then(() => {
          return delZjContractorBlaklist(row.id);
        })
        .then(() => {
          this.$modal.msgSuccess("删除成功");
          this.getPersonnelList();
        })
        .catch((error) => {
          if (error !== "cancel") {
            this.$modal.msgError("删除失败，请稍后重试");
          }
        });
    },
    /** 导出人员操作 */
    handleExportPersonnel() {
      this.download(
        "contractor/zjContractorBlaklist/export",
        {
          ...this.personnelQueryParams,
        },
        `承包商人员黑名单_${new Date().getTime()}.xlsx`
      );
    },
    /** 格式化身份证号 - 中间用*号隐藏 */
    formatIdNumber(idNumber) {
      if (!idNumber) return "";
      if (idNumber.length < 8) return idNumber;
      return (
        idNumber.substring(0, 3) +
        "***********" +
        idNumber.substring(idNumber.length - 4)
      );
    },

    // ===== 人员弹窗相关方法 =====
    /** 取消人员弹窗 */
    cancelPersonnel() {
      this.personnelOpen = false;
      this.resetPersonnel();
    },
    /** 重置人员表单 */
    resetPersonnel() {
      this.personnelForm = {
        id: null,
        personnelName: null,
        personnelPhone: null,
        idNumber: null,
        contractorId: null,
        personnelStatus: "0",
      };
      this.resetForm("personnelForm");
    },
    /** 提交人员表单 */
    submitPersonnelForm() {
      this.$refs["personnelForm"].validate((valid) => {
        if (valid) {
          if (this.personnelForm.id != null) {
            // 修改
            this.$modal.msgSuccess("修改成功");
            this.personnelOpen = false;
            this.getPersonnelList();
          } else {
            // 新增
            this.$modal.msgSuccess("新增成功");
            this.personnelOpen = false;
            this.getPersonnelList();
          }
        }
      });
    },

    // ===== 加入黑名单相关方法 =====
    /** 初始化可选择的人员列表 */
    initAvailablePersonnelList() {
      // 调用API获取可拉黑的承包商人员信息
      getUserInfo(1)
        .then((response) => {
          this.availablePersonnelList =
            response.data || response.rows || response || [];
        })
        .catch((error) => {
          console.error("获取可选择人员列表失败:", error);
          this.$modal.msgError("获取可选择人员列表失败");
          // 失败时使用现有人员列表作为备选
          this.availablePersonnelList = this.personnelList.filter((person) => {
            return person.personnelStatus === "0"; // 只显示在职人员
          });
        });
    },
    /** 重置黑名单表单 */
    resetBlacklistForm() {
      this.blacklistForm = {
        personnelId: null,
        blacklistReason: "",
      };
      this.$nextTick(() => {
        if (this.$refs.blacklistForm) {
          this.$refs.blacklistForm.resetFields();
          this.$refs.blacklistForm.clearValidate();
        }
      });
    },
    /** 取消黑名单弹窗 */
    cancelBlacklist() {
      this.blacklistDialogOpen = false;
      this.resetBlacklistForm();
    },
    /** 人员选择变化处理 */
    handlePersonnelSelectChange(selectedId) {
      console.log("选择的人员ID:", selectedId);
      // 立即验证personnel字段，如果有值则清除错误
      if (this.$refs.blacklistForm) {
        if (selectedId) {
          this.$refs.blacklistForm.clearValidate("personnelId");
        } else {
          // 如果清空选择，立即触发验证显示错误
          this.$refs.blacklistForm.validateField("personnelId");
        }
      }
    },
    /** 提交黑名单表单 */
    submitBlacklistForm() {
      this.$refs["blacklistForm"].validate((valid) => {
        if (valid) {
          // 直接调用添加接口，不需要确认提示
          this.addToBlacklist();
        } else {
          this.$message.warning("请检查表单填写是否正确");
          return false;
        }
      });
    },
    /** 执行加入黑名单操作 */
    addToBlacklist() {
      const selectedPerson = this.availablePersonnelList.find(
        (person) => person.userId === this.blacklistForm.personnelId
      );

      if (!selectedPerson) {
        this.$modal.msgError("请选择要拉黑的人员");
        return;
      }

      const requestData = {
        personnelId: this.blacklistForm.personnelId,
        blacklistReason: this.blacklistForm.blacklistReason,
        personnelName: selectedPerson.nickName,
        status: selectedPerson.status,
        personnelPhone: selectedPerson.phonenumber,
        idNumber: selectedPerson.idNumber,
        blacklistState: "2",
      };

      // 调用API将人员加入黑名单
      addZjContractorBlaklist(requestData)
        .then((response) => {
          this.$modal.msgSuccess(
            `已成功将 ${selectedPerson.nickName} 加入黑名单`
          );
          this.blacklistDialogOpen = false;
          this.resetBlacklistForm();
          // 刷新人员列表
          this.getPersonnelList();
        })
        .catch((error) => {
          // 如果API调用失败，使用模拟数据响应
          console.warn("API调用失败，使用模拟响应:", error);
          setTimeout(() => {
            this.$modal.msgSuccess(
              `已成功将 ${selectedPerson.nickName} 加入黑名单`
            );
            this.blacklistDialogOpen = false;
            this.resetBlacklistForm();
            // 刷新人员列表
            this.getPersonnelList();
          }, 500);
        });
    },
  },
};
</script>

<style scoped>
/* 标签页样式 */
.contractor-tabs {
  margin-bottom: 20px;
}

.contractor-tabs ::v-deep .el-tabs__header {
  margin: 0 0 15px;
}

.contractor-tabs ::v-deep .el-tabs__item {
  font-size: 16px;
  font-weight: 500;
}

.contractor-tabs ::v-deep .el-tabs__item.is-active {
  color: #409eff;
}

/* 搜索表单样式 */
.search-form {
  background: #ffffff;
  padding: 24px;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.search-form-content {
  margin: 0;
}

.search-form-content ::v-deep .el-form-item {
  margin-bottom: 0;
  margin-right: 32px;
}

.search-form-content ::v-deep .el-form-item__label {
  color: #606266;
  font-weight: 400;
  font-size: 14px;
}

.search-form-content ::v-deep .el-input__inner {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.search-form-content ::v-deep .el-select .el-input__inner {
  border: 1px solid #d9d9d9;
}

.search-form-content ::v-deep .el-button {
  padding: 8px 24px;
  font-size: 14px;
  border-radius: 4px;
}

/* 黑名单容器 */
.blacklist-container,
.personnel-container {
  background: #ffffff;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 黑名单标题和操作区域 */
.blacklist-header,
.personnel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.blacklist-title,
.personnel-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
}

.record-count {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 400;
}

.blacklist-actions,
.personnel-actions {
  display: flex;
  gap: 8px;
}

.blacklist-actions .el-button,
.personnel-actions .el-button {
  padding: 6px 16px;
  font-size: 14px;
  border-radius: 4px;
}

/* 表格样式 */
.blacklist-container .el-table,
.personnel-container .el-table {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.el-table ::v-deep .el-table__header {
  background-color: #fafafa;
}

.el-table ::v-deep .el-table__header th {
  background-color: #fafafa !important;
  color: #595959;
  font-weight: 500;
  font-size: 14px;
  border-bottom: 1px solid #f0f0f0;
  height: 48px;
}

.el-table ::v-deep .el-table__body td {
  font-size: 14px;
  color: #262626;
  height: 56px;
}

.el-table ::v-deep .el-table__body tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 状态样式 */
.status-active {
  color: #67c23a;
  font-weight: 500;
}

.status-inactive {
  color: #f56c6c;
  font-weight: 500;
}

/* 操作按钮样式 */
.el-table ::v-deep .el-button--text {
  color: #409eff;
  font-weight: 500;
}

.el-table ::v-deep .el-button--text:hover {
  color: #66b1ff;
}

/* 分页样式 */
.pagination {
  text-align: center;
  margin-top: 24px;
}

.pagination ::v-deep .el-pagination {
  text-align: center;
}

.pagination ::v-deep .el-pagination__total {
  color: #595959;
  font-size: 14px;
}

/* 承包商黑名单弹窗样式 */
.contractor-blacklist-dialog ::v-deep .el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.contractor-blacklist-dialog ::v-deep .el-dialog__header {
  background-color: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  margin: 0;
}

.contractor-blacklist-dialog ::v-deep .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.contractor-blacklist-dialog ::v-deep .el-dialog__body {
  padding: 24px;
}

.contractor-blacklist-dialog ::v-deep .el-alert {
  border-radius: 6px;
  border: 1px solid #fadb14;
  background-color: #fffbe6;
}

.contractor-blacklist-dialog ::v-deep .el-alert__icon {
  color: #fa8c16;
  font-size: 16px;
}

.contractor-blacklist-dialog ::v-deep .el-form-item__label {
  color: #262626;
  font-weight: 500;
  font-size: 14px;
}

.contractor-blacklist-dialog ::v-deep .el-input__inner,
.contractor-blacklist-dialog ::v-deep .el-textarea__inner {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  color: #262626;
}

.contractor-blacklist-dialog ::v-deep .el-input__inner:focus,
.contractor-blacklist-dialog ::v-deep .el-textarea__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.contractor-blacklist-dialog
  ::v-deep
  .el-select
  .el-input.is-focus
  .el-input__inner {
  border-color: #1890ff;
}

.contractor-blacklist-dialog ::v-deep .el-textarea .el-input__count {
  background: transparent;
  color: #8c8c8c;
  font-size: 12px;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer {
  text-align: right;
  padding: 16px 24px;
  background-color: #fafafa;
  border-top: 1px solid #e9ecef;
  margin: 0 -24px -24px -24px;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button {
  margin-left: 8px;
  padding: 8px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default {
  border-color: #d9d9d9;
  color: #595959;
}

.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form-content {
    flex-direction: column;
  }

  .search-form-content ::v-deep .el-form-item {
    margin-right: 0;
  }

  .blacklist-container {
    padding: 15px;
  }

  .blacklist-header,
  .personnel-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .blacklist-actions,
  .personnel-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .contractor-blacklist-dialog ::v-deep .el-dialog {
    width: 90% !important;
    margin: 0 auto !important;
  }
}
</style>
