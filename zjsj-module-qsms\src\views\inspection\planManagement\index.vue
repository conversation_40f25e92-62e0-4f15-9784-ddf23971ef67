<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="预案名称" prop="planName">
        <el-input
          v-model="queryParams.planName"
          placeholder="请输入预案名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <selectPeopleTree
          v-model="queryParams.projectName"
          :people-list="projectList"
          placeholder="请选择项目名称"
          @change="handleProjectChange"
        />
      </el-form-item>
      <el-form-item label="公司" prop="unitName">
        <el-select
          ref="queryCompanySelect"
          v-model="queryParams.unitName"
          placeholder="请选择公司"
          clearable
          style="width: 180px"
          popper-class="org-tree-select-dropdown"
        >
          <el-option
            :value="queryParams.unitName"
            :label="queryParams.unitName"
            style="height: auto; padding: 0; border: none"
          >
            <div class="tree-select-wrapper">
              <el-tree
                v-loading="companyTreeLoading"
                :data="companyTreeData"
                :props="companyTreeProps"
                highlight-current
                @node-click="handleQueryCompanyNodeClick"
              >
                <template #default="{ node, data }">
                  <el-tooltip
                    effect="dark"
                    :content="data.label"
                    placement="top"
                  >
                    <span class="el-tree-node__label">
                      {{ node.label }}
                    </span>
                  </el-tooltip>
                </template>
              </el-tree>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="上传url" prop="uploadUrl">
        <el-input
          v-model="queryParams.uploadUrl"
          placeholder="请输入上传url"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:planManagement:add']"
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:planManagement:edit']"
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:planManagement:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:planManagement:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="planManagementList"
      height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
      <el-table-column label="预案编号" align="center" prop="planNumber" />
      <el-table-column label="预案名称" align="center" prop="planName" />
      <el-table-column label="类型" align="center" prop="planType">
        <template slot-scope="scope">
          {{ getPlanType(scope.row.planType) }}
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <el-table-column label="公司" align="center" prop="unitName" />
      <el-table-column label="上传人" align="center" prop="uploadName" />
      <el-table-column
        label="上传时间"
        align="center"
        prop="uploadTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uploadTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="附件"
        align="center"
        prop="uploadUrl"
        width="100"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.uploadUrl"
            size="mini"
            type="text"
            @click="handleViewAttachment(scope.row.uploadUrl)"
            >查看</el-button
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <!-- <el-button
            v-if="scope.row.planStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:planManagement:edit']"
            >更新版本</el-button
          >
          <el-button
            v-if="scope.row.planStatus == 1"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:planManagement:remove']"
            >废止</el-button
          > -->
          <el-button
            v-hasPermi="['inspection:planManagement:edit']"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >修改</el-button
          >
          <el-button
            v-hasPermi="['inspection:planManagement:remove']"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      class="pagination"
      @pagination="getList"
    />

    <!-- 添加或修改应急预案管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="预案名称" prop="planName">
          <el-input v-model="form.planName" placeholder="请输入预案名称" />
        </el-form-item>
        <el-form-item label="编号" prop="planNumber">
          <el-input v-model="form.planNumber" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <selectPeopleTree
            v-model="form.projectName"
            :people-list="projectList"
            placeholder="请选择项目名称"
            @change="handleFormProjectChange"
          />
        </el-form-item>
        <el-form-item label="事故类型" prop="accidentType">
          <el-select
            v-model="form.accidentType"
            placeholder="请选择事故类型"
            multiple
            collapse-tags
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.yjjy_sglx"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公司" prop="unitName">
          <el-select
            ref="formCompanySelect"
            v-model="form.unitName"
            placeholder="请选择公司"
            clearable
            style="width: 100%"
            popper-class="org-tree-select-dropdown"
          >
            <el-option
              :value="form.unitName"
              :label="form.unitName"
              style="height: auto; padding: 0; border: none"
            >
              <div class="tree-select-wrapper">
                <el-tree
                  v-loading="companyTreeLoading"
                  :data="companyTreeData"
                  :props="companyTreeProps"
                  highlight-current
                  @node-click="handleFormCompanyNodeClick"
                >
                  <template #default="{ node, data }">
                    <el-tooltip
                      effect="dark"
                      :content="data.label"
                      placement="top"
                    >
                      <span class="el-tree-node__label">
                        {{ node.label }}
                      </span>
                    </el-tooltip>
                  </template>
                </el-tree>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="planType">
          <el-select v-model="form.planType" style="width: 100%">
            <el-option label="应急预案" value="1" />
            <el-option label="现场处置方案" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="上传人" prop="uploadName">
          <el-input v-model="form.uploadName" placeholder="请输入上传人" />
        </el-form-item>
        <el-form-item label="附件" prop="uploadUrl">
          <file-upload v-model="form.uploadUrl" :file-type="fileType" />
        </el-form-item>
        <el-form-item label="上传时间" prop="uploadTime">
          <el-date-picker
            v-model="form.uploadTime"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择上传时间"
          />
        </el-form-item>
        <el-form-item label="状态" prop="planStatus">
          <el-select v-model="form.planStatus" style="width: 100%">
            <el-option label="正常" value="1" />
            <el-option label="废止" value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog
      v-model="attachmentDialogVisible"
      :attachment-list="attachmentList"
    />
  </div>
</template>

<script>
import {
  listPlanManagement,
  getPlanManagement,
  delPlanManagement,
  addPlanManagement,
  updatePlanManagement,
} from "@/api/inspection/planManagement";
import AttachmentDialog from "@/views/components/attchmentDialog.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";
import { querytree, getEnterpriseInfo } from "@/api/system/info";

export default {
  name: "PlanManagement",
  dicts: ["yjjy_sglx"],
  components: {
    AttachmentDialog,
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应急预案管理表格数据
      planManagementList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planName: null,
        projectName: null,
        projectId: null,
        accidentType: [],
        belongingUnit: null,
        unitName: null,
        planStatus: "1", // 默认查询正常状态的预案
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        planName: [
          { required: true, message: "预案名称不能为空", trigger: "blur" },
        ],
        planStatus: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
      // 附件对话框
      attachmentDialogVisible: false,
      attachmentList: [],
      // 文件类型限制
      fileType: ["png", "jpg", "jpeg", "doc", "xls", "pdf"],
      // 项目列表
      projectList: [],
      // 企业信息数据
      companyTreeData: [],
      companyTreeLoading: false,
      companyTreeProps: {
        children: "children",
        label: "label",
        id: "id",
        isLeaf: "isLeaf",
      },
    };
  },
  created() {
    this.getList();
    this.getProjectList();
    this.getCompanyTreeData();
  },
  methods: {
    getCompanyTreeData() {
      this.companyTreeLoading = true;
      getEnterpriseInfo()
        .then((res) => {
          const deptList = res.data;
          this.companyTreeData = [];
          deptList.forEach((item) => {
            this.companyTreeData.push({
              label: item.label,
              id: item.id,
              children: item.children,
            });
          });
          this.companyTreeLoading = false;
        })
        .catch((err) => {
          this.companyTreeLoading = false;
          console.error(err);
        });
    },
    handleQueryCompanyNodeClick(nodeData) {
      if (nodeData && nodeData.label) {
        this.queryParams.unitName = nodeData.label;
        this.queryParams.belongingUnit = nodeData.id;
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.queryCompanySelect) {
            this.$refs.queryCompanySelect.blur();
          }
        });
      }
    },
    handleFormCompanyNodeClick(nodeData) {
      if (nodeData && nodeData.label) {
        this.form.unitName = nodeData.label;
        this.form.belongingUnit = nodeData.id;
        // 选择后关闭下拉框
        this.$nextTick(() => {
          if (this.$refs.formCompanySelect) {
            this.$refs.formCompanySelect.blur();
          }
        });
      }
    },
    getPlanType(type) {
      if (type === 1 || type === "1") {
        return "应急预案";
      } else if (type === 2 || type === "2") {
        return "现场处置方案";
      }
    },
    getPlanStatus(status) {
      if (status === 1 || status === "1") {
        return "正常";
      } else if (status === 2 || status === "2") {
        return "废止";
      }
    },
    /** 查询应急预案管理列表 */
    getList() {
      this.loading = true;
      // 将数组转换为逗号分隔的字符串发送给后端
      const params = { ...this.queryParams };
      // 确保分页时始终传递planStatus=1
      params.planStatus = "1";
      if (
        Array.isArray(params.accidentType) &&
        params.accidentType.length > 0
      ) {
        params.accidentType = params.accidentType.join(",");
      } else if (Array.isArray(params.accidentType)) {
        params.accidentType = null;
      }

      listPlanManagement(params).then((response) => {
        this.planManagementList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        planName: null,
        planNumber: null,
        projectName: null,
        projectId: null,
        accidentType: [],
        belongingUnit: null,
        unitName: null,
        planType: null,
        uploadName: null,
        uploadUrl: null,
        uploadTime: null,
        planStatus: "1", // 设置默认状态为正常
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      // console.log(this.ids);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目级应急预案管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getPlanManagement(id).then((response) => {
        this.form = response.data;
        // 将逗号分隔的字符串转换为数组用于编辑回显
        if (
          this.form.accidentType &&
          typeof this.form.accidentType === "string"
        ) {
          this.form.accidentType = this.form.accidentType
            .split(",")
            .map((item) => item.trim());
        } else if (!this.form.accidentType) {
          this.form.accidentType = [];
        }
        this.open = true;
        this.title = "修改项目级应急预案管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 将数组转换为逗号分隔的字符串发送给后端
          const formData = { ...this.form };
          if (
            Array.isArray(formData.accidentType) &&
            formData.accidentType.length > 0
          ) {
            formData.accidentType = formData.accidentType.join(",");
          } else if (Array.isArray(formData.accidentType)) {
            formData.accidentType = null;
          }

          if (this.form.id != null) {
            updatePlanManagement(formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPlanManagement(formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 废止按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.form.planStatus = 2;
      this.form.id = ids;
      this.$modal
        .confirm('是否确认废止应急预案管理编号为"' + ids + '"的数据项？')
        .then(() => {
          // return delPlanManagement(ids);
          return delPlanManagement(ids).then((response) => {
            this.getList();
            this.$modal.msgSuccess("废止成功");
          });
        })

        .catch(() => {});
    },
    /** 查看附件 */
    handleViewAttachment(value) {
      this.attachmentDialogVisible = true;
      this.attachmentList = value.split(",");
    },
    /** 导出按钮操作 */
    handleExport() {
      // 将数组转换为逗号分隔的字符串用于导出
      const params = { ...this.queryParams };
      // 确保导出时始终传递planStatus=1
      params.planStatus = "1";
      if (
        Array.isArray(params.accidentType) &&
        params.accidentType.length > 0
      ) {
        params.accidentType = params.accidentType.join(",");
      } else if (Array.isArray(params.accidentType)) {
        params.accidentType = null;
      }

      this.download(
        "inspection/planManagement/export",
        params,
        `planManagement_${new Date().getTime()}.xlsx`
      );
    },
    /** 获取项目列表 */
    getProjectList() {
      querytree().then((response) => {
        if (response.code === 200) {
          this.projectList = response.data;
        }
      });
    },
    /** 项目选择变化处理 */
    handleProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.queryParams.projectName = selectedItem.label;
        this.queryParams.projectId = selectedItem.id;
      } else {
        this.queryParams.projectName = null;
        this.queryParams.projectId = null;
      }
    },
    /** 表单项目选择变化处理 */
    handleFormProjectChange(selectedItem) {
      if (selectedItem && selectedItem.type === "general-project") {
        this.form.projectName = selectedItem.label;
        this.form.projectId = selectedItem.id;
      } else {
        this.form.projectName = null;
        this.form.projectId = null;
      }
    },
  },
};
</script>

<style scoped>
::v-deep .org-tree-select-dropdown .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
  width: auto !important;
  min-width: 200px !important;
  max-width: 400px !important;
}

::v-deep .org-tree-select-dropdown .el-select-dropdown__item:hover {
  background-color: transparent !important;
}

::v-deep .org-tree-select-dropdown {
  min-width: 200px !important;
  max-width: 400px !important;
  width: auto !important;
  max-height: 300px !important;
}

/* 树形下拉选择器样式 */
.tree-select-wrapper {
  min-width: 200px;
  max-width: 400px;
  width: auto;
  padding: 8px;
}

/* 企业树形组件样式 */
::v-deep .tree-select-wrapper .el-tree-node__label {
  display: inline-block;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

::v-deep .tree-select-wrapper .el-tree-node__label::-webkit-scrollbar {
  display: none;
}

::v-deep
  .tree-select-wrapper
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}

/* 确保分页固定在底部，参考国外项目信息页面的实现 */
.app-container {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  padding: 20px;
}

/* 搜索表单区域 */
.app-container .el-form {
  flex-shrink: 0;
  margin-bottom: 10px;
}

/* 工具栏区域 */
.app-container .el-row.mb8 {
  flex-shrink: 0;
  margin-bottom: 8px;
}

/* 表格区域 - 占据剩余空间 */
.app-container .el-table {
  flex: 1;
  margin-bottom: 20px;
}

/* 分页样式 - 固定在底部 */
.pagination {
  flex-shrink: 0;
  text-align: center;
  padding: 10px 0;
  margin-top: auto;
  background-color: #fff;
  border-top: 1px solid #ebeef5;
}
</style>
