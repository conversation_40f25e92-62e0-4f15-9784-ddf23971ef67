{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue?vue&type=style&index=0&id=2f6a326a&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue", "mtime": 1757424290727}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLyog5qCH562+6aG15qC35byPICovDQouY29udHJhY3Rvci10YWJzIHsNCiAgbWFyZ2luLWJvdHRvbTogMjBweDsNCn0NCg0KLmNvbnRyYWN0b3ItdGFicyA6OnYtZGVlcCAuZWwtdGFic19faGVhZGVyIHsNCiAgbWFyZ2luOiAwIDAgMTVweDsNCn0NCg0KLmNvbnRyYWN0b3ItdGFicyA6OnYtZGVlcCAuZWwtdGFic19faXRlbSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmNvbnRyYWN0b3ItdGFicyA6OnYtZGVlcCAuZWwtdGFic19faXRlbS5pcy1hY3RpdmUgew0KICBjb2xvcjogIzQwOWVmZjsNCn0NCg0KLyog5pCc57Si6KGo5Y2V5qC35byPICovDQouc2VhcmNoLWZvcm0gew0KICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICBwYWRkaW5nOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMDgpOw0KfQ0KDQouc2VhcmNoLWZvcm0tY29udGVudCB7DQogIG1hcmdpbjogMDsNCn0NCg0KLnNlYXJjaC1mb3JtLWNvbnRlbnQgOjp2LWRlZXAgLmVsLWZvcm0taXRlbSB7DQogIG1hcmdpbi1ib3R0b206IDA7DQogIG1hcmdpbi1yaWdodDogMzJweDsNCn0NCg0KLnNlYXJjaC1mb3JtLWNvbnRlbnQgOjp2LWRlZXAgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBjb2xvcjogIzYwNjI2NjsNCiAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouc2VhcmNoLWZvcm0tY29udGVudCA6OnYtZGVlcCAuZWwtaW5wdXRfX2lubmVyIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi5zZWFyY2gtZm9ybS1jb250ZW50IDo6di1kZWVwIC5lbC1zZWxlY3QgLmVsLWlucHV0X19pbm5lciB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQp9DQoNCi5zZWFyY2gtZm9ybS1jb250ZW50IDo6di1kZWVwIC5lbC1idXR0b24gew0KICBwYWRkaW5nOiA4cHggMjRweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi8qIOm7keWQjeWNleWuueWZqCAqLw0KLmJsYWNrbGlzdC1jb250YWluZXIsDQoucGVyc29ubmVsLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIHBhZGRpbmc6IDI0cHg7DQogIGJvcmRlci1yYWRpdXM6IDZweDsNCiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4wOCk7DQp9DQoNCi8qIOm7keWQjeWNleagh+mimOWSjOaTjeS9nOWMuuWfnyAqLw0KLmJsYWNrbGlzdC1oZWFkZXIsDQoucGVyc29ubmVsLWhlYWRlciB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgcGFkZGluZy1ib3R0b206IDE2cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KfQ0KDQouYmxhY2tsaXN0LXRpdGxlLA0KLnBlcnNvbm5lbC10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgY29sb3I6ICMyNjI2MjY7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZ2FwOiAxNnB4Ow0KfQ0KDQoucmVjb3JkLWNvdW50IHsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzhjOGM4YzsNCiAgZm9udC13ZWlnaHQ6IDQwMDsNCn0NCg0KLmJsYWNrbGlzdC1hY3Rpb25zLA0KLnBlcnNvbm5lbC1hY3Rpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZ2FwOiA4cHg7DQp9DQoNCi5ibGFja2xpc3QtYWN0aW9ucyAuZWwtYnV0dG9uLA0KLnBlcnNvbm5lbC1hY3Rpb25zIC5lbC1idXR0b24gew0KICBwYWRkaW5nOiA2cHggMTZweDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQp9DQoNCi8qIOihqOagvOagt+W8jyAqLw0KLmJsYWNrbGlzdC1jb250YWluZXIgLmVsLXRhYmxlLA0KLnBlcnNvbm5lbC1jb250YWluZXIgLmVsLXRhYmxlIHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZjBmMGYwOw0KfQ0KDQouZWwtdGFibGUgOjp2LWRlZXAgLmVsLXRhYmxlX19oZWFkZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhOw0KfQ0KDQouZWwtdGFibGUgOjp2LWRlZXAgLmVsLXRhYmxlX19oZWFkZXIgdGggew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmFmYWZhICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjNTk1OTU5Ow0KICBmb250LXdlaWdodDogNTAwOw0KICBmb250LXNpemU6IDE0cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOw0KICBoZWlnaHQ6IDQ4cHg7DQp9DQoNCi5lbC10YWJsZSA6OnYtZGVlcCAuZWwtdGFibGVfX2JvZHkgdGQgew0KICBmb250LXNpemU6IDE0cHg7DQogIGNvbG9yOiAjMjYyNjI2Ow0KICBoZWlnaHQ6IDU2cHg7DQp9DQoNCi5lbC10YWJsZSA6OnYtZGVlcCAuZWwtdGFibGVfX2JvZHkgdHI6aG92ZXIgPiB0ZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmEgIWltcG9ydGFudDsNCn0NCg0KLyog54q25oCB5qC35byPICovDQouc3RhdHVzLWFjdGl2ZSB7DQogIGNvbG9yOiAjNjdjMjNhOw0KICBmb250LXdlaWdodDogNTAwOw0KfQ0KDQouc3RhdHVzLWluYWN0aXZlIHsNCiAgY29sb3I6ICNmNTZjNmM7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi8qIOaTjeS9nOaMiemSruagt+W8jyAqLw0KLmVsLXRhYmxlIDo6di1kZWVwIC5lbC1idXR0b24tLXRleHQgew0KICBjb2xvcjogIzQwOWVmZjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmVsLXRhYmxlIDo6di1kZWVwIC5lbC1idXR0b24tLXRleHQ6aG92ZXIgew0KICBjb2xvcjogIzY2YjFmZjsNCn0NCg0KLyog5YiG6aG15qC35byPICovDQoucGFnaW5hdGlvbiB7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogMjRweDsNCn0NCg0KLnBhZ2luYXRpb24gOjp2LWRlZXAgLmVsLXBhZ2luYXRpb24gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5wYWdpbmF0aW9uIDo6di1kZWVwIC5lbC1wYWdpbmF0aW9uX190b3RhbCB7DQogIGNvbG9yOiAjNTk1OTU5Ow0KICBmb250LXNpemU6IDE0cHg7DQp9DQoNCi8qIOaJv+WMheWVhum7keWQjeWNleW8ueeql+agt+W8jyAqLw0KLmNvbnRyYWN0b3ItYmxhY2tsaXN0LWRpYWxvZyA6OnYtZGVlcCAuZWwtZGlhbG9nIHsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5lbC1kaWFsb2dfX2hlYWRlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7DQogIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOWVjZWY7DQogIG1hcmdpbjogMDsNCn0NCg0KLmNvbnRyYWN0b3ItYmxhY2tsaXN0LWRpYWxvZyA6OnYtZGVlcCAuZWwtZGlhbG9nX190aXRsZSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgY29sb3I6ICMyNjI2MjY7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLWRpYWxvZ19fYm9keSB7DQogIHBhZGRpbmc6IDI0cHg7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLWFsZXJ0IHsNCiAgYm9yZGVyLXJhZGl1czogNnB4Ow0KICBib3JkZXI6IDFweCBzb2xpZCAjZmFkYjE0Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmYmU2Ow0KfQ0KDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5lbC1hbGVydF9faWNvbiB7DQogIGNvbG9yOiAjZmE4YzE2Ow0KICBmb250LXNpemU6IDE2cHg7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICBjb2xvcjogIzI2MjYyNjsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KfQ0KDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXIsDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgY29sb3I6ICMyNjI2MjY7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lcjpmb2N1cywNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLXRleHRhcmVhX19pbm5lcjpmb2N1cyB7DQogIGJvcmRlci1jb2xvcjogIzE4OTBmZjsNCiAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjIpOw0KfQ0KDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nDQogIDo6di1kZWVwDQogIC5lbC1zZWxlY3QNCiAgLmVsLWlucHV0LmlzLWZvY3VzDQogIC5lbC1pbnB1dF9faW5uZXIgew0KICBib3JkZXItY29sb3I6ICMxODkwZmY7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmVsLXRleHRhcmVhIC5lbC1pbnB1dF9fY291bnQgew0KICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsNCiAgY29sb3I6ICM4YzhjOGM7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLmNvbnRyYWN0b3ItYmxhY2tsaXN0LWRpYWxvZyA6OnYtZGVlcCAuZGlhbG9nLWZvb3RlciB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KICBwYWRkaW5nOiAxNnB4IDI0cHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNmYWZhZmE7DQogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTllY2VmOw0KICBtYXJnaW46IDAgLTI0cHggLTI0cHggLTI0cHg7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmRpYWxvZy1mb290ZXIgLmVsLWJ1dHRvbiB7DQogIG1hcmdpbi1sZWZ0OiA4cHg7DQogIHBhZGRpbmc6IDhweCAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgZm9udC13ZWlnaHQ6IDUwMDsNCn0NCg0KLmNvbnRyYWN0b3ItYmxhY2tsaXN0LWRpYWxvZyA6OnYtZGVlcCAuZGlhbG9nLWZvb3RlciAuZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogIzE4OTBmZjsNCiAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOw0KfQ0KDQouY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5kaWFsb2ctZm9vdGVyIC5lbC1idXR0b24tLXByaW1hcnk6aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjNDBhOWZmOw0KICBib3JkZXItY29sb3I6ICM0MGE5ZmY7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmRpYWxvZy1mb290ZXIgLmVsLWJ1dHRvbi0tZGVmYXVsdCB7DQogIGJvcmRlci1jb2xvcjogI2Q5ZDlkOTsNCiAgY29sb3I6ICM1OTU5NTk7DQp9DQoNCi5jb250cmFjdG9yLWJsYWNrbGlzdC1kaWFsb2cgOjp2LWRlZXAgLmRpYWxvZy1mb290ZXIgLmVsLWJ1dHRvbi0tZGVmYXVsdDpob3ZlciB7DQogIGJvcmRlci1jb2xvcjogIzQwYTlmZjsNCiAgY29sb3I6ICM0MGE5ZmY7DQp9DQoNCi8qIOWTjeW6lOW8j+iuvuiuoSAqLw0KQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7DQogIC5zZWFyY2gtZm9ybS1jb250ZW50IHsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICB9DQoNCiAgLnNlYXJjaC1mb3JtLWNvbnRlbnQgOjp2LWRlZXAgLmVsLWZvcm0taXRlbSB7DQogICAgbWFyZ2luLXJpZ2h0OiAwOw0KICB9DQoNCiAgLmJsYWNrbGlzdC1jb250YWluZXIgew0KICAgIHBhZGRpbmc6IDE1cHg7DQogIH0NCg0KICAuYmxhY2tsaXN0LWhlYWRlciwNCiAgLnBlcnNvbm5lbC1oZWFkZXIgew0KICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7DQogICAgZ2FwOiAxNXB4Ow0KICB9DQoNCiAgLmJsYWNrbGlzdC1hY3Rpb25zLA0KICAucGVyc29ubmVsLWFjdGlvbnMgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7DQogIH0NCg0KICAuY29udHJhY3Rvci1ibGFja2xpc3QtZGlhbG9nIDo6di1kZWVwIC5lbC1kaWFsb2cgew0KICAgIHdpZHRoOiA5MCUgIWltcG9ydGFudDsNCiAgICBtYXJnaW46IDAgYXV0byAhaW1wb3J0YW50Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqrCA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/contractor/zjContractorBlaklist", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 标签页 -->\r\n    <el-tabs v-model=\"activeTab\" class=\"contractor-tabs\">\r\n      <el-tab-pane label=\"承包商\" name=\"contractor\"></el-tab-pane>\r\n      <el-tab-pane label=\"承包商人员\" name=\"personnel\"></el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <!-- 承包商人员标签页内容 -->\r\n    <div v-show=\"activeTab === 'personnel'\">\r\n      <!-- 人员搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"personnelQueryParams\"\r\n          ref=\"personnelQueryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"所属承包商：\" prop=\"contractorName\">\r\n            <el-select\r\n              v-model=\"personnelQueryParams.contractorName\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in contractorOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.label\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"关键信息：\" prop=\"keyword\">\r\n            <el-input\r\n              v-model=\"personnelQueryParams.keyword\"\r\n              placeholder=\"请输入人员\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handlePersonnelQuery\"\r\n            />\r\n          </el-form-item> -->\r\n\r\n          <el-form-item label=\"人员状态：\" prop=\"personnelStatus\">\r\n            <el-radio-group v-model=\"personnelQueryParams.personnelStatus\">\r\n              <el-radio label=\"0\">在职</el-radio>\r\n              <el-radio label=\"1\">离职</el-radio>\r\n              <el-radio label=\"\">全部</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetPersonnelQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handlePersonnelQuery\"\r\n              >查询</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商人员表格 -->\r\n      <div class=\"personnel-container\">\r\n        <div class=\"personnel-header\">\r\n          <div class=\"personnel-title\">\r\n            承包商人员列表\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedPersonnelRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"personnel-actions\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAddPersonnel\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasPersonnelSelection\"\r\n              @click=\"handleExportPersonnel\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"personnelLoading\"\r\n          :data=\"personnelList\"\r\n          @selection-change=\"handlePersonnelSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"人员姓名\"\r\n            align=\"center\"\r\n            prop=\"personnelName\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"personnelStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span\r\n                :class=\"\r\n                  scope.row.personnelStatus === '0'\r\n                    ? 'status-active'\r\n                    : 'status-inactive'\r\n                \"\r\n              >\r\n                {{ scope.row.personnelStatus === \"0\" ? \"在职\" : \"离职\" }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"联系电话\"\r\n            align=\"center\"\r\n            prop=\"personnelPhone\"\r\n          />\r\n          <el-table-column label=\"身份证号\" align=\"center\" prop=\"idNumber\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatIdNumber(scope.row.idNumber) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"所属承包商\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"150\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleEditPersonnel(scope.row)\"\r\n              >编辑</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDeletePersonnel(scope.row)\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"personnelTotal > 0\"\r\n        :total=\"personnelTotal\"\r\n        :page.sync=\"personnelQueryParams.pageNum\"\r\n        :limit.sync=\"personnelQueryParams.pageSize\"\r\n        @pagination=\"getPersonnelList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 承包商黑名单标签页内容 -->\r\n    <div v-show=\"activeTab === 'contractor'\">\r\n      <!-- 黑名单搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"承包商名称：\" prop=\"contractorName\">\r\n            <el-input\r\n              v-model=\"queryParams.contractorName\"\r\n              placeholder=\"请输入内容\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"管理人：\" prop=\"administratorId\">\r\n            <el-select\r\n              v-model=\"queryParams.administratorId\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in managerOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商黑名单区域 -->\r\n      <div class=\"blacklist-container\">\r\n        <!-- 承包商黑名单标题和操作按钮 -->\r\n        <div class=\"blacklist-header\">\r\n          <div class=\"blacklist-title\">\r\n            承包商黑名单\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"blacklist-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:add']\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasSelection\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:export']\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"zjContractorBlaklistList\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"承包商名称\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"统一社会信用代码\"\r\n            align=\"center\"\r\n            prop=\"creditCode\"\r\n          />\r\n          <el-table-column\r\n            label=\"承包商类型\"\r\n            align=\"center\"\r\n            prop=\"contractorType\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag\r\n                :options=\"dict.type.sys_contractor_type\"\r\n                :value=\"scope.row.contractorType\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"管理人\"\r\n            align=\"center\"\r\n            prop=\"administratorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"负责人\"\r\n            align=\"center\"\r\n            prop=\"contractorManager\"\r\n          />\r\n          <el-table-column\r\n            label=\"黑名单原因\"\r\n            align=\"center\"\r\n            prop=\"blacklistReason\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"100\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleRemoveFromBlacklist(scope.row)\"\r\n                v-hasPermi=\"['contractor:zjContractorBlaklist:remove']\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改承包商黑名单对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      class=\"contractor-blacklist-dialog\"\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商将不能被加入到应用项目中，取消对应项目人员权限。默认承包商管理人进行审批。\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"拉黑承包商\" prop=\"contractorId\" v-if=\"!form.id\">\r\n          <el-select\r\n            v-model=\"form.contractorId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handleContractorSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"contractor in availableContractorList\"\r\n              :key=\"contractor.id\"\r\n              :label=\"contractor.contractorName\"\r\n              :value=\"contractor.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"承包商名称\" prop=\"contractorName\" v-if=\"form.id\">\r\n          <el-input v-model=\"form.contractorName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"form.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改承包商人员对话框 -->\r\n    <el-dialog\r\n      :title=\"personnelTitle\"\r\n      :visible.sync=\"personnelOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"personnelForm\"\r\n        :model=\"personnelForm\"\r\n        :rules=\"personnelRules\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"人员姓名\" prop=\"personnelName\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelName\"\r\n            placeholder=\"请输入人员姓名\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系电话\" prop=\"personnelPhone\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelPhone\"\r\n            placeholder=\"请输入联系电话\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idNumber\">\r\n          <el-input\r\n            v-model=\"personnelForm.idNumber\"\r\n            placeholder=\"请输入身份证号\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"所属承包商\" prop=\"contractorId\">\r\n          <el-select\r\n            v-model=\"personnelForm.contractorId\"\r\n            placeholder=\"请选择承包商\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in contractorOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"人员状态\" prop=\"personnelStatus\">\r\n          <el-radio-group v-model=\"personnelForm.personnelStatus\">\r\n            <el-radio label=\"0\">在职</el-radio>\r\n            <el-radio label=\"1\">离职</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPersonnelForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPersonnel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 加入黑名单对话框 -->\r\n    <el-dialog\r\n      title=\"加入黑名单\"\r\n      :visible.sync=\"blacklistDialogOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        title=\"\"\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商人员将不能被新加入到项目中，并在现有项目中被列为黑名单人员，取消人工权限。默认承包商管理人进行审批\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form\r\n        ref=\"blacklistForm\"\r\n        :model=\"blacklistForm\"\r\n        :rules=\"blacklistRules\"\r\n        label-width=\"130px\"\r\n      >\r\n        <el-form-item label=\"拉黑承包商人员\" prop=\"personnelId\">\r\n          <el-select\r\n            v-model=\"blacklistForm.personnelId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handlePersonnelSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"person in availablePersonnelList\"\r\n              :key=\"person.userId\"\r\n              :label=\"person.nickName\"\r\n              :value=\"person.userId\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"blacklistForm.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBlacklist\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitBlacklistForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjContractorBlaklist,\r\n  getZjContractorBlaklist,\r\n  delZjContractorBlaklist,\r\n  addZjContractorBlaklist,\r\n  updateZjContractorBlaklist,\r\n  getUserInfo,\r\n} from \"@/api/contractor/zjContractorBlaklist\";\r\nimport {\r\n  listZjContractorInfo,\r\n  getZjContractorInfo,\r\n  updateZjContractorInfo,\r\n  getContractorInfo,\r\n} from \"@/api/contractor/zjContractorInfo\";\r\n\r\nexport default {\r\n  name: \"ZjContractorBlaklist\",\r\n  dicts: [\"sys_contractor_type\"],\r\n  data() {\r\n    return {\r\n      // 当前活动标签页\r\n      activeTab: \"contractor\",\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 承包商黑名单表格数据\r\n      zjContractorBlaklistList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 承包商选项\r\n      contractorOptions: [],\r\n      // 可添加到黑名单的承包商列表\r\n      availableContractorList: [],\r\n      // 管理人选项\r\n      managerOptions: [],\r\n      // 承包商人员相关数据\r\n      personnelLoading: true,\r\n      selectedPersonnelRows: [],\r\n      personnelIds: [],\r\n      personnelTotal: 0,\r\n      personnelList: [],\r\n      personnelQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        personnelStatus: \"\",\r\n      },\r\n      // 承包商黑名单查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        administratorId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contractorId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要加入黑名单的承包商\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          { required: true, message: \"请输入黑名单原因\", trigger: \"blur\" },\r\n          { min: 5, message: \"黑名单原因至少需要5个字符\", trigger: \"blur\" },\r\n          { max: 500, message: \"黑名单原因不能超过500个字符\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      // 人员弹窗相关\r\n      personnelOpen: false,\r\n      personnelTitle: \"\",\r\n      personnelForm: {},\r\n      personnelRules: {\r\n        personnelName: [\r\n          { required: true, message: \"人员姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personnelPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        idNumber: [\r\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractorId: [\r\n          { required: true, message: \"请选择所属承包商\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      // 加入黑名单弹窗相关\r\n      blacklistDialogOpen: false,\r\n      blacklistForm: {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      },\r\n      blacklistRules: {\r\n        personnelId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要拉黑的承包商人员\",\r\n            trigger: [\"change\", \"blur\"],\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          {\r\n            required: true,\r\n            message: \"请输入拉黑原因\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            min: 5,\r\n            message: \"拉黑原因至少需要5个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            max: 500,\r\n            message: \"拉黑原因不能超过500个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n        ],\r\n      },\r\n      // 可选择的人员列表（未在黑名单中的人员）\r\n      availablePersonnelList: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否有选中项\r\n    hasSelection() {\r\n      return this.ids.length > 0;\r\n    },\r\n    // 是否有选中的人员项\r\n    hasPersonnelSelection() {\r\n      return this.personnelIds.length > 0;\r\n    },\r\n  },\r\n  created() {\r\n    // 初始化两个标签页的数据\r\n    this.getPersonnelList();\r\n    this.getList();\r\n    // 加载管理人选项\r\n    this.loadManagerOptions();\r\n    // 加载承包商选项\r\n    this.loadContractorOptions();\r\n  },\r\n  methods: {\r\n    /** 初始化承包商人员模拟数据 */\r\n    initPersonnelMockData() {\r\n      this.personnelList = [\r\n        {\r\n          id: 1,\r\n          personnelName: \"张三\",\r\n          personnelStatus: \"0\",\r\n          personnelPhone: \"13800138000\",\r\n          idNumber: \"320123199001011234\",\r\n          contractorName: \"智创机械集团\",\r\n        },\r\n        {\r\n          id: 2,\r\n          personnelName: \"李四\",\r\n          personnelStatus: \"1\",\r\n          personnelPhone: \"13900139000\",\r\n          idNumber: \"320123199002021235\",\r\n          contractorName: \"精工电子设备制造集团\",\r\n        },\r\n      ];\r\n      this.personnelTotal = 2;\r\n      this.personnelLoading = false;\r\n    },\r\n    /** 初始化承包商黑名单模拟数据 */\r\n    initMockData() {\r\n      this.zjContractorBlaklistList = [\r\n        {\r\n          id: 1,\r\n          contractorName: \"广东明华工程有限公司\",\r\n          creditCode: \"91320736617893075\",\r\n          contractorType: \"1\",\r\n          managerName: \"智威科技智慧工厂\",\r\n          responsiblePerson: \"王佳明\",\r\n          blacklistReason: \"1\",\r\n        },\r\n      ];\r\n      this.total = 1;\r\n      this.loading = false;\r\n    },\r\n    /** 查询承包商黑名单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 使用承包商信息接口，传blacklistStatus=2查询黑名单\r\n      const params = {\r\n        ...this.queryParams,\r\n        blacklistStatus: 2,\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          this.zjContractorBlaklistList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据\r\n          this.initMockData();\r\n        });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        contractorId: null,\r\n        contractorName: null,\r\n        creditCode: null,\r\n        contractorType: null,\r\n        managerName: null,\r\n        responsiblePerson: null,\r\n        blacklistReason: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 承包商选择变化处理 */\r\n    handleContractorSelectChange(contractorId) {\r\n      if (contractorId) {\r\n        // 根据选择的承包商ID填充承包商信息\r\n        const selectedContractor = this.availableContractorList.find(\r\n          (contractor) => contractor.id === contractorId\r\n        );\r\n        if (selectedContractor) {\r\n          this.form.contractorName = selectedContractor.contractorName;\r\n          this.form.creditCode = selectedContractor.creditCode;\r\n          this.form.contractorType = selectedContractor.contractorType;\r\n          this.form.managerName = selectedContractor.managerName;\r\n          this.form.responsiblePerson = selectedContractor.responsiblePerson;\r\n        }\r\n      } else {\r\n        // 清空承包商信息\r\n        this.form.contractorName = null;\r\n        this.form.creditCode = null;\r\n        this.form.contractorType = null;\r\n        this.form.managerName = null;\r\n        this.form.responsiblePerson = null;\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 加载管理人选项 */\r\n    loadManagerOptions() {\r\n      // 使用getUserInfo接口获取管理人员数据，传递type=1表示获取管理人员\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.data || response.rows || response || [];\r\n          this.managerOptions = data.map((manager) => ({\r\n            value: manager.userId || manager.id,\r\n            label: manager.nickName || manager.name || manager.userName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取管理人选项失败:\", error);\r\n          this.$modal.msgError(\"获取管理人选项失败\");\r\n          // 失败时使用原有硬编码数据作为备选\r\n          this.managerOptions = [\r\n            { value: \"1\", label: \"王佳明\" },\r\n            { value: \"2\", label: \"李东\" },\r\n            { value: \"3\", label: \"张伟\" },\r\n          ];\r\n        });\r\n    },\r\n    /** 加载承包商选项 */\r\n    loadContractorOptions() {\r\n      // 使用承包商信息查询接口获取所有承包商数据\r\n      const params = {\r\n        pageNum: 1,\r\n        pageSize: 1000, // 获取足够多的数据\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.rows || response.data || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败:\", error);\r\n          // 失败时使用备用接口\r\n          this.loadContractorOptionsFallback();\r\n        });\r\n    },\r\n    /** 备用方法：使用另一个接口加载承包商选项 */\r\n    loadContractorOptionsFallback() {\r\n      // 使用getContractorInfo接口作为备选，参数为null获取所有承包商\r\n      getContractorInfo(null)\r\n        .then((response) => {\r\n          const data = response.data || response.rows || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败（备用接口）:\", error);\r\n          this.$modal.msgError(\"获取承包商选项失败\");\r\n          // 最终失败时使用硬编码数据作为备选\r\n          this.contractorOptions = [\r\n            { value: \"1\", label: \"智创机械集团\" },\r\n            { value: \"2\", label: \"精工电子设备制造集团\" },\r\n          ];\r\n        });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.loadAvailableContractors();\r\n      this.open = true;\r\n      this.title = \"添加承包商黑名单\";\r\n    },\r\n    /** 加载可用的承包商列表（可加入黑名单的承包商） */\r\n    loadAvailableContractors() {\r\n      // 使用新接口获取可加入黑名单的承包商\r\n      getContractorInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.availableContractorList = response.data.map((contractor) => ({\r\n              id: contractor.id,\r\n              contractorName: contractor.contractorName,\r\n              creditCode: contractor.creditCode,\r\n              contractorType: contractor.contractorType,\r\n              managerName: contractor.administratorName,\r\n              responsiblePerson: contractor.legalRepresentative,\r\n            }));\r\n          } else {\r\n            this.availableContractorList = [];\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可用承包商列表失败:\", error);\r\n          this.$modal.msgError(\"获取可用承包商列表失败\");\r\n          // 失败时使用原有接口作为备选\r\n          const params = {\r\n            pageNum: 1,\r\n            pageSize: 1000,\r\n            blacklistStatus: 1,\r\n          };\r\n          listZjContractorInfo(params)\r\n            .then((response) => {\r\n              this.availableContractorList = response.rows || [];\r\n            })\r\n            .catch(() => {\r\n              this.availableContractorList = [];\r\n            });\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      // 使用承包商信息接口获取数据\r\n      getZjContractorInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改承包商黑名单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // 修改黑名单原因\r\n            const updateData = {\r\n              id: this.form.id,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // 新增到黑名单：使用修改承包商信息接口\r\n            const updateData = {\r\n              id: this.form.contractorId,\r\n              blacklistStatus: 2,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData)\r\n              .then((response) => {\r\n                this.$modal.msgSuccess(\"已成功将承包商加入黑名单\");\r\n                this.open = false;\r\n                this.getList();\r\n              })\r\n              .catch((error) => {\r\n                console.error(\"加入黑名单失败:\", error);\r\n                this.$modal.msgError(\"加入黑名单失败，请稍后重试\");\r\n              });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除承包商黑名单编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjContractorBlaklist(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjContractorBlaklist_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n\r\n    /** 移出黑名单操作 */\r\n    handleRemoveFromBlacklist(row) {\r\n      this.$modal\r\n        .confirm('确定要将\"' + row.contractorName + '\"移出黑名单吗？')\r\n        .then(() => {\r\n          // 使用修改承包商信息接口，将blacklistStatus设为1（正常状态）\r\n          const updateData = {\r\n            id: row.id,\r\n            blacklistStatus: 1,\r\n          };\r\n          return updateZjContractorInfo(updateData);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"移出成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n\r\n    // ===== 承包商人员相关方法 =====\r\n    /** 查询承包商人员列表 */\r\n    getPersonnelList() {\r\n      this.personnelLoading = true;\r\n      listZjContractorBlaklist(this.personnelQueryParams)\r\n        .then((response) => {\r\n          this.personnelList = response.rows;\r\n          this.personnelTotal = response.total;\r\n          this.personnelLoading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据作为fallback\r\n          console.warn(\"承包商人员黑名单API调用失败，使用模拟数据\");\r\n          this.initPersonnelMockData();\r\n        });\r\n    },\r\n    /** 人员搜索按钮操作 */\r\n    handlePersonnelQuery() {\r\n      this.personnelQueryParams.pageNum = 1;\r\n      this.getPersonnelList();\r\n    },\r\n    /** 人员重置按钮操作 */\r\n    resetPersonnelQuery() {\r\n      this.resetForm(\"personnelQueryForm\");\r\n      this.handlePersonnelQuery();\r\n    },\r\n    // 人员多选框选中数据\r\n    handlePersonnelSelectionChange(selection) {\r\n      this.selectedPersonnelRows = selection;\r\n      this.personnelIds = selection.map((item) => item.id);\r\n    },\r\n    /** 添加人员到黑名单操作 */\r\n    handleAddPersonnel() {\r\n      this.initAvailablePersonnelList();\r\n      this.blacklistDialogOpen = true;\r\n      // 在下一个tick中重置表单，确保DOM已渲染\r\n      this.$nextTick(() => {\r\n        this.resetBlacklistForm();\r\n      });\r\n    },\r\n    /** 添加承包商人员操作 */\r\n    handleAddNewPersonnel() {\r\n      this.resetPersonnel();\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"添加承包商人员\";\r\n    },\r\n    /** 编辑人员操作 */\r\n    handleEditPersonnel(row) {\r\n      this.resetPersonnel();\r\n      this.personnelForm = {\r\n        id: row.id,\r\n        personnelName: row.personnelName,\r\n        personnelPhone: row.personnelPhone,\r\n        idNumber: row.idNumber,\r\n        contractorId: row.contractorId || \"1\",\r\n        personnelStatus: row.personnelStatus,\r\n      };\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"修改承包商人员\";\r\n    },\r\n    /** 删除人员操作 */\r\n    handleDeletePersonnel(row) {\r\n      this.$modal\r\n        .confirm('确定要删除人员\"' + row.personnelName + '\"吗？')\r\n        .then(() => {\r\n          return delZjContractorBlaklist(row.id);\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          if (error !== \"cancel\") {\r\n            this.$modal.msgError(\"删除失败，请稍后重试\");\r\n          }\r\n        });\r\n    },\r\n    /** 导出人员操作 */\r\n    handleExportPersonnel() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.personnelQueryParams,\r\n        },\r\n        `承包商人员黑名单_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    /** 格式化身份证号 - 中间用*号隐藏 */\r\n    formatIdNumber(idNumber) {\r\n      if (!idNumber) return \"\";\r\n      if (idNumber.length < 8) return idNumber;\r\n      return (\r\n        idNumber.substring(0, 3) +\r\n        \"***********\" +\r\n        idNumber.substring(idNumber.length - 4)\r\n      );\r\n    },\r\n\r\n    // ===== 人员弹窗相关方法 =====\r\n    /** 取消人员弹窗 */\r\n    cancelPersonnel() {\r\n      this.personnelOpen = false;\r\n      this.resetPersonnel();\r\n    },\r\n    /** 重置人员表单 */\r\n    resetPersonnel() {\r\n      this.personnelForm = {\r\n        id: null,\r\n        personnelName: null,\r\n        personnelPhone: null,\r\n        idNumber: null,\r\n        contractorId: null,\r\n        personnelStatus: \"0\",\r\n      };\r\n      this.resetForm(\"personnelForm\");\r\n    },\r\n    /** 提交人员表单 */\r\n    submitPersonnelForm() {\r\n      this.$refs[\"personnelForm\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.personnelForm.id != null) {\r\n            // 修改\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          } else {\r\n            // 新增\r\n            this.$modal.msgSuccess(\"新增成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // ===== 加入黑名单相关方法 =====\r\n    /** 初始化可选择的人员列表 */\r\n    initAvailablePersonnelList() {\r\n      // 调用API获取可拉黑的承包商人员信息\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          this.availablePersonnelList =\r\n            response.data || response.rows || response || [];\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可选择人员列表失败:\", error);\r\n          this.$modal.msgError(\"获取可选择人员列表失败\");\r\n          // 失败时使用现有人员列表作为备选\r\n          this.availablePersonnelList = this.personnelList.filter((person) => {\r\n            return person.personnelStatus === \"0\"; // 只显示在职人员\r\n          });\r\n        });\r\n    },\r\n    /** 重置黑名单表单 */\r\n    resetBlacklistForm() {\r\n      this.blacklistForm = {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      };\r\n      this.$nextTick(() => {\r\n        if (this.$refs.blacklistForm) {\r\n          this.$refs.blacklistForm.resetFields();\r\n          this.$refs.blacklistForm.clearValidate();\r\n        }\r\n      });\r\n    },\r\n    /** 取消黑名单弹窗 */\r\n    cancelBlacklist() {\r\n      this.blacklistDialogOpen = false;\r\n      this.resetBlacklistForm();\r\n    },\r\n    /** 人员选择变化处理 */\r\n    handlePersonnelSelectChange(selectedId) {\r\n      console.log(\"选择的人员ID:\", selectedId);\r\n      // 立即验证personnel字段，如果有值则清除错误\r\n      if (this.$refs.blacklistForm) {\r\n        if (selectedId) {\r\n          this.$refs.blacklistForm.clearValidate(\"personnelId\");\r\n        } else {\r\n          // 如果清空选择，立即触发验证显示错误\r\n          this.$refs.blacklistForm.validateField(\"personnelId\");\r\n        }\r\n      }\r\n    },\r\n    /** 提交黑名单表单 */\r\n    submitBlacklistForm() {\r\n      this.$refs[\"blacklistForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 直接调用添加接口，不需要确认提示\r\n          this.addToBlacklist();\r\n        } else {\r\n          this.$message.warning(\"请检查表单填写是否正确\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    /** 执行加入黑名单操作 */\r\n    addToBlacklist() {\r\n      const selectedPerson = this.availablePersonnelList.find(\r\n        (person) => person.userId === this.blacklistForm.personnelId\r\n      );\r\n\r\n      if (!selectedPerson) {\r\n        this.$modal.msgError(\"请选择要拉黑的人员\");\r\n        return;\r\n      }\r\n\r\n      const requestData = {\r\n        personnelId: this.blacklistForm.personnelId,\r\n        blacklistReason: this.blacklistForm.blacklistReason,\r\n        personnelName: selectedPerson.nickName,\r\n        status: selectedPerson.status,\r\n        personnelPhone: selectedPerson.phonenumber,\r\n        idNumber: selectedPerson.idNumber,\r\n        blacklistState: \"2\",\r\n      };\r\n\r\n      // 调用API将人员加入黑名单\r\n      addZjContractorBlaklist(requestData)\r\n        .then((response) => {\r\n          this.$modal.msgSuccess(\r\n            `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n          );\r\n          this.blacklistDialogOpen = false;\r\n          this.resetBlacklistForm();\r\n          // 刷新人员列表\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          // 如果API调用失败，使用模拟数据响应\r\n          console.warn(\"API调用失败，使用模拟响应:\", error);\r\n          setTimeout(() => {\r\n            this.$modal.msgSuccess(\r\n              `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n            );\r\n            this.blacklistDialogOpen = false;\r\n            this.resetBlacklistForm();\r\n            // 刷新人员列表\r\n            this.getPersonnelList();\r\n          }, 500);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 标签页样式 */\r\n.contractor-tabs {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__header {\r\n  margin: 0 0 15px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item.is-active {\r\n  color: #409eff;\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form-content {\r\n  margin: 0;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item {\r\n  margin-bottom: 0;\r\n  margin-right: 32px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item__label {\r\n  color: #606266;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-select .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n}\r\n\r\n.search-form-content ::v-deep .el-button {\r\n  padding: 8px 24px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 黑名单容器 */\r\n.blacklist-container,\r\n.personnel-container {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n/* 黑名单标题和操作区域 */\r\n.blacklist-header,\r\n.personnel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.blacklist-title,\r\n.personnel-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.record-count {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  font-weight: 400;\r\n}\r\n\r\n.blacklist-actions,\r\n.personnel-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.blacklist-actions .el-button,\r\n.personnel-actions .el-button {\r\n  padding: 6px 16px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格样式 */\r\n.blacklist-container .el-table,\r\n.personnel-container .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header th {\r\n  background-color: #fafafa !important;\r\n  color: #595959;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  height: 48px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body td {\r\n  font-size: 14px;\r\n  color: #262626;\r\n  height: 56px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body tr:hover > td {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-active {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-inactive {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.el-table ::v-deep .el-button--text {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-table ::v-deep .el-button--text:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination {\r\n  text-align: center;\r\n  margin-top: 24px;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination {\r\n  text-align: center;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination__total {\r\n  color: #595959;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 承包商黑名单弹窗样式 */\r\n.contractor-blacklist-dialog ::v-deep .el-dialog {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__header {\r\n  background-color: #f8f9fa;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  margin: 0;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert {\r\n  border-radius: 6px;\r\n  border: 1px solid #fadb14;\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert__icon {\r\n  color: #fa8c16;\r\n  font-size: 16px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner:focus,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner:focus {\r\n  border-color: #1890ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.contractor-blacklist-dialog\r\n  ::v-deep\r\n  .el-select\r\n  .el-input.is-focus\r\n  .el-input__inner {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-textarea .el-input__count {\r\n  background: transparent;\r\n  color: #8c8c8c;\r\n  font-size: 12px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer {\r\n  text-align: right;\r\n  padding: 16px 24px;\r\n  background-color: #fafafa;\r\n  border-top: 1px solid #e9ecef;\r\n  margin: 0 -24px -24px -24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button {\r\n  margin-left: 8px;\r\n  padding: 8px 24px;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary:hover {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default {\r\n  border-color: #d9d9d9;\r\n  color: #595959;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default:hover {\r\n  border-color: #40a9ff;\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-content {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form-content ::v-deep .el-form-item {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .blacklist-container {\r\n    padding: 15px;\r\n  }\r\n\r\n  .blacklist-header,\r\n  .personnel-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .blacklist-actions,\r\n  .personnel-actions {\r\n    width: 100%;\r\n    justify-content: flex-end;\r\n  }\r\n\r\n  .contractor-blacklist-dialog ::v-deep .el-dialog {\r\n    width: 90% !important;\r\n    margin: 0 auto !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}