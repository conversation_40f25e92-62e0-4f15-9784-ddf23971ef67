<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="68px"
    >
      <!-- <el-form-item label="项目ID" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="检查人id" prop="creatorId">
        <el-input
          v-model="queryParams.creatorId"
          placeholder="请输入检查人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="所属公司" prop="unitName">
        <el-select
          ref="queryCompanySelect"
          v-model="queryParams.unitName"
          placeholder="请选择所属公司"
          clearable
          style="width: 180px"
          popper-class="org-tree-select-dropdown"
        >
          <el-option
            :value="queryParams.unitName"
            :label="queryParams.unitName"
            style="height: auto; padding: 0; border: none"
          >
            <div class="tree-select-wrapper">
              <el-tree
                v-loading="companyTreeLoading"
                :data="companyTreeData"
                :props="companyTreeProps"
                highlight-current
                @node-click="handleQueryCompanyNodeClick"
              >
                <template #default="{ node, data }">
                  <el-tooltip
                    effect="dark"
                    :content="data.label"
                    placement="top"
                  >
                    <span class="el-tree-node__label">
                      {{ node.label }}
                    </span>
                  </el-tooltip>
                </template>
              </el-tree>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="常规id" prop="routineId">
        <el-input
          v-model="queryParams.routineId"
          placeholder="请输入常规id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="记录id" prop="recordId">
        <el-input
          v-model="queryParams.recordId"
          placeholder="请输入记录id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任区域ID" prop="regionId">
        <el-input
          v-model="queryParams.regionId"
          placeholder="请输入责任区域ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="责任区域全id" prop="regionFullId">
        <el-input
          v-model="queryParams.regionFullId"
          placeholder="请输入责任区域全id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域名称" prop="regionName">
        <el-input
          v-model="queryParams.regionName"
          placeholder="请输入区域名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区域全称" prop="regionFullName">
        <el-input
          v-model="queryParams.regionFullName"
          placeholder="请输入区域全称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患类别id" prop="dangerTypeId">
        <el-input
          v-model="queryParams.dangerTypeId"
          placeholder="请输入隐患类别id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="关系" prop="relation">
        <el-input
          v-model="queryParams.relation"
          placeholder="请输入关系"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="隐患类别" prop="dangerTypeName">
        <el-input
          v-model="queryParams.dangerTypeName"
          placeholder="请输入隐患类别名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     <el-form-item label="隐患类别全称" prop="dangerTypeFullName">
        <el-input
          v-model="queryParams.dangerTypeFullName"
          placeholder="请输入隐患类别全称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="auto_recg" prop="autoRecg">
        <el-input
          v-model="queryParams.autoRecg"
          placeholder="请输入auto_recg"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
       <el-form-item label="备注" prop="dangerDesc">
        <el-input
          v-model="queryParams.dangerDesc"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="整改时间" prop="changeTime">
        <el-date-picker
          clearable
          v-model="queryParams.changeTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择整改时间"
        >
        </el-date-picker>
      </el-form-item>-->
      <!-- <el-form-item label="整改时限(天)" prop="changeLimitTime">
        <el-date-picker
          clearable
          v-model="queryParams.changeLimitTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择整改时限(天)"
        >
        </el-date-picker>
      </el-form-item> -->
      <!-- <el-form-item label="级别" prop="level">
        <el-input
          v-model="queryParams.level"
          placeholder="请输入级别"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="级别名称" prop="levelName">
        <el-input
          v-model="queryParams.levelName"
          placeholder="请输入级别名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     <el-form-item label="整改人id" prop="changeId">
        <el-input
          v-model="queryParams.changeId"
          placeholder="请输入整改人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="参与人id" prop="participationIds">
        <el-input
          v-model="queryParams.participationIds"
          placeholder="请输入参与人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="参与人" prop="participationNames">
        <el-input
          v-model="queryParams.participationNames"
          placeholder="请输入参与人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- 检查结果 -->
      <el-form-item label="检查结果" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择检查结果"
          clearable
          @change="handleQuery"
        >
          <el-option
            v-for="item in checkResultOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjQualityInspectionInfo:add']" type="primary" plain icon="el-icon-plus"
          size="mini" @click="handleAdd">新增</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['inspection:zjQualityInspectionInfo:edit']"
          >修改</el-button
        >
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjQualityInspectionInfo:remove']"
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['inspection:zjQualityInspectionInfo:export']"
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="zjQualityInspectionInfoList"
      height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="left" />
      <el-table-column label="序号" width="55" align="left">
        <template slot-scope="scope">
          {{
            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1
          }}
        </template>
      </el-table-column>
      <!-- 检查结果/截止时限 -->
      <el-table-column label="检查结果/截止时限" align="left" width="200">
        <template slot-scope="{ row }">
          <div class="font-12">
            <span class="circle" :class="getStatusClass(row.status)" />
            {{ getCheckResultText(row.status) }}
          </div>
          <div class="font-12">
            复查时限:{{
              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : ""
            }}
          </div>
        </template>
      </el-table-column>
      <!-- 所属公司 -->
      <el-table-column
        label="所属公司"
        align="left"
        prop="ownerOrgStr"
        width="160"
        show-overflow-tooltip
      />

      <el-table-column
        label="项目名称"
        align="left"
        prop="projectName"
        width="300"
        show-overflow-tooltip
      />
      <el-table-column
        label="检查人/检查时间"
        align="left"
        prop="creatorName"
        width="140"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div class="font-12">
            {{ scope.row.creatorName }}
          </div>
          <div class="font-12">
            {{ scope.row.createTime }}
          </div>
          <!-- {{ scope.row.creatorName + "/" + scope.row.createTime }} -->
        </template>
      </el-table-column>
      <!-- 质量问题信息 -->
      <el-table-column
        label="质量问题信息"
        align="left"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <!-- levelName dangerTypeFullName dangerDesc  -->
          <el-tag type="primary"> {{ scope.row.levelName }}</el-tag>

          {{ scope.row.dangerTypeFullName + "," + scope.row.dangerDesc }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="状态" align="left" prop="projectStatus" /> -->
      <!-- <el-table-column
        label="区域名称"
        align="left"
        prop="regionName"
        width="300"
        show-overflow-tooltip
      /> -->
      <!-- <el-table-column
        label="隐患类别名称"
        align="left"
        width="120"
        prop="dangerTypeName"
      /> -->
      <!-- <el-table-column
        label="整改时间"
        align="left"
        prop="changeTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.changeTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column> -->

      <el-table-column label="整改人" align="left" prop="changeName" />
      <!-- <el-table-column label="参与人" align="left" prop="participationNames" /> -->
      <!-- 复查人 -->
      <el-table-column label="复查人" align="left" prop="reviewName" />
      <!-- 核验人 -->
      <el-table-column label="核验人" align="left" prop="verifyManName" />
      <!-- 例行检查 -->
      <el-table-column label="例行检查" align="left" prop="routineCheckNames" />
      <!-- 说明 -->
      <el-table-column
        label="说明"
        align="left"
        prop="remark"
        show-overflow-tooltip
      />
      <!-- <el-table-column label="项目ID" align="left" prop="projectId" />
      <el-table-column label="检查人id" align="left" prop="creatorId" />

      <el-table-column label="备注" align="left" prop="remark" />
      <el-table-column label="常规id" align="left" prop="routineId" />
      <el-table-column label="记录id" align="left" prop="recordId" />
      <el-table-column label="责任区域ID" align="left" prop="regionId" />
      <el-table-column
        label="责任区域全id"
        align="left"
        prop="regionFullId"
      />
      <el-table-column label="区域全称" align="left" prop="regionFullName" />
      <el-table-column label="关系" align="left" prop="relation" />
      <el-table-column label="隐患类别id" align="left" prop="dangerTypeId" />

      <el-table-column
        label="隐患类别全称"
        align="left"
        prop="dangerTypeFullName"
      />
      <el-table-column label="auto_recg" align="left" prop="autoRecg" />
      <el-table-column
        label="隐患类目内容"
        align="left"
        prop="dangerItemContent"
      />
      <el-table-column label="备注" align="left" prop="dangerDesc" />

      <el-table-column
        label="整改时限(天)"
        align="left"
        prop="changeLimitTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.changeLimitTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="级别" align="left" prop="level" />
      <el-table-column label="级别名称" align="left" prop="levelName" />
      <el-table-column label="状态" align="left" prop="status" />
      <el-table-column label="整改人id" align="left" prop="changeId" />
      <el-table-column
        label="参与人id"
        align="left"
        prop="participationIds"
      /> -->

      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
        fixed="right"
        width="150"
      >
        <template slot-scope="scope">
          <!-- 查看详情 -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            >详情</el-button
          >
          <!-- <el-button size="mini" type="text">打印</el-button> -->

          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['inspection:zjQualityInspectionInfo:edit']"
            >修改</el-button
          > -->
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['inspection:zjQualityInspectionInfo:remove']"
            >删除</el-button
          > -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 详情弹窗 -->
    <el-dialog
      title="查看详情"
      :visible.sync="detailDialog"
      width="1000px"
      append-to-body
      custom-class="detail-dialog"
    >
      <div v-if="detailData" class="detail-content">
        <!-- 问题记录部分 -->
        <div class="independent-section">
          <h3 class="independent-title">问题记录</h3>
          <!-- 左右两列布局 -->
          <div class="record-columns">
            <!-- 左列 -->
            <div class="left-column">
              <div class="field-row highlighted-field">
                <span class="field-label">检查部位:</span>
                <span class="field-value">{{
                  detailData.regionName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题描述:</span>
                <span class="field-value">{{
                  detailData.dangerItemContent || detailData.dangerDesc || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题分类:</span>
                <span class="field-value">{{
                  detailData.dangerTypeFullName ||
                  detailData.dangerTypeName ||
                  "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">补充说明:</span>
                <span class="field-value">{{
                  detailData.dangerDesc || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">问题等级:</span>
                <span class="field-value">{{
                  detailData.levelName || detailData.level || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">整改要求:</span>
                <span class="field-value">{{
                  detailData.rectificationRequirements || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">轴线位置:</span>
                <span class="field-value">{{
                  detailData.axisPosition || "8号18-17/D"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">分包单位:</span>
                <span class="field-value">{{
                  detailData.contractorName || "-"
                }}</span>
              </div>
            </div>

            <!-- 右列 -->
            <div class="right-column">
              <div class="field-row highlighted-field">
                <span class="field-label">检查人:</span>
                <span class="field-value">{{
                  detailData.creatorName || "-"
                }}</span>
              </div>

              <div class="field-row highlighted-field">
                <span class="field-label">检查时间:</span>
                <span class="field-value">{{
                  detailData.createTime || "2025-08-25"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">紧急程度:</span>
                <span class="field-value">{{
                  detailData.urgencyLevel || "一般"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">检查人:</span>
                <span class="field-value">{{
                  detailData.creatorName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">检查时间:</span>
                <span class="field-value">{{
                  detailData.createTime
                    ? detailData.createTime.slice(0, 10)
                    : "2025-08-25"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">整改时限:</span>
                <span class="field-value">{{
                  detailData.changeLimitTime
                    ? detailData.changeLimitTime.slice(0, 10)
                    : "2025-08-27"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">复查人:</span>
                <span class="field-value">{{
                  detailData.reviewName || "-"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">复查时间:</span>
                <span class="field-value">{{
                  detailData.reviewTime
                    ? detailData.reviewTime.slice(0, 10)
                    : "2025-08-30"
                }}</span>
              </div>

              <div class="field-row">
                <span class="field-label">通知人:</span>
                <span class="field-value">{{
                  detailData.notifierName || "-"
                }}</span>
              </div>
            </div>
          </div>

          <!-- 现状整改部分 -->
          <div class="field-row">
            <span class="field-label">现状整改:</span>
            <div class="status-row">
              <label
                ><input
                  type="checkbox"
                  :checked="detailData.status !== '1'"
                  disabled
                />
                未完成</label
              >
            </div>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div v-if="detailData.hiddenDangerPictures" class="photo-container">
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.hiddenDangerPictures
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="问题照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 整改记录部分 - 待整改状态(status=1)时不显示 -->
        <div v-if="detailData.status !== '1'" class="independent-section">
          <h3 class="independent-title">整改记录</h3>
          <!-- 整改信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">整改状态:</span>
              <span class="field-value">{{
                getRectificationStatusText(detailData.status)
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改人:</span>
              <span class="field-value">{{
                detailData.changeName || "-"
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">整改时间:</span>
              <span class="field-value">{{
                detailData.changeTime
                  ? detailData.changeTime.slice(0, 16)
                  : "2025-08-27"
              }}</span>
            </div>
          </div>

          <!-- 整改说明 -->
          <div class="field-row full-width">
            <span class="field-label">整改说明:</span>
            <span class="field-value">{{
              detailData.rectificationDesc || "已整改"
            }}</span>
          </div>

          <!-- 相关照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div
              v-if="detailData.rectificationPictures"
              class="photo-container"
            >
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.rectificationPictures
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="整改照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 复查记录部分 - 待整改(status=1)和待复查(status=2)状态时不显示 -->
        <div
          v-if="detailData.status !== '1' && detailData.status !== '2'"
          class="independent-section"
        >
          <h3 class="independent-title">复查记录</h3>
          <!-- 复查信息行 -->
          <div class="rectification-info">
            <div class="field-row highlighted-field">
              <span class="field-label">复查状态:</span>
              <span class="field-value">{{
                getReviewStatusText(detailData.status)
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查人:</span>
              <span class="field-value">{{
                detailData.reviewName || "-"
              }}</span>
            </div>

            <div class="field-row highlighted-field">
              <span class="field-label">复查时间:</span>
              <span class="field-value">{{
                detailData.reviewTime
                  ? detailData.reviewTime.slice(0, 16)
                  : "2025-08-27"
              }}</span>
            </div>
          </div>

          <!-- 复查说明 -->
          <div class="field-row full-width">
            <span class="field-label">复查说明:</span>
            <span class="field-value">{{
              detailData.reviewComments || "-"
            }}</span>
          </div>

          <!-- 复查照片 -->
          <div class="photo-section">
            <div class="field-row">
              <span class="field-label">相关附件:</span>
            </div>
            <div v-if="detailData.reviewPicUrl" class="photo-container">
              <div
                v-for="(imageUrl, index) in getAllImageUrls(
                  detailData.reviewPicUrl
                )"
                :key="index"
                class="photo-item"
              >
                <img
                  :src="imageUrl"
                  alt="复查照片"
                  @click="previewImage(imageUrl)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加或修改质量检查台账对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主键ID" prop="id">
              <el-input v-model="form.id" placeholder="请输入主键ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目ID" prop="projectId">
              <el-input v-model="form.projectId" placeholder="请输入项目ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检查人id" prop="creatorId">
              <el-input v-model="form.creatorId" placeholder="请输入检查人id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="检查人姓名" prop="creatorName">
              <el-input
                v-model="form.creatorName"
                placeholder="请输入检查人姓名"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="常规id" prop="routineId">
              <el-input v-model="form.routineId" placeholder="请输入常规id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="form.projectName"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="记录id" prop="recordId">
              <el-input v-model="form.recordId" placeholder="请输入记录id" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="责任区域ID" prop="regionId">
              <el-input
                v-model="form.regionId"
                placeholder="请输入责任区域ID"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任区域全id" prop="regionFullId">
              <el-input
                v-model="form.regionFullId"
                placeholder="请输入责任区域全id"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="区域名称" prop="regionName">
              <el-input
                v-model="form.regionName"
                placeholder="请输入区域名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="区域全称" prop="regionFullName">
              <el-input
                v-model="form.regionFullName"
                placeholder="请输入区域全称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患类别id" prop="dangerTypeId">
              <el-input
                v-model="form.dangerTypeId"
                placeholder="请输入隐患类别id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关系" prop="relation">
              <el-input v-model="form.relation" placeholder="请输入关系" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="隐患类别名称" prop="dangerTypeName">
              <el-input
                v-model="form.dangerTypeName"
                placeholder="请输入隐患类别名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="隐患类别全称" prop="dangerTypeFullName">
              <el-input
                v-model="form.dangerTypeFullName"
                placeholder="请输入隐患类别全称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="auto_recg" prop="autoRecg">
              <el-input v-model="form.autoRecg" placeholder="请输入auto_recg" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注" prop="dangerDesc">
              <el-input v-model="form.dangerDesc" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="隐患类目内容">
              <editor v-model="form.dangerItemContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="整改时间" prop="changeTime">
              <el-date-picker
                v-model="form.changeTime"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择整改时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改时限(天)" prop="changeLimitTime">
              <el-date-picker
                v-model="form.changeLimitTime"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择整改时限(天)"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="级别" prop="level">
              <el-input v-model="form.level" placeholder="请输入级别" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="级别名称" prop="levelName">
              <el-input v-model="form.levelName" placeholder="请输入级别名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="整改人id" prop="changeId">
              <el-input v-model="form.changeId" placeholder="请输入整改人id" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="整改人名称" prop="changeName">
              <el-input
                v-model="form.changeName"
                placeholder="请输入整改人名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="参与人id" prop="participationIds">
          <el-input
            v-model="form.participationIds"
            placeholder="请输入参与人id"
          />
        </el-form-item> -->
        <!-- <el-form-item label="参与人姓名" prop="participationNames">
          <el-input
            v-model="form.participationNames"
            placeholder="请输入参与人姓名"
          />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjQualityInspectionInfo,
  getZjQualityInspectionInfo,
  delZjQualityInspectionInfo,
  addZjQualityInspectionInfo,
  updateZjQualityInspectionInfo,
} from "@/api/inspection/zjQualityInspectionInfo";
import { getEnterpriseInfo } from "@/api/system/info";

export default {
  name: "ZjQualityInspectionInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质量检查台账表格数据
      zjQualityInspectionInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        creatorId: null,
        unitName: null,
        routineId: null,
        projectName: null,
        projectStatus: null,
        recordId: null,
        regionId: null,
        regionFullId: null,
        regionName: null,
        regionFullName: null,
        dangerTypeId: null,
        relation: null,
        dangerTypeName: null,
        dangerTypeFullName: null,
        autoRecg: null,
        dangerItemContent: null,
        dangerDesc: null,
        changeTime: null,
        changeLimitTime: null,
        level: null,
        levelName: null,
        status: null,
        changeId: null,
        participationIds: null,
        participationNames: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        id: [{ required: true, message: "主键ID不能为空", trigger: "blur" }],
      },
      // 详情弹窗
      detailDialog: false,
      // 详情数据
      detailData: {},
      // 公司树相关数据
      companyTreeLoading: false,
      companyTreeData: [],
      companyTreeProps: {
        children: "children",
        label: "label",
      },
      // 检查结果选择项
      checkResultOptions: [
        {
          label: "无需整改",
          value: 0,
        },
        {
          label: "待整改",
          value: 1,
        },
        {
          label: "已整改",
          value: 2,
        },
        {
          label: "已合格",
          value: 3,
        },
        {
          label: "不合格",
          value: 4,
        },
        {
          label: "待核验",
          value: 7,
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getCompanyTreeData();
  },
  methods: {
    getStatusClass(status) {
      const statusMap = {
        0: "bg-blue", // 无需整改
        1: "bg-orange", // 待整改
        2: "bg-green", // 已整改
        3: "bg-green", // 已合格
        4: "bg-orange", // 不合格
        7: "bg-blue", // 待核验
      };
      return statusMap[status] || "bg-blue";
    },
    // 获取检查结果文字
    getCheckResultText(status) {
      const statusMap = {
        0: "无需整改",
        1: "待整改",
        2: "已整改",
        3: "已合格",
        4: "不合格",
        7: "待核验",
      };
      return statusMap[status] || "未知状态";
    },
    /** 查询质量检查台账列表 */
    getList() {
      this.loading = true;
      listZjQualityInspectionInfo(this.queryParams).then((response) => {
        this.zjQualityInspectionInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        projectId: null,
        createTime: null,
        creatorId: null,
        updateTime: null,
        remark: null,
        routineId: null,
        projectName: null,
        projectStatus: null,
        recordId: null,
        regionId: null,
        regionFullId: null,
        regionName: null,
        regionFullName: null,
        dangerTypeId: null,
        relation: null,
        dangerTypeName: null,
        dangerTypeFullName: null,
        autoRecg: null,
        dangerItemContent: null,
        dangerDesc: null,
        changeTime: null,
        changeLimitTime: null,
        level: null,
        levelName: null,
        status: null,
        changeId: null,
        participationIds: null,
        participationNames: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加质量检查台账";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjQualityInspectionInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改质量检查台账";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjQualityInspectionInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjQualityInspectionInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除质量检查台账编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjQualityInspectionInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjQualityInspectionInfo/export",
        {
          ...this.queryParams,
        },
        `zjQualityInspectionInfo_${new Date().getTime()}.xlsx`
      );
    },
    // 查看详情
    handleDetail(row) {
      const id = row.id;
      getZjQualityInspectionInfo(id).then((response) => {
        this.detailData = response.data;
        this.detailDialog = true;
      });
    },
    // 获取整改状态文字
    getRectificationStatusText(status) {
      const statusMap = {
        0: "无需整改",
        1: "待整改",
        2: "已整改",
        3: "已合格",
        4: "不合格",
        7: "待核验",
      };
      return statusMap[status] || "未知状态";
    },
    // 获取复查状态文字
    getReviewStatusText(status) {
      const statusMap = {
        0: "无需复查",
        1: "待复查",
        2: "待复查",
        3: "复查合格",
        4: "复查不合格",
        7: "待核验",
      };
      return statusMap[status] || "未复查";
    },
    // 解析图片URL列表
    parseImageUrls(imageUrl, dataSource) {
      if (!imageUrl) return [];

      // 当dataSource为2时，处理后端拼接好的URL
      if (dataSource === 2 || dataSource === "2") {
        // 移除开头的@符号，然后按逗号分割
        const urlStr = imageUrl.startsWith("@")
          ? imageUrl.substring(1)
          : imageUrl;
        return urlStr
          .split(",")
          .filter((url) => url.trim())
          .map((url) => url.trim());
      }

      // 默认情况：前端拼接
      if (imageUrl.startsWith("/")) {
        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`];
      }
      return [imageUrl];
    },
    // 获取图片URL（兼容原有逻辑）
    getImageUrl(imageUrl) {
      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource);
      return urls.length > 0 ? urls[0] : "";
    },
    // 获取所有图片URL
    getAllImageUrls(imageUrl) {
      return this.parseImageUrls(imageUrl, this.detailData?.dataSource);
    },
    // 预览图片
    previewImage(imageUrl) {
      if (!imageUrl) return;
      // 使用element-ui的图片预览功能
      this.$alert(
        `<img src="${imageUrl}" style="width: 100%; max-width: 500px;" alt="预览图片">`,
        "图片预览",
        {
          dangerouslyUseHTMLString: true,
          customClass: "image-preview-dialog",
          showConfirmButton: false,
          showCancelButton: true,
          cancelButtonText: "关闭",
        }
      );
    },
    // 处理公司节点点击
    handleQueryCompanyNodeClick(data) {
      this.queryParams.unitName = data.label;
      this.$refs.queryCompanySelect.blur();
    },
    // 获取公司树数据
    getCompanyTreeData() {
      this.companyTreeLoading = true;
      getEnterpriseInfo()
        .then((res) => {
          const deptList = res.data;
          this.companyTreeData = [];
          deptList.forEach((item) => {
            this.companyTreeData.push({
              label: item.label,
              id: item.id,
              children: item.children,
            });
          });
          this.companyTreeLoading = false;
        })
        .catch((err) => {
          this.companyTreeLoading = false;
          console.error(err);
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.font-12 {
  font-size: 12px;
}

.circle {
  width: 8px;
  height: 8px;
  display: inline-block;
  border-radius: 50%;
  margin-right: 2px;
}

.bg-orange {
  background-color: #ffa500;
}

.bg-blue {
  background-color: #007bff;
}

.bg-green {
  background-color: #28a745;
}

/* 详情弹窗样式 */
:deep(.detail-dialog) {
  .el-dialog__header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
    padding: 15px 20px;
  }

  .el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .el-dialog__body {
    padding: 0;
    max-height: 60vh;
    overflow-y: auto;
    padding: 0px 20px !important;
  }

  ::v-deep(.el-dialog__body) {
    padding: 0px 20px !important;
  }
}

.detail-content {
  padding: 0 20px;

  .independent-section {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .independent-title {
    margin: 0 0 20px 0;
    padding: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 20px;

    &:after {
      content: "";
      flex: 1;
      height: 2px;
      border-top: 2px dashed #409eff;
    }
  }

  // 左右两列布局
  .record-columns {
    display: flex;
    gap: 40px;
  }

  .left-column,
  .right-column {
    flex: 1;
  }

  .field-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &.full-width {
      width: 100%;
    }

    &:last-child {
      margin-bottom: 0;
    }

    &.highlighted-field {
      background-color: #ebecf0;
      padding: 8px 0;
      margin-bottom: 0;
      margin-left: -20px;
      margin-right: -20px;
      padding-left: 20px;
      padding-right: 20px;
    }
  }

  // 非高亮字段与高亮字段之间的间距
  .field-row:not(.highlighted-field) + .field-row.highlighted-field,
  .field-row.highlighted-field + .field-row:not(.highlighted-field) {
    margin-top: 16px;
  }

  .field-label {
    min-width: 70px;
    font-weight: 400;
    color: #666;
    margin-right: 10px;
    white-space: nowrap;
    font-size: 14px;
    line-height: 1.5;
  }

  .field-value {
    color: #333;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
    flex: 1;

    &.status-tag {
      padding: 2px 8px;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 500;
    }
  }

  // 整改记录部分样式
  .rectification-info {
    display: flex;
    gap: 0;
    margin-bottom: 16px;
    background-color: #ebecf0;
    margin-left: -20px;
    margin-right: -20px;
    padding: 8px 20px;

    .field-row {
      margin-bottom: 0;
      white-space: nowrap;
      flex: 1;
      margin-left: 0;
      margin-right: 0;
      padding: 0 20px;
      background-color: transparent;

      &:first-child {
        padding-left: 0;
      }

      &:last-child {
        padding-right: 0;
      }
    }
  }

  // 状态标签颜色
  .status-no-need {
    background-color: #f0f9ff;
    color: #0369a1;
    border: 1px solid #7dd3fc;
  }

  .status-pending {
    background-color: transparent;
    color: #d97706;
    border: none;
  }

  .status-rectified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-qualified {
    background-color: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
  }

  .status-unqualified {
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
  }

  .status-verify {
    background-color: #ede9fe;
    color: #7c3aed;
    border: 1px solid #a78bfa;
  }

  .status-unknown {
    background-color: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
  }

  // 状态行样式
  .status-row {
    margin-top: 8px;

    label {
      margin-right: 15px;
      color: #666;
      font-size: 14px;

      input[type="checkbox"] {
        margin-right: 5px;
      }
    }
  }

  // 照片相关样式
  .photo-section {
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
  }

  .photo-container {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .photo-item,
  .photo-placeholder {
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s;
    background: #fafafa;

    &:hover {
      transform: scale(1.02);
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    img {
      width: 120px;
      height: 90px;
      object-fit: cover;
      display: block;
    }
  }

  .photo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border: 1px dashed #d1d5db;

    &:hover {
      transform: none;
      border-color: #d1d5db;
      box-shadow: none;
    }

    img {
      opacity: 0.3;
      background: transparent;
    }
  }

  .no-photo {
    color: #999;
    font-style: italic;
    font-size: 12px;
    margin-top: 8px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    .record-columns {
      flex-direction: column;
      gap: 20px;
    }

    .rectification-info {
      flex-direction: column;
      gap: 10px;
    }
  }
}

// 图片预览弹窗样式
:deep(.image-preview-dialog) {
  .el-message-box__content {
    text-align: center;
  }

  .el-message-box__message {
    margin: 0;
  }
}

// 公司筛选下拉框样式
.tree-select-wrapper {
  padding: 8px;
  min-height: 200px;
  max-height: 300px;
  overflow: auto;
}

:deep(.org-tree-select-dropdown) {
  .el-select-dropdown__item {
    height: auto;
    max-height: 300px;
    padding: 0;
    overflow: hidden;
    overflow-y: auto;
  }

  .el-tree-node__content {
    height: 32px;
    line-height: 32px;
    font-size: 14px;
  }

  .el-tree-node__label {
    font-size: 14px;
  }
}
</style>
