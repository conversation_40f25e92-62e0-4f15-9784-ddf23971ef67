<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="70px">
      <!-- <el-form-item label="分子公司" prop="subsidiaryCompanies">
        <el-input
          v-model="queryParams.subsidiaryCompanies"
          placeholder="请输入分子公司"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable style="width: 300px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="危大工程" prop="dangerousProjectName">
        <el-input v-model="queryParams.dangerousProjectName" placeholder="请输入危大工程名称" clearable style="width: 300px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="是否需要论证" prop="isNeedArgue">
        <el-input
          v-model="queryParams.isNeedArgue"
          placeholder="请输入是否需要论证"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="施工开始时间" prop="constructionStartTime">
        <el-date-picker
          clearable
          v-model="queryParams.constructionStartTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择施工开始时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="超危施工结束时间" prop="constructionEndTime">
        <el-date-picker
          clearable
          v-model="queryParams.constructionEndTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择超危施工结束时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="如需要论证是否已论证" prop="isAlreadyDemonstrated">
        <el-input
          v-model="queryParams.isAlreadyDemonstrated"
          placeholder="请输入如需要论证是否已论证"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjDangerousProject:add']" type="primary" plain icon="el-icon-plus"
          size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjDangerousProject:edit']" type="success" plain icon="el-icon-edit"
          size="mini" :disabled="single || hasReviewedItem" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjDangerousProject:remove']" type="danger" plain icon="el-icon-delete"
          size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjDangerousProject:export']" type="warning" plain icon="el-icon-download"
          size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table v-loading="loading" :data="zjDangerousProjectList" height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="分子公司" align="center" prop="subsidiaryCompanies" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="250" show-overflow-tooltip>
        <template slot-scope="{ row }">{{ row.projectName }}</template>
      </el-table-column>
      <el-table-column label="危大工程" align="center" prop="dangerousProjectName" min-width="200" show-overflow-tooltip />
      <!-- <el-table-column label="是否编制" align="center" prop="isPrepared" />
      <el-table-column label="是否审核" align="center" prop="isReview" />
      <el-table-column
        label="是否需要论证"
        align="center"
        prop="isNeedArgue"
        width="100"
      /> -->
      <el-table-column label="施工开始时间" align="center" prop="constructionStartTime" width="160">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.constructionStartTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="施工结束时间" align="center" prop="constructionEndTime" width="160">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.constructionEndTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="是否已论证"
        align="center"
        prop="isAlreadyDemonstrated"
        width="100"
      /> -->
      <el-table-column label="方案附件" align="center" prop="schemeAttachment" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button v-if="scope.row.schemeAttachment" size="mini" type="text"
            @click="handleViewAttachment(scope.row.schemeAttachment)">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="reviewStatus" width="120">
        <template slot-scope="scope">
          <el-tag :type="getReviewStatusType(scope.row.reviewStatus)" size="mini">
            {{ getReviewStatusText(scope.row.reviewStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button v-hasPermi="['inspection:zjDangerousProject:edit']" size="mini" type="text" icon="el-icon-edit"
            :disabled="scope.row.reviewStatus === '1'" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['inspection:zjDangerousProject:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
          <el-button v-hasPermi="['inspection:zjDangerousProject:edit']" size="mini" type="text" icon="el-icon-check"
            :disabled="scope.row.reviewStatus !== '0'" @click="handleReview(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改危大工程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="分子公司" prop="subsidiaryCompanies">
          <el-input v-model="form.subsidiaryCompanies" placeholder="请输入分子公司" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="危大工程" prop="dangerousProjectName">
          <el-input v-model="form.dangerousProjectName" placeholder="请输入危大工程名称" style="width: 100%" clearable />
        </el-form-item>
        <el-form-item label="施工开始时间" prop="constructionStartTime">
          <el-date-picker v-model="form.constructionStartTime" style="width: 100%" clearable type="date"
            value-format="yyyy-MM-dd" placeholder="请选择施工开始时间" />
        </el-form-item>
        <el-form-item label="施工结束时间" prop="constructionEndTime">
          <el-date-picker v-model="form.constructionEndTime" style="width: 100%" clearable type="date"
            value-format="yyyy-MM-dd" placeholder="请选择施工结束时间" />
        </el-form-item>
        <el-form-item label="危大工程方案附件" prop="schemeAttachment">
          <file-upload v-model="form.schemeAttachment" :file-type="fileType" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 审核对话框 -->
    <el-dialog :title="reviewTitle" :visible.sync="reviewOpen" width="400px" append-to-body>
      <el-form ref="reviewForm" :model="reviewForm" :rules="reviewRules" label-width="80px">
        <el-form-item label="审核状态" prop="reviewStatus">
          <el-select v-model="reviewForm.reviewStatus" placeholder="请选择审核状态" style="width: 100%">
            <el-option label="审核通过" value="1" />
            <el-option label="审核不通过" value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReview">确 定</el-button>
        <el-button @click="cancelReview">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog v-model="attachmentDialogVisible" :attachment-list="attachmentList" />
  </div>
</template>

<script>
import {
  listZjDangerousProject,
  getZjDangerousProject,
  delZjDangerousProject,
  addZjDangerousProject,
  updateZjDangerousProject
} from '@/api/inspection/zjDangerousProject'
import AttachmentDialog from '@/views/components/attchmentDialog.vue'

export default {
  name: 'ZjDangerousProject',
  components: {
    AttachmentDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中记录包含审核通过的项目
      hasReviewedItem: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 危大工程表格数据
      zjDangerousProjectList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subsidiaryCompanies: null,
        projectName: null,
        dangerousProjectName: null,
        projectType: null,
        constructionStartTime: null,
        constructionEndTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subsidiaryCompanies: [
          { required: true, message: '请输入分子公司', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        dangerousProjectName: [
          { required: true, message: '请输入危大工程名称', trigger: 'blur' }
        ]
      },
      // 附件相关
      attachmentDialogVisible: false,
      attachmentList: [],
      fileType: ['png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'pdf'],
      // 危大工程类别树形数据
      treeData: [
        {
          id: '128621949831229440',
          label: '模板工程及支撑体系'
        },
        {
          id: '128621951169212416',
          label: '脚手架工程'
        },
        {
          id: '128621949592154112',
          label: '基坑工程'
        },
        {
          id: '128621951043383296',
          label: '起重吊装及起重机械安装拆卸工程'
        },
        {
          id: '128621951345373184',
          label: '拆除工程'
        },
        {
          id: '128621951416676352',
          label: '暗挖工程'
        },
        {
          id: '128621951454425088',
          label: '其它'
        }
      ],
      // 审核相关
      reviewOpen: false,
      reviewTitle: '',
      reviewForm: {},
      reviewRules: {
        reviewStatus: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询危大工程列表 */
    getList() {
      this.loading = true
      listZjDangerousProject(this.queryParams).then((response) => {
        this.zjDangerousProjectList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        subsidiaryCompanies: null,
        projectName: null,
        dangerousProjectName: null,
        projectType: null,
        constructionStartTime: null,
        constructionEndTime: null,
        typeId: null,
        typeName: null,
        schemeAttachment: null
      }
      this.resetForm('form')
    },
    /** 查看附件 */
    handleViewAttachment(value) {
      this.attachmentDialogVisible = true
      this.attachmentList = value.split(',')
    },
    /** 处理危大工程类别选择变化 */
    handleTypeChange(value) {
      if (value) {
        const selectedType = this.treeData.find(item => String(item.id) === String(value))
        if (selectedType) {
          this.form.typeId = selectedType.id
          this.form.typeName = selectedType.label
          this.form.dangerousProjectName = selectedType.label
          this.form.projectType = selectedType.label
        }
      } else {
        this.form.typeId = null
        this.form.typeName = null
        this.form.dangerousProjectName = null
        this.form.projectType = null
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      // 检查选中的记录是否包含审核通过的项目
      this.hasReviewedItem = selection.some(item => item.reviewStatus === '1')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加危大工程'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getZjDangerousProject(id).then((response) => {
        this.form = response.data
        // 确保危大工程选择器正确回显 - 统一为字符串比较
        if (this.form.typeId) {
          console.log('当前typeId:', this.form.typeId, '类型:', typeof this.form.typeId)

          // 统一转换为字符串进行匹配
          const typeIdStr = String(this.form.typeId)
          const selectedType = this.treeData.find(item => String(item.id) === typeIdStr)

          if (selectedType) {
            console.log('找到匹配的类型:', selectedType)
            this.form.typeId = selectedType.id // 使用字符串类型的 id
            this.form.dangerousProjectName = selectedType.label
            this.form.typeName = selectedType.label
            this.form.projectType = selectedType.label
          } else {
            console.log('未找到匹配的危大工程类型，typeId:', typeIdStr)
          }
        }
        this.open = true
        this.title = '修改危大工程'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjDangerousProject(this.form).then((response) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addZjDangerousProject(this.form).then((response) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除危大工程编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjDangerousProject(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'inspection/zjDangerousProject/export',
        {
          ...this.queryParams
        },
        `zjDangerousProject_${new Date().getTime()}.xlsx`
      )
    },
    /** 获取审核状态文字 */
    getReviewStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '审核通过',
        '2': '审核不通过'
      }
      return statusMap[status] || '未审核'
    },
    /** 获取审核状态标签类型 */
    getReviewStatusType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'success',
        '2': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 审核按钮操作 */
    handleReview(row) {
      this.reviewForm = {
        id: row.id,
        reviewStatus: '1' // 默认选择审核通过
      }
      this.reviewOpen = true
      this.reviewTitle = '审核危大工程'
    },
    /** 提交审核 */
    submitReview() {
      this.$refs['reviewForm'].validate((valid) => {
        if (valid) {
          // 调用修改接口更新审核状态
          updateZjDangerousProject(this.reviewForm).then((response) => {
            this.$modal.msgSuccess('审核成功')
            this.reviewOpen = false
            this.getList()
          })
        }
      })
    },
    /** 取消审核 */
    cancelReview() {
      this.reviewOpen = false
      this.reviewForm = {}
    }
  }
}
</script>
