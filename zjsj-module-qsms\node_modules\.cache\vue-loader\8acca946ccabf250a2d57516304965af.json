{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue?vue&type=style&index=0&id=a3657b8e&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue", "mtime": 1757425926929}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgouY3VzdG9tLXRyZWUtbm9kZSB7CiAgZmxleDogMTsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGZvbnQtc2l6ZTogMTRweDsKICBwYWRkaW5nLXJpZ2h0OiA4cHg7Cn0KCi50cmVlLWxhYmVsIHsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOwogIHdoaXRlLXNwYWNlOiBub3dyYXA7Cn0KCjo6di1kZWVwIC5oYXphcmQtY2F0ZWdvcnktZHJvcGRvd24gewogIG1heC1oZWlnaHQ6IDQwMHB4Owp9Cgo6OnYtZGVlcCAuaGF6YXJkLWNhdGVnb3J5LWRyb3Bkb3duIC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgewogIGhlaWdodDogYXV0bzsKICBwYWRkaW5nOiA0cHggMDsKfQoKOjp2LWRlZXAgLmhhemFyZC1jYXRlZ29yeS1kcm9wZG93biAuZWwtdHJlZS1ub2RlX19jb250ZW50OmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOwp9Cgo6OnYtZGVlcCAuaGF6YXJkLWNhdGVnb3J5LWRyb3Bkb3duIC5lbC10cmVlLW5vZGUuaXMtY3VycmVudCA+IC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgewogIGJhY2tncm91bmQtY29sb3I6ICNmMGY3ZmY7CiAgY29sb3I6ICM0MDllZmY7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7Cn0K"}, {"version": 3, "sources": ["selectHazardCategoryTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2VA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "selectHazardCategoryTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-select\n    ref=\"hazardSelectRef\"\n    :value=\"displayValue\"\n    :placeholder=\"placeholder\"\n    :disabled=\"disabled\"\n    clearable\n    filterable\n    style=\"width: 100%\"\n    class=\"hazard-category-select\"\n    popper-class=\"hazard-category-dropdown\"\n    @input=\"handleInput\"\n    @change=\"handleSelectChange\"\n    @visible-change=\"handleVisibleChange\"\n    @clear=\"handleClear\"\n  >\n    <el-option :value=\"displayValue\" style=\"height: auto; padding: 0\">\n      <el-tree\n        ref=\"hazardTree\"\n        :data=\"treeData\"\n        :props=\"treeProps\"\n        :expand-on-click-node=\"false\"\n        node-key=\"id\"\n        highlight-current\n        style=\"padding: 5px 0\"\n        @node-click=\"handleNodeClick\"\n      >\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\n          <span class=\"tree-label\">{{ node.label }}</span>\n        </span>\n      </el-tree>\n    </el-option>\n  </el-select>\n</template>\n\n<script>\nexport default {\n  name: 'SelectHazardCategoryTree',\n  props: {\n    value: {\n      type: [String, Number],\n      default: '',\n    },\n    placeholder: {\n      type: String,\n      default: '请选择隐患类别',\n    },\n    categoryList: {\n      type: Array,\n      default: () => [],\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    return {\n      displayValue: '',\n      treeProps: {\n        label: 'label',\n        children: 'children',\n      },\n    }\n  },\n\n  computed: {\n    treeData() {\n      return this.processTreeData(this.categoryList)\n    },\n  },\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        console.log('value 变化:', newVal)\n        this.displayValue = newVal || ''\n        // 当值改变时，延迟设置树的选中状态\n        setTimeout(() => {\n          this.setTreeSelection()\n        }, 100)\n      },\n    },\n    categoryList: {\n      immediate: true,\n      handler(newData) {\n        console.log('categoryList 变化:', newData)\n        // 当分类列表变化时，延迟设置树的选中状态\n        if (newData && newData.length > 0 && this.displayValue) {\n          setTimeout(() => {\n            this.setTreeSelection()\n          }, 200)\n        }\n      },\n    },\n  },\n\n  mounted() {\n    // 组件挂载后初始化设置\n    this.$nextTick(() => {\n      this.setTreeSelection()\n    })\n  },\n\n  methods: {\n    handleVisibleChange(isVisible) {\n      if (isVisible) {\n        // 下拉框打开时，设置当前选中的节点\n        this.$nextTick(() => {\n          if (this.value && this.$refs.hazardTree) {\n            const selectedNode = this.findNodeById(this.treeData, this.value)\n            if (selectedNode) {\n              this.$refs.hazardTree.setCurrentKey(this.value)\n              this.expandToNode(selectedNode)\n            }\n          }\n        })\n      }\n    },\n\n    handleNodeClick(data, node) {\n      console.log('点击隐患类别节点', data, node)\n      \n      // 如果有子节点，切换展开状态\n      if (data.children && data.children.length > 0) {\n        node.expanded = !node.expanded\n        return\n      }\n      \n      // 叶子节点，触发选择\n      this.selectNode(data)\n    },\n\n    selectNode(data) {\n      this.displayValue = data.label\n      this.$emit('input', data.label)\n      \n      // 触发 change 事件，传递完整的节点信息\n      this.$emit('change', {\n        id: data.id,\n        label: data.label,\n        value: data.label,\n      })\n      \n      // 更新树的高亮选择\n      this.$refs.hazardTree.setCurrentKey(data.id)\n      \n      // 关闭下拉框\n      this.$nextTick(() => {\n        this.closeDropdown()\n      })\n    },\n\n    handleInput(value) {\n      this.displayValue = value\n      this.$emit('input', value)\n    },\n\n    handleSelectChange(value) {\n      if (!value) {\n        this.$emit('change', null)\n      }\n    },\n\n    handleClear() {\n      this.displayValue = ''\n      this.$emit('input', '')\n      this.$emit('change', null)\n    },\n\n    closeDropdown() {\n      this.$refs.hazardSelectRef.blur()\n    },\n\n    // 处理树形数据\n    processTreeData(data) {\n      if (!data || !Array.isArray(data)) return []\n      \n      return data.map((item) => ({\n        ...item,\n        id: item.id || item.hazardId,\n        label: item.label || item.hazardName || item.name,\n        children: item.children ? this.processTreeData(item.children) : [],\n      }))\n    },\n\n    // 根据ID查找节点\n    findNodeById(nodes, id) {\n      for (const node of nodes) {\n        if (node.id === id) return node\n        if (node.children && node.children.length) {\n          const found = this.findNodeById(node.children, id)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 展开到指定节点\n    expandToNode(targetNode) {\n      if (!targetNode || !this.$refs.hazardTree) return\n      \n      // 递归展开父节点\n      const expandParents = (node) => {\n        const parent = this.findParentNode(this.treeData, node.id)\n        if (parent) {\n          this.$refs.hazardTree.setExpandedKey(parent.id, true)\n          expandParents(parent)\n        }\n      }\n      \n      expandParents(targetNode)\n    },\n\n    // 查找父节点\n    findParentNode(nodes, childId, parent = null) {\n      for (const node of nodes) {\n        if (node.id === childId) {\n          return parent\n        }\n        if (node.children && node.children.length) {\n          const found = this.findParentNode(node.children, childId, node)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 根据标签路径查找节点（用于回显）\n    findNodeByLabelPath(labelPath) {\n      console.log('查找节点路径:', labelPath)\n\n      if (!labelPath || !this.treeData.length) {\n        console.log('路径为空或树数据为空')\n        return null\n      }\n\n      // 分割路径，例如 \"安全管理-安全生产责任制\" -> [\"安全管理\", \"安全生产责任制\"]\n      const pathParts = labelPath.split('-')\n      console.log('路径分割结果:', pathParts)\n\n      let currentNodes = this.treeData\n      let targetNode = null\n\n      for (let i = 0; i < pathParts.length; i++) {\n        const part = pathParts[i].trim()\n        console.log(`查找第${i+1}级: \"${part}\"`)\n        console.log('当前可选节点:', currentNodes.map(n => n.label))\n\n        targetNode = currentNodes.find(node => node.label === part)\n\n        if (!targetNode) {\n          console.log(`未找到匹配的节点: \"${part}\"`)\n          return null\n        }\n\n        console.log(`找到节点: \"${part}\"`, targetNode)\n\n        if (i < pathParts.length - 1) {\n          // 不是最后一级，继续查找子节点\n          currentNodes = targetNode.children || []\n          console.log(`进入下一级，子节点数量: ${currentNodes.length}`)\n        }\n      }\n\n      console.log('最终找到的目标节点:', targetNode)\n      return targetNode\n    },\n\n    // 设置树形选择回显\n    setTreeSelection() {\n      console.log('=== setTreeSelection 开始 ===')\n      console.log('displayValue:', this.displayValue)\n      console.log('treeData 长度:', this.treeData?.length)\n      console.log('hazardTree ref:', this.$refs.hazardTree)\n\n      if (!this.displayValue) {\n        console.log('displayValue 为空，退出')\n        return\n      }\n\n      if (!this.$refs.hazardTree) {\n        console.log('hazardTree ref 不存在，退出')\n        return\n      }\n\n      if (!this.treeData || this.treeData.length === 0) {\n        console.log('treeData 为空，退出')\n        return\n      }\n\n      this.$nextTick(() => {\n        const targetNode = this.findNodeByLabelPath(this.displayValue)\n\n        if (targetNode) {\n          console.log('找到目标节点:', targetNode)\n\n          // 设置当前选中的节点\n          this.$refs.hazardTree.setCurrentKey(targetNode.id)\n\n          // 展开到目标节点\n          this.expandToNode(targetNode)\n\n          console.log('回显设置成功')\n        } else {\n          console.warn('未找到匹配的节点:', this.displayValue)\n          console.log('树数据结构:', JSON.stringify(this.treeData, null, 2))\n        }\n      })\n\n      console.log('=== setTreeSelection 结束 ===')\n    },\n\n    // 设置树的选中状态\n    setTreeSelection() {\n      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {\n        return\n      }\n\n      // 根据displayValue查找对应的节点\n      const selectedNode = this.findNodeByLabel(this.treeData, this.value)\n      if (selectedNode) {\n        this.$refs.hazardTree.setCurrentKey(selectedNode.id)\n        this.expandToNode(selectedNode)\n      }\n    },\n\n    // 根据标签查找节点\n    findNodeByLabel(nodes, label) {\n      for (const node of nodes) {\n        if (node.label === label) {\n          return node\n        }\n        if (node.children && node.children.length) {\n          const found = this.findNodeByLabel(node.children, label)\n          if (found) return found\n        }\n      }\n      return null\n    },\n  },\n}\n</script>\n\n<style scoped>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.tree-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n::v-deep .hazard-category-dropdown {\n  max-height: 400px;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content {\n  height: auto;\n  padding: 4px 0;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content:hover {\n  background-color: #f5f7fa;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #f0f7ff;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"]}]}