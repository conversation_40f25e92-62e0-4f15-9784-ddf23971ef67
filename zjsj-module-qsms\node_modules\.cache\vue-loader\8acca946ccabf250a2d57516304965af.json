{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue?vue&type=style&index=0&id=a3657b8e&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue", "mtime": 1757425570923}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKLmN1c3RvbS10cmVlLW5vZGUgewogIGZsZXg6IDE7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBmb250LXNpemU6IDE0cHg7CiAgcGFkZGluZy1yaWdodDogOHB4Owp9CgoudHJlZS1sYWJlbCB7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsKICB3aGl0ZS1zcGFjZTogbm93cmFwOwp9Cgo6OnYtZGVlcCAuaGF6YXJkLWNhdGVnb3J5LWRyb3Bkb3duIHsKICBtYXgtaGVpZ2h0OiA0MDBweDsKfQoKOjp2LWRlZXAgLmhhemFyZC1jYXRlZ29yeS1kcm9wZG93biAuZWwtdHJlZS1ub2RlX19jb250ZW50IHsKICBoZWlnaHQ6IGF1dG87CiAgcGFkZGluZzogNHB4IDA7Cn0KCjo6di1kZWVwIC5oYXphcmQtY2F0ZWdvcnktZHJvcGRvd24gLmVsLXRyZWUtbm9kZV9fY29udGVudDpob3ZlciB7CiAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjdmYTsKfQoKOjp2LWRlZXAgLmhhemFyZC1jYXRlZ29yeS1kcm9wZG93biAuZWwtdHJlZS1ub2RlLmlzLWN1cnJlbnQgPiAuZWwtdHJlZS1ub2RlX19jb250ZW50IHsKICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmN2ZmOwogIGNvbG9yOiAjNDA5ZWZmOwogIGZvbnQtd2VpZ2h0OiBib2xkOwp9Cg=="}, {"version": 3, "sources": ["selectHazardCategoryTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "selectHazardCategoryTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-select\n    ref=\"hazardSelectRef\"\n    :value=\"displayValue\"\n    :placeholder=\"placeholder\"\n    :disabled=\"disabled\"\n    clearable\n    filterable\n    style=\"width: 100%\"\n    class=\"hazard-category-select\"\n    popper-class=\"hazard-category-dropdown\"\n    @input=\"handleInput\"\n    @change=\"handleSelectChange\"\n    @visible-change=\"handleVisibleChange\"\n    @clear=\"handleClear\"\n  >\n    <el-option :value=\"displayValue\" style=\"height: auto; padding: 0\">\n      <el-tree\n        ref=\"hazardTree\"\n        :data=\"treeData\"\n        :props=\"treeProps\"\n        :expand-on-click-node=\"false\"\n        node-key=\"id\"\n        highlight-current\n        style=\"padding: 5px 0\"\n        @node-click=\"handleNodeClick\"\n      >\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\n          <span class=\"tree-label\">{{ node.label }}</span>\n        </span>\n      </el-tree>\n    </el-option>\n  </el-select>\n</template>\n\n<script>\nexport default {\n  name: 'SelectHazardCategoryTree',\n  props: {\n    value: {\n      type: [String, Number],\n      default: '',\n    },\n    placeholder: {\n      type: String,\n      default: '请选择隐患类别',\n    },\n    categoryList: {\n      type: Array,\n      default: () => [],\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    return {\n      displayValue: '',\n      treeProps: {\n        label: 'label',\n        children: 'children',\n      },\n    }\n  },\n\n  computed: {\n    treeData() {\n      return this.processTreeData(this.categoryList)\n    },\n  },\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        this.displayValue = newVal || ''\n        // 当值改变时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n    categoryList: {\n      immediate: true,\n      handler() {\n        // 当分类列表变化时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n  },\n\n  mounted() {\n    // 组件挂载后初始化设置\n    this.$nextTick(() => {\n      this.setTreeSelection()\n    })\n  },\n\n  methods: {\n    handleVisibleChange(isVisible) {\n      if (isVisible) {\n        // 下拉框打开时，设置当前选中的节点\n        this.$nextTick(() => {\n          if (this.value && this.$refs.hazardTree) {\n            const selectedNode = this.findNodeById(this.treeData, this.value)\n            if (selectedNode) {\n              this.$refs.hazardTree.setCurrentKey(this.value)\n              this.expandToNode(selectedNode)\n            }\n          }\n        })\n      }\n    },\n\n    handleNodeClick(data, node) {\n      console.log('点击隐患类别节点', data, node)\n      \n      // 如果有子节点，切换展开状态\n      if (data.children && data.children.length > 0) {\n        node.expanded = !node.expanded\n        return\n      }\n      \n      // 叶子节点，触发选择\n      this.selectNode(data)\n    },\n\n    selectNode(data) {\n      this.displayValue = data.label\n      this.$emit('input', data.label)\n      \n      // 触发 change 事件，传递完整的节点信息\n      this.$emit('change', {\n        id: data.id,\n        label: data.label,\n        value: data.label,\n      })\n      \n      // 更新树的高亮选择\n      this.$refs.hazardTree.setCurrentKey(data.id)\n      \n      // 关闭下拉框\n      this.$nextTick(() => {\n        this.closeDropdown()\n      })\n    },\n\n    handleInput(value) {\n      this.displayValue = value\n      this.$emit('input', value)\n    },\n\n    handleSelectChange(value) {\n      if (!value) {\n        this.$emit('change', null)\n      }\n    },\n\n    handleClear() {\n      this.displayValue = ''\n      this.$emit('input', '')\n      this.$emit('change', null)\n    },\n\n    closeDropdown() {\n      this.$refs.hazardSelectRef.blur()\n    },\n\n    // 处理树形数据\n    processTreeData(data) {\n      if (!data || !Array.isArray(data)) return []\n      \n      return data.map((item) => ({\n        ...item,\n        id: item.id || item.hazardId,\n        label: item.label || item.hazardName || item.name,\n        children: item.children ? this.processTreeData(item.children) : [],\n      }))\n    },\n\n    // 根据ID查找节点\n    findNodeById(nodes, id) {\n      for (const node of nodes) {\n        if (node.id === id) return node\n        if (node.children && node.children.length) {\n          const found = this.findNodeById(node.children, id)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 展开到指定节点\n    expandToNode(targetNode) {\n      if (!targetNode || !this.$refs.hazardTree) return\n      \n      // 递归展开父节点\n      const expandParents = (node) => {\n        const parent = this.findParentNode(this.treeData, node.id)\n        if (parent) {\n          this.$refs.hazardTree.setExpandedKey(parent.id, true)\n          expandParents(parent)\n        }\n      }\n      \n      expandParents(targetNode)\n    },\n\n    // 查找父节点\n    findParentNode(nodes, childId, parent = null) {\n      for (const node of nodes) {\n        if (node.id === childId) {\n          return parent\n        }\n        if (node.children && node.children.length) {\n          const found = this.findParentNode(node.children, childId, node)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 设置树的选中状态\n    setTreeSelection() {\n      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {\n        return\n      }\n\n      // 根据displayValue查找对应的节点\n      const selectedNode = this.findNodeByLabel(this.treeData, this.value)\n      if (selectedNode) {\n        this.$refs.hazardTree.setCurrentKey(selectedNode.id)\n        this.expandToNode(selectedNode)\n      }\n    },\n\n    // 根据标签查找节点\n    findNodeByLabel(nodes, label) {\n      for (const node of nodes) {\n        if (node.label === label) {\n          return node\n        }\n        if (node.children && node.children.length) {\n          const found = this.findNodeByLabel(node.children, label)\n          if (found) return found\n        }\n      }\n      return null\n    },\n  },\n}\n</script>\n\n<style scoped>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.tree-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n::v-deep .hazard-category-dropdown {\n  max-height: 400px;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content {\n  height: auto;\n  padding: 4px 0;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content:hover {\n  background-color: #f5f7fa;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #f0f7ff;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"]}]}