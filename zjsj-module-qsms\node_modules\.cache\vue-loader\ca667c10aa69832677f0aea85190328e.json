{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\contractor\\zjContractorBlaklist\\index.vue", "mtime": 1757424290727}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0WmpDb250cmFjdG9yQmxha2xpc3QsDQogIGdldFpqQ29udHJhY3RvckJsYWtsaXN0LA0KICBkZWxaakNvbnRyYWN0b3JCbGFrbGlzdCwNCiAgYWRkWmpDb250cmFjdG9yQmxha2xpc3QsDQogIHVwZGF0ZVpqQ29udHJhY3RvckJsYWtsaXN0LA0KICBnZXRVc2VySW5mbywNCn0gZnJvbSAiQC9hcGkvY29udHJhY3Rvci96akNvbnRyYWN0b3JCbGFrbGlzdCI7DQppbXBvcnQgew0KICBsaXN0WmpDb250cmFjdG9ySW5mbywNCiAgZ2V0WmpDb250cmFjdG9ySW5mbywNCiAgdXBkYXRlWmpDb250cmFjdG9ySW5mbywNCiAgZ2V0Q29udHJhY3RvckluZm8sDQp9IGZyb20gIkAvYXBpL2NvbnRyYWN0b3IvempDb250cmFjdG9ySW5mbyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlpqQ29udHJhY3RvckJsYWtsaXN0IiwNCiAgZGljdHM6IFsic3lzX2NvbnRyYWN0b3JfdHlwZSJdLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDlvZPliY3mtLvliqjmoIfnrb7pobUNCiAgICAgIGFjdGl2ZVRhYjogImNvbnRyYWN0b3IiLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6YCJ5Lit55qE6KGM5pWw5o2uDQogICAgICBzZWxlY3RlZFJvd3M6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOaJv+WMheWVhum7keWQjeWNleihqOagvOaVsOaNrg0KICAgICAgempDb250cmFjdG9yQmxha2xpc3RMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOaJv+WMheWVhumAiemhuQ0KICAgICAgY29udHJhY3Rvck9wdGlvbnM6IFtdLA0KICAgICAgLy8g5Y+v5re75Yqg5Yiw6buR5ZCN5Y2V55qE5om/5YyF5ZWG5YiX6KGoDQogICAgICBhdmFpbGFibGVDb250cmFjdG9yTGlzdDogW10sDQogICAgICAvLyDnrqHnkIbkurrpgInpobkNCiAgICAgIG1hbmFnZXJPcHRpb25zOiBbXSwNCiAgICAgIC8vIOaJv+WMheWVhuS6uuWRmOebuOWFs+aVsOaNrg0KICAgICAgcGVyc29ubmVsTG9hZGluZzogdHJ1ZSwNCiAgICAgIHNlbGVjdGVkUGVyc29ubmVsUm93czogW10sDQogICAgICBwZXJzb25uZWxJZHM6IFtdLA0KICAgICAgcGVyc29ubmVsVG90YWw6IDAsDQogICAgICBwZXJzb25uZWxMaXN0OiBbXSwNCiAgICAgIHBlcnNvbm5lbFF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgY29udHJhY3Rvck5hbWU6IG51bGwsDQogICAgICAgIHBlcnNvbm5lbFN0YXR1czogIiIsDQogICAgICB9LA0KICAgICAgLy8g5om/5YyF5ZWG6buR5ZCN5Y2V5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIGNvbnRyYWN0b3JOYW1lOiBudWxsLA0KICAgICAgICBhZG1pbmlzdHJhdG9ySWQ6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY29udHJhY3RvcklkOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6KaB5Yqg5YWl6buR5ZCN5Y2V55qE5om/5YyF5ZWGIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiLA0KICAgICAgICAgIH0sDQogICAgICAgIF0sDQogICAgICAgIGJsYWNrbGlzdFJlYXNvbjogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXpu5HlkI3ljZXljp/lm6AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogNSwgbWVzc2FnZTogIum7keWQjeWNleWOn+WboOiHs+WwkemcgOimgTXkuKrlrZfnrKYiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1heDogNTAwLCBtZXNzYWdlOiAi6buR5ZCN5Y2V5Y6f5Zug5LiN6IO96LaF6L+HNTAw5Liq5a2X56ymIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgLy8g5Lq65ZGY5by556qX55u45YWzDQogICAgICBwZXJzb25uZWxPcGVuOiBmYWxzZSwNCiAgICAgIHBlcnNvbm5lbFRpdGxlOiAiIiwNCiAgICAgIHBlcnNvbm5lbEZvcm06IHt9LA0KICAgICAgcGVyc29ubmVsUnVsZXM6IHsNCiAgICAgICAgcGVyc29ubmVsTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkurrlkZjlp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcGVyc29ubmVsUGhvbmU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGU57O755S16K+d5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGlkTnVtYmVyOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui6q+S7veivgeWPt+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb250cmFjdG9ySWQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5omA5bGe5om/5YyF5ZWGIiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICAvLyDliqDlhaXpu5HlkI3ljZXlvLnnqpfnm7jlhbMNCiAgICAgIGJsYWNrbGlzdERpYWxvZ09wZW46IGZhbHNlLA0KICAgICAgYmxhY2tsaXN0Rm9ybTogew0KICAgICAgICBwZXJzb25uZWxJZDogbnVsbCwNCiAgICAgICAgYmxhY2tsaXN0UmVhc29uOiAiIiwNCiAgICAgIH0sDQogICAgICBibGFja2xpc3RSdWxlczogew0KICAgICAgICBwZXJzb25uZWxJZDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuivt+mAieaLqeimgeaLiem7keeahOaJv+WMheWVhuS6uuWRmCIsDQogICAgICAgICAgICB0cmlnZ2VyOiBbImNoYW5nZSIsICJibHVyIl0sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYmxhY2tsaXN0UmVhc29uOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5ouJ6buR5Y6f5ZugIiwNCiAgICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG1pbjogNSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmi4npu5Hljp/lm6Doh7PlsJHpnIDopoE15Liq5a2X56ymIiwNCiAgICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXSwNCiAgICAgICAgICB9LA0KICAgICAgICAgIHsNCiAgICAgICAgICAgIG1heDogNTAwLA0KICAgICAgICAgICAgbWVzc2FnZTogIuaLiem7keWOn+WboOS4jeiDvei2hei/hzUwMOS4quWtl+espiIsDQogICAgICAgICAgICB0cmlnZ2VyOiBbImJsdXIiLCAiY2hhbmdlIl0sDQogICAgICAgICAgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICAvLyDlj6/pgInmi6nnmoTkurrlkZjliJfooajvvIjmnKrlnKjpu5HlkI3ljZXkuK3nmoTkurrlkZjvvIkNCiAgICAgIGF2YWlsYWJsZVBlcnNvbm5lbExpc3Q6IFtdLA0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5piv5ZCm5pyJ6YCJ5Lit6aG5DQogICAgaGFzU2VsZWN0aW9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuaWRzLmxlbmd0aCA+IDA7DQogICAgfSwNCiAgICAvLyDmmK/lkKbmnInpgInkuK3nmoTkurrlkZjpobkNCiAgICBoYXNQZXJzb25uZWxTZWxlY3Rpb24oKSB7DQogICAgICByZXR1cm4gdGhpcy5wZXJzb25uZWxJZHMubGVuZ3RoID4gMDsNCiAgICB9LA0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIC8vIOWIneWni+WMluS4pOS4quagh+etvumhteeahOaVsOaNrg0KICAgIHRoaXMuZ2V0UGVyc29ubmVsTGlzdCgpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIC8vIOWKoOi9veeuoeeQhuS6uumAiemhuQ0KICAgIHRoaXMubG9hZE1hbmFnZXJPcHRpb25zKCk7DQogICAgLy8g5Yqg6L295om/5YyF5ZWG6YCJ6aG5DQogICAgdGhpcy5sb2FkQ29udHJhY3Rvck9wdGlvbnMoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDliJ3lp4vljJbmib/ljIXllYbkurrlkZjmqKHmi5/mlbDmja4gKi8NCiAgICBpbml0UGVyc29ubmVsTW9ja0RhdGEoKSB7DQogICAgICB0aGlzLnBlcnNvbm5lbExpc3QgPSBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogMSwNCiAgICAgICAgICBwZXJzb25uZWxOYW1lOiAi5byg5LiJIiwNCiAgICAgICAgICBwZXJzb25uZWxTdGF0dXM6ICIwIiwNCiAgICAgICAgICBwZXJzb25uZWxQaG9uZTogIjEzODAwMTM4MDAwIiwNCiAgICAgICAgICBpZE51bWJlcjogIjMyMDEyMzE5OTAwMTAxMTIzNCIsDQogICAgICAgICAgY29udHJhY3Rvck5hbWU6ICLmmbrliJvmnLrmorDpm4blm6IiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDIsDQogICAgICAgICAgcGVyc29ubmVsTmFtZTogIuadjuWbmyIsDQogICAgICAgICAgcGVyc29ubmVsU3RhdHVzOiAiMSIsDQogICAgICAgICAgcGVyc29ubmVsUGhvbmU6ICIxMzkwMDEzOTAwMCIsDQogICAgICAgICAgaWROdW1iZXI6ICIzMjAxMjMxOTkwMDIwMjEyMzUiLA0KICAgICAgICAgIGNvbnRyYWN0b3JOYW1lOiAi57K+5bel55S15a2Q6K6+5aSH5Yi26YCg6ZuG5ZuiIiwNCiAgICAgICAgfSwNCiAgICAgIF07DQogICAgICB0aGlzLnBlcnNvbm5lbFRvdGFsID0gMjsNCiAgICAgIHRoaXMucGVyc29ubmVsTG9hZGluZyA9IGZhbHNlOw0KICAgIH0sDQogICAgLyoqIOWIneWni+WMluaJv+WMheWVhum7keWQjeWNleaooeaLn+aVsOaNriAqLw0KICAgIGluaXRNb2NrRGF0YSgpIHsNCiAgICAgIHRoaXMuempDb250cmFjdG9yQmxha2xpc3RMaXN0ID0gWw0KICAgICAgICB7DQogICAgICAgICAgaWQ6IDEsDQogICAgICAgICAgY29udHJhY3Rvck5hbWU6ICLlub/kuJzmmI7ljY7lt6XnqIvmnInpmZDlhazlj7giLA0KICAgICAgICAgIGNyZWRpdENvZGU6ICI5MTMyMDczNjYxNzg5MzA3NSIsDQogICAgICAgICAgY29udHJhY3RvclR5cGU6ICIxIiwNCiAgICAgICAgICBtYW5hZ2VyTmFtZTogIuaZuuWogeenkeaKgOaZuuaFp+W3peWOgiIsDQogICAgICAgICAgcmVzcG9uc2libGVQZXJzb246ICLnjovkvbPmmI4iLA0KICAgICAgICAgIGJsYWNrbGlzdFJlYXNvbjogIjEiLA0KICAgICAgICB9LA0KICAgICAgXTsNCiAgICAgIHRoaXMudG90YWwgPSAxOw0KICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgfSwNCiAgICAvKiog5p+l6K+i5om/5YyF5ZWG6buR5ZCN5Y2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAvLyDkvb/nlKjmib/ljIXllYbkv6Hmga/mjqXlj6PvvIzkvKBibGFja2xpc3RTdGF0dXM9Muafpeivoum7keWQjeWNlQ0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICBibGFja2xpc3RTdGF0dXM6IDIsDQogICAgICB9Ow0KICAgICAgbGlzdFpqQ29udHJhY3RvckluZm8ocGFyYW1zKQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICB0aGlzLnpqQ29udHJhY3RvckJsYWtsaXN0TGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNrg0KICAgICAgICAgIHRoaXMuaW5pdE1vY2tEYXRhKCk7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICAgIGNvbnRyYWN0b3JJZDogbnVsbCwNCiAgICAgICAgY29udHJhY3Rvck5hbWU6IG51bGwsDQogICAgICAgIGNyZWRpdENvZGU6IG51bGwsDQogICAgICAgIGNvbnRyYWN0b3JUeXBlOiBudWxsLA0KICAgICAgICBtYW5hZ2VyTmFtZTogbnVsbCwNCiAgICAgICAgcmVzcG9uc2libGVQZXJzb246IG51bGwsDQogICAgICAgIGJsYWNrbGlzdFJlYXNvbjogbnVsbCwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaJv+WMheWVhumAieaLqeWPmOWMluWkhOeQhiAqLw0KICAgIGhhbmRsZUNvbnRyYWN0b3JTZWxlY3RDaGFuZ2UoY29udHJhY3RvcklkKSB7DQogICAgICBpZiAoY29udHJhY3RvcklkKSB7DQogICAgICAgIC8vIOagueaNrumAieaLqeeahOaJv+WMheWVhklE5aGr5YWF5om/5YyF5ZWG5L+h5oGvDQogICAgICAgIGNvbnN0IHNlbGVjdGVkQ29udHJhY3RvciA9IHRoaXMuYXZhaWxhYmxlQ29udHJhY3Rvckxpc3QuZmluZCgNCiAgICAgICAgICAoY29udHJhY3RvcikgPT4gY29udHJhY3Rvci5pZCA9PT0gY29udHJhY3RvcklkDQogICAgICAgICk7DQogICAgICAgIGlmIChzZWxlY3RlZENvbnRyYWN0b3IpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uY29udHJhY3Rvck5hbWUgPSBzZWxlY3RlZENvbnRyYWN0b3IuY29udHJhY3Rvck5hbWU7DQogICAgICAgICAgdGhpcy5mb3JtLmNyZWRpdENvZGUgPSBzZWxlY3RlZENvbnRyYWN0b3IuY3JlZGl0Q29kZTsNCiAgICAgICAgICB0aGlzLmZvcm0uY29udHJhY3RvclR5cGUgPSBzZWxlY3RlZENvbnRyYWN0b3IuY29udHJhY3RvclR5cGU7DQogICAgICAgICAgdGhpcy5mb3JtLm1hbmFnZXJOYW1lID0gc2VsZWN0ZWRDb250cmFjdG9yLm1hbmFnZXJOYW1lOw0KICAgICAgICAgIHRoaXMuZm9ybS5yZXNwb25zaWJsZVBlcnNvbiA9IHNlbGVjdGVkQ29udHJhY3Rvci5yZXNwb25zaWJsZVBlcnNvbjsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5riF56m65om/5YyF5ZWG5L+h5oGvDQogICAgICAgIHRoaXMuZm9ybS5jb250cmFjdG9yTmFtZSA9IG51bGw7DQogICAgICAgIHRoaXMuZm9ybS5jcmVkaXRDb2RlID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLmNvbnRyYWN0b3JUeXBlID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLm1hbmFnZXJOYW1lID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLnJlc3BvbnNpYmxlUGVyc29uID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLyoqIOWKoOi9veeuoeeQhuS6uumAiemhuSAqLw0KICAgIGxvYWRNYW5hZ2VyT3B0aW9ucygpIHsNCiAgICAgIC8vIOS9v+eUqGdldFVzZXJJbmZv5o6l5Y+j6I635Y+W566h55CG5Lq65ZGY5pWw5o2u77yM5Lyg6YCSdHlwZT0x6KGo56S66I635Y+W566h55CG5Lq65ZGYDQogICAgICBnZXRVc2VySW5mbygxKQ0KICAgICAgICAudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAvLyDmoLnmja7mjqXlj6Pov5Tlm57nmoTmlbDmja7nu5PmnoTlpITnkIYNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UuZGF0YSB8fCByZXNwb25zZS5yb3dzIHx8IHJlc3BvbnNlIHx8IFtdOw0KICAgICAgICAgIHRoaXMubWFuYWdlck9wdGlvbnMgPSBkYXRhLm1hcCgobWFuYWdlcikgPT4gKHsNCiAgICAgICAgICAgIHZhbHVlOiBtYW5hZ2VyLnVzZXJJZCB8fCBtYW5hZ2VyLmlkLA0KICAgICAgICAgICAgbGFiZWw6IG1hbmFnZXIubmlja05hbWUgfHwgbWFuYWdlci5uYW1lIHx8IG1hbmFnZXIudXNlck5hbWUsDQogICAgICAgICAgfSkpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKGVycm9yKSA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W566h55CG5Lq66YCJ6aG55aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6I635Y+W566h55CG5Lq66YCJ6aG55aSx6LSlIik7DQogICAgICAgICAgLy8g5aSx6LSl5pe25L2/55So5Y6f5pyJ56Gs57yW56CB5pWw5o2u5L2c5Li65aSH6YCJDQogICAgICAgICAgdGhpcy5tYW5hZ2VyT3B0aW9ucyA9IFsNCiAgICAgICAgICAgIHsgdmFsdWU6ICIxIiwgbGFiZWw6ICLnjovkvbPmmI4iIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAiMiIsIGxhYmVsOiAi5p2O5LicIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogIjMiLCBsYWJlbDogIuW8oOS8nyIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliqDovb3mib/ljIXllYbpgInpobkgKi8NCiAgICBsb2FkQ29udHJhY3Rvck9wdGlvbnMoKSB7DQogICAgICAvLyDkvb/nlKjmib/ljIXllYbkv6Hmga/mn6Xor6LmjqXlj6Pojrflj5bmiYDmnInmib/ljIXllYbmlbDmja4NCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwMDAsIC8vIOiOt+WPlui2s+Wkn+WkmueahOaVsOaNrg0KICAgICAgfTsNCiAgICAgIGxpc3RaakNvbnRyYWN0b3JJbmZvKHBhcmFtcykNCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgLy8g5qC55o2u5o6l5Y+j6L+U5Zue55qE5pWw5o2u57uT5p6E5aSE55CGDQogICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJvd3MgfHwgcmVzcG9uc2UuZGF0YSB8fCByZXNwb25zZSB8fCBbXTsNCiAgICAgICAgICB0aGlzLmNvbnRyYWN0b3JPcHRpb25zID0gZGF0YS5tYXAoKGNvbnRyYWN0b3IpID0+ICh7DQogICAgICAgICAgICB2YWx1ZTogY29udHJhY3Rvci5pZCwNCiAgICAgICAgICAgIGxhYmVsOiBjb250cmFjdG9yLmNvbnRyYWN0b3JOYW1lLA0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluaJv+WMheWVhumAiemhueWksei0pToiLCBlcnJvcik7DQogICAgICAgICAgLy8g5aSx6LSl5pe25L2/55So5aSH55So5o6l5Y+jDQogICAgICAgICAgdGhpcy5sb2FkQ29udHJhY3Rvck9wdGlvbnNGYWxsYmFjaygpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlpIfnlKjmlrnms5XvvJrkvb/nlKjlj6bkuIDkuKrmjqXlj6PliqDovb3mib/ljIXllYbpgInpobkgKi8NCiAgICBsb2FkQ29udHJhY3Rvck9wdGlvbnNGYWxsYmFjaygpIHsNCiAgICAgIC8vIOS9v+eUqGdldENvbnRyYWN0b3JJbmZv5o6l5Y+j5L2c5Li65aSH6YCJ77yM5Y+C5pWw5Li6bnVsbOiOt+WPluaJgOacieaJv+WMheWVhg0KICAgICAgZ2V0Q29udHJhY3RvckluZm8obnVsbCkNCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLmRhdGEgfHwgcmVzcG9uc2Uucm93cyB8fCByZXNwb25zZSB8fCBbXTsNCiAgICAgICAgICB0aGlzLmNvbnRyYWN0b3JPcHRpb25zID0gZGF0YS5tYXAoKGNvbnRyYWN0b3IpID0+ICh7DQogICAgICAgICAgICB2YWx1ZTogY29udHJhY3Rvci5pZCwNCiAgICAgICAgICAgIGxhYmVsOiBjb250cmFjdG9yLmNvbnRyYWN0b3JOYW1lLA0KICAgICAgICAgIH0pKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluaJv+WMheWVhumAiemhueWksei0pe+8iOWkh+eUqOaOpeWPo++8iToiLCBlcnJvcik7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluaJv+WMheWVhumAiemhueWksei0pSIpOw0KICAgICAgICAgIC8vIOacgOe7iOWksei0peaXtuS9v+eUqOehrOe8lueggeaVsOaNruS9nOS4uuWkh+mAiQ0KICAgICAgICAgIHRoaXMuY29udHJhY3Rvck9wdGlvbnMgPSBbDQogICAgICAgICAgICB7IHZhbHVlOiAiMSIsIGxhYmVsOiAi5pm65Yib5py65qKw6ZuG5ZuiIiB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogIjIiLCBsYWJlbDogIueyvuW3peeUteWtkOiuvuWkh+WItumAoOmbhuWboiIgfSwNCiAgICAgICAgICBdOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSb3dzID0gc2VsZWN0aW9uOw0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmxvYWRBdmFpbGFibGVDb250cmFjdG9ycygpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5om/5YyF5ZWG6buR5ZCN5Y2VIjsNCiAgICB9LA0KICAgIC8qKiDliqDovb3lj6/nlKjnmoTmib/ljIXllYbliJfooajvvIjlj6/liqDlhaXpu5HlkI3ljZXnmoTmib/ljIXllYbvvIkgKi8NCiAgICBsb2FkQXZhaWxhYmxlQ29udHJhY3RvcnMoKSB7DQogICAgICAvLyDkvb/nlKjmlrDmjqXlj6Pojrflj5blj6/liqDlhaXpu5HlkI3ljZXnmoTmib/ljIXllYYNCiAgICAgIGdldENvbnRyYWN0b3JJbmZvKDEpDQogICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIC8vIOagueaNruaOpeWPo+i/lOWbnueahOaVsOaNrue7k+aehOWkhOeQhg0KICAgICAgICAgIGlmIChyZXNwb25zZS5kYXRhICYmIEFycmF5LmlzQXJyYXkocmVzcG9uc2UuZGF0YSkpIHsNCiAgICAgICAgICAgIHRoaXMuYXZhaWxhYmxlQ29udHJhY3Rvckxpc3QgPSByZXNwb25zZS5kYXRhLm1hcCgoY29udHJhY3RvcikgPT4gKHsNCiAgICAgICAgICAgICAgaWQ6IGNvbnRyYWN0b3IuaWQsDQogICAgICAgICAgICAgIGNvbnRyYWN0b3JOYW1lOiBjb250cmFjdG9yLmNvbnRyYWN0b3JOYW1lLA0KICAgICAgICAgICAgICBjcmVkaXRDb2RlOiBjb250cmFjdG9yLmNyZWRpdENvZGUsDQogICAgICAgICAgICAgIGNvbnRyYWN0b3JUeXBlOiBjb250cmFjdG9yLmNvbnRyYWN0b3JUeXBlLA0KICAgICAgICAgICAgICBtYW5hZ2VyTmFtZTogY29udHJhY3Rvci5hZG1pbmlzdHJhdG9yTmFtZSwNCiAgICAgICAgICAgICAgcmVzcG9uc2libGVQZXJzb246IGNvbnRyYWN0b3IubGVnYWxSZXByZXNlbnRhdGl2ZSwNCiAgICAgICAgICAgIH0pKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5hdmFpbGFibGVDb250cmFjdG9yTGlzdCA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuiOt+WPluWPr+eUqOaJv+WMheWVhuWIl+ihqOWksei0pToiLCBlcnJvcik7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoIuiOt+WPluWPr+eUqOaJv+WMheWVhuWIl+ihqOWksei0pSIpOw0KICAgICAgICAgIC8vIOWksei0peaXtuS9v+eUqOWOn+acieaOpeWPo+S9nOS4uuWkh+mAiQ0KICAgICAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgICAgICBwYWdlU2l6ZTogMTAwMCwNCiAgICAgICAgICAgIGJsYWNrbGlzdFN0YXR1czogMSwNCiAgICAgICAgICB9Ow0KICAgICAgICAgIGxpc3RaakNvbnRyYWN0b3JJbmZvKHBhcmFtcykNCiAgICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLmF2YWlsYWJsZUNvbnRyYWN0b3JMaXN0ID0gcmVzcG9uc2Uucm93cyB8fCBbXTsNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLmF2YWlsYWJsZUNvbnRyYWN0b3JMaXN0ID0gW107DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICAvLyDkvb/nlKjmib/ljIXllYbkv6Hmga/mjqXlj6Pojrflj5bmlbDmja4NCiAgICAgIGdldFpqQ29udHJhY3RvckluZm8oaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55om/5YyF5ZWG6buR5ZCN5Y2VIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgLy8g5L+u5pS56buR5ZCN5Y2V5Y6f5ZugDQogICAgICAgICAgICBjb25zdCB1cGRhdGVEYXRhID0gew0KICAgICAgICAgICAgICBpZDogdGhpcy5mb3JtLmlkLA0KICAgICAgICAgICAgICBibGFja2xpc3RSZWFzb246IHRoaXMuZm9ybS5ibGFja2xpc3RSZWFzb24sDQogICAgICAgICAgICB9Ow0KICAgICAgICAgICAgdXBkYXRlWmpDb250cmFjdG9ySW5mbyh1cGRhdGVEYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaWsOWinuWIsOm7keWQjeWNle+8muS9v+eUqOS/ruaUueaJv+WMheWVhuS/oeaBr+aOpeWPow0KICAgICAgICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHsNCiAgICAgICAgICAgICAgaWQ6IHRoaXMuZm9ybS5jb250cmFjdG9ySWQsDQogICAgICAgICAgICAgIGJsYWNrbGlzdFN0YXR1czogMiwNCiAgICAgICAgICAgICAgYmxhY2tsaXN0UmVhc29uOiB0aGlzLmZvcm0uYmxhY2tsaXN0UmVhc29uLA0KICAgICAgICAgICAgfTsNCiAgICAgICAgICAgIHVwZGF0ZVpqQ29udHJhY3RvckluZm8odXBkYXRlRGF0YSkNCiAgICAgICAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5bey5oiQ5Yqf5bCG5om/5YyF5ZWG5Yqg5YWl6buR5ZCN5Y2VIik7DQogICAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCLliqDlhaXpu5HlkI3ljZXlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLliqDlhaXpu5HlkI3ljZXlpLHotKXvvIzor7fnqI3lkI7ph43or5UiKTsNCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5om/5YyF5ZWG6buR5ZCN5Y2V57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsWmpDb250cmFjdG9yQmxha2xpc3QoaWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICJjb250cmFjdG9yL3pqQ29udHJhY3RvckJsYWtsaXN0L2V4cG9ydCIsDQogICAgICAgIHsNCiAgICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zLA0KICAgICAgICB9LA0KICAgICAgICBgempDb250cmFjdG9yQmxha2xpc3RfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICk7DQogICAgfSwNCg0KICAgIC8qKiDnp7vlh7rpu5HlkI3ljZXmk43kvZwgKi8NCiAgICBoYW5kbGVSZW1vdmVGcm9tQmxhY2tsaXN0KHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+ehruWumuimgeWwhiInICsgcm93LmNvbnRyYWN0b3JOYW1lICsgJyLnp7vlh7rpu5HlkI3ljZXlkJfvvJ8nKQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgLy8g5L2/55So5L+u5pS55om/5YyF5ZWG5L+h5oGv5o6l5Y+j77yM5bCGYmxhY2tsaXN0U3RhdHVz6K6+5Li6Me+8iOato+W4uOeKtuaAge+8iQ0KICAgICAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7DQogICAgICAgICAgICBpZDogcm93LmlkLA0KICAgICAgICAgICAgYmxhY2tsaXN0U3RhdHVzOiAxLA0KICAgICAgICAgIH07DQogICAgICAgICAgcmV0dXJuIHVwZGF0ZVpqQ29udHJhY3RvckluZm8odXBkYXRlRGF0YSk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLnp7vlh7rmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KDQogICAgLy8gPT09PT0g5om/5YyF5ZWG5Lq65ZGY55u45YWz5pa55rOVID09PT09DQogICAgLyoqIOafpeivouaJv+WMheWVhuS6uuWRmOWIl+ihqCAqLw0KICAgIGdldFBlcnNvbm5lbExpc3QoKSB7DQogICAgICB0aGlzLnBlcnNvbm5lbExvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFpqQ29udHJhY3RvckJsYWtsaXN0KHRoaXMucGVyc29ubmVsUXVlcnlQYXJhbXMpDQogICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIHRoaXMucGVyc29ubmVsTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy5wZXJzb25uZWxUb3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIHRoaXMucGVyc29ubmVsTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNruS9nOS4umZhbGxiYWNrDQogICAgICAgICAgY29uc29sZS53YXJuKCLmib/ljIXllYbkurrlkZjpu5HlkI3ljZVBUEnosIPnlKjlpLHotKXvvIzkvb/nlKjmqKHmi5/mlbDmja4iKTsNCiAgICAgICAgICB0aGlzLmluaXRQZXJzb25uZWxNb2NrRGF0YSgpOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDkurrlkZjmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVQZXJzb25uZWxRdWVyeSgpIHsNCiAgICAgIHRoaXMucGVyc29ubmVsUXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldFBlcnNvbm5lbExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDkurrlkZjph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFBlcnNvbm5lbFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInBlcnNvbm5lbFF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVQZXJzb25uZWxRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5Lq65ZGY5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlUGVyc29ubmVsU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZFBlcnNvbm5lbFJvd3MgPSBzZWxlY3Rpb247DQogICAgICB0aGlzLnBlcnNvbm5lbElkcyA9IHNlbGVjdGlvbi5tYXAoKGl0ZW0pID0+IGl0ZW0uaWQpOw0KICAgIH0sDQogICAgLyoqIOa3u+WKoOS6uuWRmOWIsOm7keWQjeWNleaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZFBlcnNvbm5lbCgpIHsNCiAgICAgIHRoaXMuaW5pdEF2YWlsYWJsZVBlcnNvbm5lbExpc3QoKTsNCiAgICAgIHRoaXMuYmxhY2tsaXN0RGlhbG9nT3BlbiA9IHRydWU7DQogICAgICAvLyDlnKjkuIvkuIDkuKp0aWNr5Lit6YeN572u6KGo5Y2V77yM56Gu5L+dRE9N5bey5riy5p+TDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMucmVzZXRCbGFja2xpc3RGb3JtKCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmt7vliqDmib/ljIXllYbkurrlkZjmk43kvZwgKi8NCiAgICBoYW5kbGVBZGROZXdQZXJzb25uZWwoKSB7DQogICAgICB0aGlzLnJlc2V0UGVyc29ubmVsKCk7DQogICAgICB0aGlzLnBlcnNvbm5lbE9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy5wZXJzb25uZWxUaXRsZSA9ICLmt7vliqDmib/ljIXllYbkurrlkZgiOw0KICAgIH0sDQogICAgLyoqIOe8lui+keS6uuWRmOaTjeS9nCAqLw0KICAgIGhhbmRsZUVkaXRQZXJzb25uZWwocm93KSB7DQogICAgICB0aGlzLnJlc2V0UGVyc29ubmVsKCk7DQogICAgICB0aGlzLnBlcnNvbm5lbEZvcm0gPSB7DQogICAgICAgIGlkOiByb3cuaWQsDQogICAgICAgIHBlcnNvbm5lbE5hbWU6IHJvdy5wZXJzb25uZWxOYW1lLA0KICAgICAgICBwZXJzb25uZWxQaG9uZTogcm93LnBlcnNvbm5lbFBob25lLA0KICAgICAgICBpZE51bWJlcjogcm93LmlkTnVtYmVyLA0KICAgICAgICBjb250cmFjdG9ySWQ6IHJvdy5jb250cmFjdG9ySWQgfHwgIjEiLA0KICAgICAgICBwZXJzb25uZWxTdGF0dXM6IHJvdy5wZXJzb25uZWxTdGF0dXMsDQogICAgICB9Ow0KICAgICAgdGhpcy5wZXJzb25uZWxPcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMucGVyc29ubmVsVGl0bGUgPSAi5L+u5pS55om/5YyF5ZWG5Lq65ZGYIjsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTkurrlkZjmk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGVQZXJzb25uZWwocm93KSB7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn56Gu5a6a6KaB5Yig6Zmk5Lq65ZGYIicgKyByb3cucGVyc29ubmVsTmFtZSArICci5ZCX77yfJykNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHJldHVybiBkZWxaakNvbnRyYWN0b3JCbGFrbGlzdChyb3cuaWQpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgICAgdGhpcy5nZXRQZXJzb25uZWxMaXN0KCk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBpZiAoZXJyb3IgIT09ICJjYW5jZWwiKSB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi5Yig6Zmk5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VIik7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rkurrlkZjmk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnRQZXJzb25uZWwoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAiY29udHJhY3Rvci96akNvbnRyYWN0b3JCbGFrbGlzdC9leHBvcnQiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5wZXJzb25uZWxRdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYOaJv+WMheWVhuS6uuWRmOm7keWQjeWNlV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKTsNCiAgICB9LA0KICAgIC8qKiDmoLzlvI/ljJbouqvku73or4Hlj7cgLSDkuK3pl7TnlKgq5Y+36ZqQ6JePICovDQogICAgZm9ybWF0SWROdW1iZXIoaWROdW1iZXIpIHsNCiAgICAgIGlmICghaWROdW1iZXIpIHJldHVybiAiIjsNCiAgICAgIGlmIChpZE51bWJlci5sZW5ndGggPCA4KSByZXR1cm4gaWROdW1iZXI7DQogICAgICByZXR1cm4gKA0KICAgICAgICBpZE51bWJlci5zdWJzdHJpbmcoMCwgMykgKw0KICAgICAgICAiKioqKioqKioqKioiICsNCiAgICAgICAgaWROdW1iZXIuc3Vic3RyaW5nKGlkTnVtYmVyLmxlbmd0aCAtIDQpDQogICAgICApOw0KICAgIH0sDQoNCiAgICAvLyA9PT09PSDkurrlkZjlvLnnqpfnm7jlhbPmlrnms5UgPT09PT0NCiAgICAvKiog5Y+W5raI5Lq65ZGY5by556qXICovDQogICAgY2FuY2VsUGVyc29ubmVsKCkgew0KICAgICAgdGhpcy5wZXJzb25uZWxPcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0UGVyc29ubmVsKCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5Lq65ZGY6KGo5Y2VICovDQogICAgcmVzZXRQZXJzb25uZWwoKSB7DQogICAgICB0aGlzLnBlcnNvbm5lbEZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwZXJzb25uZWxOYW1lOiBudWxsLA0KICAgICAgICBwZXJzb25uZWxQaG9uZTogbnVsbCwNCiAgICAgICAgaWROdW1iZXI6IG51bGwsDQogICAgICAgIGNvbnRyYWN0b3JJZDogbnVsbCwNCiAgICAgICAgcGVyc29ubmVsU3RhdHVzOiAiMCIsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oInBlcnNvbm5lbEZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTkurrlkZjooajljZUgKi8NCiAgICBzdWJtaXRQZXJzb25uZWxGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1sicGVyc29ubmVsRm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5wZXJzb25uZWxGb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIC8vIOS/ruaUuQ0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLnBlcnNvbm5lbE9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuZ2V0UGVyc29ubmVsTGlzdCgpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyDmlrDlop4NCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5wZXJzb25uZWxPcGVuID0gZmFsc2U7DQogICAgICAgICAgICB0aGlzLmdldFBlcnNvbm5lbExpc3QoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICAvLyA9PT09PSDliqDlhaXpu5HlkI3ljZXnm7jlhbPmlrnms5UgPT09PT0NCiAgICAvKiog5Yid5aeL5YyW5Y+v6YCJ5oup55qE5Lq65ZGY5YiX6KGoICovDQogICAgaW5pdEF2YWlsYWJsZVBlcnNvbm5lbExpc3QoKSB7DQogICAgICAvLyDosIPnlKhBUEnojrflj5blj6/mi4npu5HnmoTmib/ljIXllYbkurrlkZjkv6Hmga8NCiAgICAgIGdldFVzZXJJbmZvKDEpDQogICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgIHRoaXMuYXZhaWxhYmxlUGVyc29ubmVsTGlzdCA9DQogICAgICAgICAgICByZXNwb25zZS5kYXRhIHx8IHJlc3BvbnNlLnJvd3MgfHwgcmVzcG9uc2UgfHwgW107DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5blj6/pgInmi6nkurrlkZjliJfooajlpLHotKU6IiwgZXJyb3IpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLojrflj5blj6/pgInmi6nkurrlkZjliJfooajlpLHotKUiKTsNCiAgICAgICAgICAvLyDlpLHotKXml7bkvb/nlKjnjrDmnInkurrlkZjliJfooajkvZzkuLrlpIfpgIkNCiAgICAgICAgICB0aGlzLmF2YWlsYWJsZVBlcnNvbm5lbExpc3QgPSB0aGlzLnBlcnNvbm5lbExpc3QuZmlsdGVyKChwZXJzb24pID0+IHsNCiAgICAgICAgICAgIHJldHVybiBwZXJzb24ucGVyc29ubmVsU3RhdHVzID09PSAiMCI7IC8vIOWPquaYvuekuuWcqOiBjOS6uuWRmA0KICAgICAgICAgIH0pOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDph43nva7pu5HlkI3ljZXooajljZUgKi8NCiAgICByZXNldEJsYWNrbGlzdEZvcm0oKSB7DQogICAgICB0aGlzLmJsYWNrbGlzdEZvcm0gPSB7DQogICAgICAgIHBlcnNvbm5lbElkOiBudWxsLA0KICAgICAgICBibGFja2xpc3RSZWFzb246ICIiLA0KICAgICAgfTsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuYmxhY2tsaXN0Rm9ybSkgew0KICAgICAgICAgIHRoaXMuJHJlZnMuYmxhY2tsaXN0Rm9ybS5yZXNldEZpZWxkcygpOw0KICAgICAgICAgIHRoaXMuJHJlZnMuYmxhY2tsaXN0Rm9ybS5jbGVhclZhbGlkYXRlKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWPlua2iOm7keWQjeWNleW8ueeqlyAqLw0KICAgIGNhbmNlbEJsYWNrbGlzdCgpIHsNCiAgICAgIHRoaXMuYmxhY2tsaXN0RGlhbG9nT3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldEJsYWNrbGlzdEZvcm0oKTsNCiAgICB9LA0KICAgIC8qKiDkurrlkZjpgInmi6nlj5jljJblpITnkIYgKi8NCiAgICBoYW5kbGVQZXJzb25uZWxTZWxlY3RDaGFuZ2Uoc2VsZWN0ZWRJZCkgew0KICAgICAgY29uc29sZS5sb2coIumAieaLqeeahOS6uuWRmElEOiIsIHNlbGVjdGVkSWQpOw0KICAgICAgLy8g56uL5Y2z6aqM6K+BcGVyc29ubmVs5a2X5q6177yM5aaC5p6c5pyJ5YC85YiZ5riF6Zmk6ZSZ6K+vDQogICAgICBpZiAodGhpcy4kcmVmcy5ibGFja2xpc3RGb3JtKSB7DQogICAgICAgIGlmIChzZWxlY3RlZElkKSB7DQogICAgICAgICAgdGhpcy4kcmVmcy5ibGFja2xpc3RGb3JtLmNsZWFyVmFsaWRhdGUoInBlcnNvbm5lbElkIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g5aaC5p6c5riF56m66YCJ5oup77yM56uL5Y2z6Kem5Y+R6aqM6K+B5pi+56S66ZSZ6K+vDQogICAgICAgICAgdGhpcy4kcmVmcy5ibGFja2xpc3RGb3JtLnZhbGlkYXRlRmllbGQoInBlcnNvbm5lbElkIik7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDmj5DkuqTpu5HlkI3ljZXooajljZUgKi8NCiAgICBzdWJtaXRCbGFja2xpc3RGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siYmxhY2tsaXN0Rm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAvLyDnm7TmjqXosIPnlKjmt7vliqDmjqXlj6PvvIzkuI3pnIDopoHnoa7orqTmj5DnpLoNCiAgICAgICAgICB0aGlzLmFkZFRvQmxhY2tsaXN0KCk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fmo4Dmn6XooajljZXloavlhpnmmK/lkKbmraPnoa4iKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaJp+ihjOWKoOWFpem7keWQjeWNleaTjeS9nCAqLw0KICAgIGFkZFRvQmxhY2tsaXN0KCkgew0KICAgICAgY29uc3Qgc2VsZWN0ZWRQZXJzb24gPSB0aGlzLmF2YWlsYWJsZVBlcnNvbm5lbExpc3QuZmluZCgNCiAgICAgICAgKHBlcnNvbikgPT4gcGVyc29uLnVzZXJJZCA9PT0gdGhpcy5ibGFja2xpc3RGb3JtLnBlcnNvbm5lbElkDQogICAgICApOw0KDQogICAgICBpZiAoIXNlbGVjdGVkUGVyc29uKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7fpgInmi6nopoHmi4npu5HnmoTkurrlkZgiKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICBjb25zdCByZXF1ZXN0RGF0YSA9IHsNCiAgICAgICAgcGVyc29ubmVsSWQ6IHRoaXMuYmxhY2tsaXN0Rm9ybS5wZXJzb25uZWxJZCwNCiAgICAgICAgYmxhY2tsaXN0UmVhc29uOiB0aGlzLmJsYWNrbGlzdEZvcm0uYmxhY2tsaXN0UmVhc29uLA0KICAgICAgICBwZXJzb25uZWxOYW1lOiBzZWxlY3RlZFBlcnNvbi5uaWNrTmFtZSwNCiAgICAgICAgc3RhdHVzOiBzZWxlY3RlZFBlcnNvbi5zdGF0dXMsDQogICAgICAgIHBlcnNvbm5lbFBob25lOiBzZWxlY3RlZFBlcnNvbi5waG9uZW51bWJlciwNCiAgICAgICAgaWROdW1iZXI6IHNlbGVjdGVkUGVyc29uLmlkTnVtYmVyLA0KICAgICAgICBibGFja2xpc3RTdGF0ZTogIjIiLA0KICAgICAgfTsNCg0KICAgICAgLy8g6LCD55SoQVBJ5bCG5Lq65ZGY5Yqg5YWl6buR5ZCN5Y2VDQogICAgICBhZGRaakNvbnRyYWN0b3JCbGFrbGlzdChyZXF1ZXN0RGF0YSkNCiAgICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2VzcygNCiAgICAgICAgICAgIGDlt7LmiJDlip/lsIYgJHtzZWxlY3RlZFBlcnNvbi5uaWNrTmFtZX0g5Yqg5YWl6buR5ZCN5Y2VYA0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy5ibGFja2xpc3REaWFsb2dPcGVuID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5yZXNldEJsYWNrbGlzdEZvcm0oKTsNCiAgICAgICAgICAvLyDliLfmlrDkurrlkZjliJfooagNCiAgICAgICAgICB0aGlzLmdldFBlcnNvbm5lbExpc3QoKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICAgIC8vIOWmguaenEFQSeiwg+eUqOWksei0pe+8jOS9v+eUqOaooeaLn+aVsOaNruWTjeW6lA0KICAgICAgICAgIGNvbnNvbGUud2FybigiQVBJ6LCD55So5aSx6LSl77yM5L2/55So5qih5ouf5ZON5bqUOiIsIGVycm9yKTsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoDQogICAgICAgICAgICAgIGDlt7LmiJDlip/lsIYgJHtzZWxlY3RlZFBlcnNvbi5uaWNrTmFtZX0g5Yqg5YWl6buR5ZCN5Y2VYA0KICAgICAgICAgICAgKTsNCiAgICAgICAgICAgIHRoaXMuYmxhY2tsaXN0RGlhbG9nT3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgdGhpcy5yZXNldEJsYWNrbGlzdEZvcm0oKTsNCiAgICAgICAgICAgIC8vIOWIt+aWsOS6uuWRmOWIl+ihqA0KICAgICAgICAgICAgdGhpcy5nZXRQZXJzb25uZWxMaXN0KCk7DQogICAgICAgICAgfSwgNTAwKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAke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file": "index.vue", "sourceRoot": "src/views/contractor/zjContractorBlaklist", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 标签页 -->\r\n    <el-tabs v-model=\"activeTab\" class=\"contractor-tabs\">\r\n      <el-tab-pane label=\"承包商\" name=\"contractor\"></el-tab-pane>\r\n      <el-tab-pane label=\"承包商人员\" name=\"personnel\"></el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <!-- 承包商人员标签页内容 -->\r\n    <div v-show=\"activeTab === 'personnel'\">\r\n      <!-- 人员搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"personnelQueryParams\"\r\n          ref=\"personnelQueryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"所属承包商：\" prop=\"contractorName\">\r\n            <el-select\r\n              v-model=\"personnelQueryParams.contractorName\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in contractorOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.label\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"关键信息：\" prop=\"keyword\">\r\n            <el-input\r\n              v-model=\"personnelQueryParams.keyword\"\r\n              placeholder=\"请输入人员\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handlePersonnelQuery\"\r\n            />\r\n          </el-form-item> -->\r\n\r\n          <el-form-item label=\"人员状态：\" prop=\"personnelStatus\">\r\n            <el-radio-group v-model=\"personnelQueryParams.personnelStatus\">\r\n              <el-radio label=\"0\">在职</el-radio>\r\n              <el-radio label=\"1\">离职</el-radio>\r\n              <el-radio label=\"\">全部</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetPersonnelQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handlePersonnelQuery\"\r\n              >查询</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商人员表格 -->\r\n      <div class=\"personnel-container\">\r\n        <div class=\"personnel-header\">\r\n          <div class=\"personnel-title\">\r\n            承包商人员列表\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedPersonnelRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"personnel-actions\">\r\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAddPersonnel\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasPersonnelSelection\"\r\n              @click=\"handleExportPersonnel\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"personnelLoading\"\r\n          :data=\"personnelList\"\r\n          @selection-change=\"handlePersonnelSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"人员姓名\"\r\n            align=\"center\"\r\n            prop=\"personnelName\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"personnelStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span\r\n                :class=\"\r\n                  scope.row.personnelStatus === '0'\r\n                    ? 'status-active'\r\n                    : 'status-inactive'\r\n                \"\r\n              >\r\n                {{ scope.row.personnelStatus === \"0\" ? \"在职\" : \"离职\" }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"联系电话\"\r\n            align=\"center\"\r\n            prop=\"personnelPhone\"\r\n          />\r\n          <el-table-column label=\"身份证号\" align=\"center\" prop=\"idNumber\">\r\n            <template slot-scope=\"scope\">\r\n              {{ formatIdNumber(scope.row.idNumber) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"所属承包商\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"150\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <!-- <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleEditPersonnel(scope.row)\"\r\n              >编辑</el-button> -->\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleDeletePersonnel(scope.row)\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"personnelTotal > 0\"\r\n        :total=\"personnelTotal\"\r\n        :page.sync=\"personnelQueryParams.pageNum\"\r\n        :limit.sync=\"personnelQueryParams.pageSize\"\r\n        @pagination=\"getPersonnelList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 承包商黑名单标签页内容 -->\r\n    <div v-show=\"activeTab === 'contractor'\">\r\n      <!-- 黑名单搜索表单 -->\r\n      <div class=\"search-form\">\r\n        <el-form\r\n          :model=\"queryParams\"\r\n          ref=\"queryForm\"\r\n          :inline=\"true\"\r\n          class=\"search-form-content\"\r\n        >\r\n          <el-form-item label=\"承包商名称：\" prop=\"contractorName\">\r\n            <el-input\r\n              v-model=\"queryParams.contractorName\"\r\n              placeholder=\"请输入内容\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"管理人：\" prop=\"administratorId\">\r\n            <el-select\r\n              v-model=\"queryParams.administratorId\"\r\n              placeholder=\"请选择\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in managerOptions\"\r\n                :key=\"item.value\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button @click=\"resetQuery\">重置</el-button>\r\n            <el-button type=\"primary\" @click=\"handleQuery\">查询</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n\r\n      <!-- 承包商黑名单区域 -->\r\n      <div class=\"blacklist-container\">\r\n        <!-- 承包商黑名单标题和操作按钮 -->\r\n        <div class=\"blacklist-header\">\r\n          <div class=\"blacklist-title\">\r\n            承包商黑名单\r\n            <span class=\"record-count\"\r\n              >已选中 {{ selectedRows.length }} 项</span\r\n            >\r\n          </div>\r\n          <div class=\"blacklist-actions\">\r\n            <el-button\r\n              type=\"primary\"\r\n              size=\"small\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:add']\"\r\n              >添加黑名单</el-button\r\n            >\r\n            <!-- <el-button \r\n              type=\"primary\" \r\n              size=\"small\"\r\n              :disabled=\"!hasSelection\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['contractor:zjContractorBlaklist:export']\"\r\n            >批量导出</el-button> -->\r\n          </div>\r\n        </div>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"zjContractorBlaklistList\"\r\n          @selection-change=\"handleSelectionChange\"\r\n          height=\"calc(100vh - 430px)\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <el-table-column\r\n            label=\"承包商名称\"\r\n            align=\"center\"\r\n            prop=\"contractorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"统一社会信用代码\"\r\n            align=\"center\"\r\n            prop=\"creditCode\"\r\n          />\r\n          <el-table-column\r\n            label=\"承包商类型\"\r\n            align=\"center\"\r\n            prop=\"contractorType\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag\r\n                :options=\"dict.type.sys_contractor_type\"\r\n                :value=\"scope.row.contractorType\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"管理人\"\r\n            align=\"center\"\r\n            prop=\"administratorName\"\r\n          />\r\n          <el-table-column\r\n            label=\"负责人\"\r\n            align=\"center\"\r\n            prop=\"contractorManager\"\r\n          />\r\n          <el-table-column\r\n            label=\"黑名单原因\"\r\n            align=\"center\"\r\n            prop=\"blacklistReason\"\r\n          />\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"100\"\r\n            fixed=\"right\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                @click=\"handleRemoveFromBlacklist(scope.row)\"\r\n                v-hasPermi=\"['contractor:zjContractorBlaklist:remove']\"\r\n                >移出</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <pagination\r\n        v-show=\"total > 0\"\r\n        :total=\"total\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </div>\r\n\r\n    <!-- 添加或修改承包商黑名单对话框 -->\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      append-to-body\r\n      class=\"contractor-blacklist-dialog\"\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商将不能被加入到应用项目中，取消对应项目人员权限。默认承包商管理人进行审批。\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"拉黑承包商\" prop=\"contractorId\" v-if=\"!form.id\">\r\n          <el-select\r\n            v-model=\"form.contractorId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handleContractorSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"contractor in availableContractorList\"\r\n              :key=\"contractor.id\"\r\n              :label=\"contractor.contractorName\"\r\n              :value=\"contractor.id\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"承包商名称\" prop=\"contractorName\" v-if=\"form.id\">\r\n          <el-input v-model=\"form.contractorName\" disabled />\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"form.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改承包商人员对话框 -->\r\n    <el-dialog\r\n      :title=\"personnelTitle\"\r\n      :visible.sync=\"personnelOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"personnelForm\"\r\n        :model=\"personnelForm\"\r\n        :rules=\"personnelRules\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"人员姓名\" prop=\"personnelName\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelName\"\r\n            placeholder=\"请输入人员姓名\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"联系电话\" prop=\"personnelPhone\">\r\n          <el-input\r\n            v-model=\"personnelForm.personnelPhone\"\r\n            placeholder=\"请输入联系电话\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"身份证号\" prop=\"idNumber\">\r\n          <el-input\r\n            v-model=\"personnelForm.idNumber\"\r\n            placeholder=\"请输入身份证号\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"所属承包商\" prop=\"contractorId\">\r\n          <el-select\r\n            v-model=\"personnelForm.contractorId\"\r\n            placeholder=\"请选择承包商\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in contractorOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"人员状态\" prop=\"personnelStatus\">\r\n          <el-radio-group v-model=\"personnelForm.personnelStatus\">\r\n            <el-radio label=\"0\">在职</el-radio>\r\n            <el-radio label=\"1\">离职</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitPersonnelForm\">确 定</el-button>\r\n        <el-button @click=\"cancelPersonnel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 加入黑名单对话框 -->\r\n    <el-dialog\r\n      title=\"加入黑名单\"\r\n      :visible.sync=\"blacklistDialogOpen\"\r\n      width=\"500px\"\r\n      append-to-body\r\n    >\r\n      <!-- 警告提示 -->\r\n      <el-alert\r\n        title=\"\"\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n        style=\"margin-bottom: 20px\"\r\n      >\r\n        <template slot=\"title\">\r\n          <span style=\"color: #e6a23c; font-size: 14px\">\r\n            黑名单承包商人员将不能被新加入到项目中，并在现有项目中被列为黑名单人员，取消人工权限。默认承包商管理人进行审批\r\n          </span>\r\n        </template>\r\n      </el-alert>\r\n\r\n      <el-form\r\n        ref=\"blacklistForm\"\r\n        :model=\"blacklistForm\"\r\n        :rules=\"blacklistRules\"\r\n        label-width=\"130px\"\r\n      >\r\n        <el-form-item label=\"拉黑承包商人员\" prop=\"personnelId\">\r\n          <el-select\r\n            v-model=\"blacklistForm.personnelId\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n            @change=\"handlePersonnelSelectChange\"\r\n          >\r\n            <el-option\r\n              v-for=\"person in availablePersonnelList\"\r\n              :key=\"person.userId\"\r\n              :label=\"person.nickName\"\r\n              :value=\"person.userId\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"拉黑原因\" prop=\"blacklistReason\">\r\n          <el-input\r\n            v-model=\"blacklistForm.blacklistReason\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n            :rows=\"4\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelBlacklist\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitBlacklistForm\">确认</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjContractorBlaklist,\r\n  getZjContractorBlaklist,\r\n  delZjContractorBlaklist,\r\n  addZjContractorBlaklist,\r\n  updateZjContractorBlaklist,\r\n  getUserInfo,\r\n} from \"@/api/contractor/zjContractorBlaklist\";\r\nimport {\r\n  listZjContractorInfo,\r\n  getZjContractorInfo,\r\n  updateZjContractorInfo,\r\n  getContractorInfo,\r\n} from \"@/api/contractor/zjContractorInfo\";\r\n\r\nexport default {\r\n  name: \"ZjContractorBlaklist\",\r\n  dicts: [\"sys_contractor_type\"],\r\n  data() {\r\n    return {\r\n      // 当前活动标签页\r\n      activeTab: \"contractor\",\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 选中的行数据\r\n      selectedRows: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 承包商黑名单表格数据\r\n      zjContractorBlaklistList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 承包商选项\r\n      contractorOptions: [],\r\n      // 可添加到黑名单的承包商列表\r\n      availableContractorList: [],\r\n      // 管理人选项\r\n      managerOptions: [],\r\n      // 承包商人员相关数据\r\n      personnelLoading: true,\r\n      selectedPersonnelRows: [],\r\n      personnelIds: [],\r\n      personnelTotal: 0,\r\n      personnelList: [],\r\n      personnelQueryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        personnelStatus: \"\",\r\n      },\r\n      // 承包商黑名单查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        contractorName: null,\r\n        administratorId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        contractorId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要加入黑名单的承包商\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          { required: true, message: \"请输入黑名单原因\", trigger: \"blur\" },\r\n          { min: 5, message: \"黑名单原因至少需要5个字符\", trigger: \"blur\" },\r\n          { max: 500, message: \"黑名单原因不能超过500个字符\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      // 人员弹窗相关\r\n      personnelOpen: false,\r\n      personnelTitle: \"\",\r\n      personnelForm: {},\r\n      personnelRules: {\r\n        personnelName: [\r\n          { required: true, message: \"人员姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personnelPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        idNumber: [\r\n          { required: true, message: \"身份证号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractorId: [\r\n          { required: true, message: \"请选择所属承包商\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      // 加入黑名单弹窗相关\r\n      blacklistDialogOpen: false,\r\n      blacklistForm: {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      },\r\n      blacklistRules: {\r\n        personnelId: [\r\n          {\r\n            required: true,\r\n            message: \"请选择要拉黑的承包商人员\",\r\n            trigger: [\"change\", \"blur\"],\r\n          },\r\n        ],\r\n        blacklistReason: [\r\n          {\r\n            required: true,\r\n            message: \"请输入拉黑原因\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            min: 5,\r\n            message: \"拉黑原因至少需要5个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n          {\r\n            max: 500,\r\n            message: \"拉黑原因不能超过500个字符\",\r\n            trigger: [\"blur\", \"change\"],\r\n          },\r\n        ],\r\n      },\r\n      // 可选择的人员列表（未在黑名单中的人员）\r\n      availablePersonnelList: [],\r\n    };\r\n  },\r\n  computed: {\r\n    // 是否有选中项\r\n    hasSelection() {\r\n      return this.ids.length > 0;\r\n    },\r\n    // 是否有选中的人员项\r\n    hasPersonnelSelection() {\r\n      return this.personnelIds.length > 0;\r\n    },\r\n  },\r\n  created() {\r\n    // 初始化两个标签页的数据\r\n    this.getPersonnelList();\r\n    this.getList();\r\n    // 加载管理人选项\r\n    this.loadManagerOptions();\r\n    // 加载承包商选项\r\n    this.loadContractorOptions();\r\n  },\r\n  methods: {\r\n    /** 初始化承包商人员模拟数据 */\r\n    initPersonnelMockData() {\r\n      this.personnelList = [\r\n        {\r\n          id: 1,\r\n          personnelName: \"张三\",\r\n          personnelStatus: \"0\",\r\n          personnelPhone: \"13800138000\",\r\n          idNumber: \"320123199001011234\",\r\n          contractorName: \"智创机械集团\",\r\n        },\r\n        {\r\n          id: 2,\r\n          personnelName: \"李四\",\r\n          personnelStatus: \"1\",\r\n          personnelPhone: \"13900139000\",\r\n          idNumber: \"320123199002021235\",\r\n          contractorName: \"精工电子设备制造集团\",\r\n        },\r\n      ];\r\n      this.personnelTotal = 2;\r\n      this.personnelLoading = false;\r\n    },\r\n    /** 初始化承包商黑名单模拟数据 */\r\n    initMockData() {\r\n      this.zjContractorBlaklistList = [\r\n        {\r\n          id: 1,\r\n          contractorName: \"广东明华工程有限公司\",\r\n          creditCode: \"91320736617893075\",\r\n          contractorType: \"1\",\r\n          managerName: \"智威科技智慧工厂\",\r\n          responsiblePerson: \"王佳明\",\r\n          blacklistReason: \"1\",\r\n        },\r\n      ];\r\n      this.total = 1;\r\n      this.loading = false;\r\n    },\r\n    /** 查询承包商黑名单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      // 使用承包商信息接口，传blacklistStatus=2查询黑名单\r\n      const params = {\r\n        ...this.queryParams,\r\n        blacklistStatus: 2,\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          this.zjContractorBlaklistList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据\r\n          this.initMockData();\r\n        });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        contractorId: null,\r\n        contractorName: null,\r\n        creditCode: null,\r\n        contractorType: null,\r\n        managerName: null,\r\n        responsiblePerson: null,\r\n        blacklistReason: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 承包商选择变化处理 */\r\n    handleContractorSelectChange(contractorId) {\r\n      if (contractorId) {\r\n        // 根据选择的承包商ID填充承包商信息\r\n        const selectedContractor = this.availableContractorList.find(\r\n          (contractor) => contractor.id === contractorId\r\n        );\r\n        if (selectedContractor) {\r\n          this.form.contractorName = selectedContractor.contractorName;\r\n          this.form.creditCode = selectedContractor.creditCode;\r\n          this.form.contractorType = selectedContractor.contractorType;\r\n          this.form.managerName = selectedContractor.managerName;\r\n          this.form.responsiblePerson = selectedContractor.responsiblePerson;\r\n        }\r\n      } else {\r\n        // 清空承包商信息\r\n        this.form.contractorName = null;\r\n        this.form.creditCode = null;\r\n        this.form.contractorType = null;\r\n        this.form.managerName = null;\r\n        this.form.responsiblePerson = null;\r\n      }\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 加载管理人选项 */\r\n    loadManagerOptions() {\r\n      // 使用getUserInfo接口获取管理人员数据，传递type=1表示获取管理人员\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.data || response.rows || response || [];\r\n          this.managerOptions = data.map((manager) => ({\r\n            value: manager.userId || manager.id,\r\n            label: manager.nickName || manager.name || manager.userName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取管理人选项失败:\", error);\r\n          this.$modal.msgError(\"获取管理人选项失败\");\r\n          // 失败时使用原有硬编码数据作为备选\r\n          this.managerOptions = [\r\n            { value: \"1\", label: \"王佳明\" },\r\n            { value: \"2\", label: \"李东\" },\r\n            { value: \"3\", label: \"张伟\" },\r\n          ];\r\n        });\r\n    },\r\n    /** 加载承包商选项 */\r\n    loadContractorOptions() {\r\n      // 使用承包商信息查询接口获取所有承包商数据\r\n      const params = {\r\n        pageNum: 1,\r\n        pageSize: 1000, // 获取足够多的数据\r\n      };\r\n      listZjContractorInfo(params)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          const data = response.rows || response.data || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败:\", error);\r\n          // 失败时使用备用接口\r\n          this.loadContractorOptionsFallback();\r\n        });\r\n    },\r\n    /** 备用方法：使用另一个接口加载承包商选项 */\r\n    loadContractorOptionsFallback() {\r\n      // 使用getContractorInfo接口作为备选，参数为null获取所有承包商\r\n      getContractorInfo(null)\r\n        .then((response) => {\r\n          const data = response.data || response.rows || response || [];\r\n          this.contractorOptions = data.map((contractor) => ({\r\n            value: contractor.id,\r\n            label: contractor.contractorName,\r\n          }));\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取承包商选项失败（备用接口）:\", error);\r\n          this.$modal.msgError(\"获取承包商选项失败\");\r\n          // 最终失败时使用硬编码数据作为备选\r\n          this.contractorOptions = [\r\n            { value: \"1\", label: \"智创机械集团\" },\r\n            { value: \"2\", label: \"精工电子设备制造集团\" },\r\n          ];\r\n        });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection;\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.loadAvailableContractors();\r\n      this.open = true;\r\n      this.title = \"添加承包商黑名单\";\r\n    },\r\n    /** 加载可用的承包商列表（可加入黑名单的承包商） */\r\n    loadAvailableContractors() {\r\n      // 使用新接口获取可加入黑名单的承包商\r\n      getContractorInfo(1)\r\n        .then((response) => {\r\n          // 根据接口返回的数据结构处理\r\n          if (response.data && Array.isArray(response.data)) {\r\n            this.availableContractorList = response.data.map((contractor) => ({\r\n              id: contractor.id,\r\n              contractorName: contractor.contractorName,\r\n              creditCode: contractor.creditCode,\r\n              contractorType: contractor.contractorType,\r\n              managerName: contractor.administratorName,\r\n              responsiblePerson: contractor.legalRepresentative,\r\n            }));\r\n          } else {\r\n            this.availableContractorList = [];\r\n          }\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可用承包商列表失败:\", error);\r\n          this.$modal.msgError(\"获取可用承包商列表失败\");\r\n          // 失败时使用原有接口作为备选\r\n          const params = {\r\n            pageNum: 1,\r\n            pageSize: 1000,\r\n            blacklistStatus: 1,\r\n          };\r\n          listZjContractorInfo(params)\r\n            .then((response) => {\r\n              this.availableContractorList = response.rows || [];\r\n            })\r\n            .catch(() => {\r\n              this.availableContractorList = [];\r\n            });\r\n        });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      // 使用承包商信息接口获取数据\r\n      getZjContractorInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改承包商黑名单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            // 修改黑名单原因\r\n            const updateData = {\r\n              id: this.form.id,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // 新增到黑名单：使用修改承包商信息接口\r\n            const updateData = {\r\n              id: this.form.contractorId,\r\n              blacklistStatus: 2,\r\n              blacklistReason: this.form.blacklistReason,\r\n            };\r\n            updateZjContractorInfo(updateData)\r\n              .then((response) => {\r\n                this.$modal.msgSuccess(\"已成功将承包商加入黑名单\");\r\n                this.open = false;\r\n                this.getList();\r\n              })\r\n              .catch((error) => {\r\n                console.error(\"加入黑名单失败:\", error);\r\n                this.$modal.msgError(\"加入黑名单失败，请稍后重试\");\r\n              });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除承包商黑名单编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjContractorBlaklist(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjContractorBlaklist_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n\r\n    /** 移出黑名单操作 */\r\n    handleRemoveFromBlacklist(row) {\r\n      this.$modal\r\n        .confirm('确定要将\"' + row.contractorName + '\"移出黑名单吗？')\r\n        .then(() => {\r\n          // 使用修改承包商信息接口，将blacklistStatus设为1（正常状态）\r\n          const updateData = {\r\n            id: row.id,\r\n            blacklistStatus: 1,\r\n          };\r\n          return updateZjContractorInfo(updateData);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"移出成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n\r\n    // ===== 承包商人员相关方法 =====\r\n    /** 查询承包商人员列表 */\r\n    getPersonnelList() {\r\n      this.personnelLoading = true;\r\n      listZjContractorBlaklist(this.personnelQueryParams)\r\n        .then((response) => {\r\n          this.personnelList = response.rows;\r\n          this.personnelTotal = response.total;\r\n          this.personnelLoading = false;\r\n        })\r\n        .catch(() => {\r\n          // 如果API调用失败，使用模拟数据作为fallback\r\n          console.warn(\"承包商人员黑名单API调用失败，使用模拟数据\");\r\n          this.initPersonnelMockData();\r\n        });\r\n    },\r\n    /** 人员搜索按钮操作 */\r\n    handlePersonnelQuery() {\r\n      this.personnelQueryParams.pageNum = 1;\r\n      this.getPersonnelList();\r\n    },\r\n    /** 人员重置按钮操作 */\r\n    resetPersonnelQuery() {\r\n      this.resetForm(\"personnelQueryForm\");\r\n      this.handlePersonnelQuery();\r\n    },\r\n    // 人员多选框选中数据\r\n    handlePersonnelSelectionChange(selection) {\r\n      this.selectedPersonnelRows = selection;\r\n      this.personnelIds = selection.map((item) => item.id);\r\n    },\r\n    /** 添加人员到黑名单操作 */\r\n    handleAddPersonnel() {\r\n      this.initAvailablePersonnelList();\r\n      this.blacklistDialogOpen = true;\r\n      // 在下一个tick中重置表单，确保DOM已渲染\r\n      this.$nextTick(() => {\r\n        this.resetBlacklistForm();\r\n      });\r\n    },\r\n    /** 添加承包商人员操作 */\r\n    handleAddNewPersonnel() {\r\n      this.resetPersonnel();\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"添加承包商人员\";\r\n    },\r\n    /** 编辑人员操作 */\r\n    handleEditPersonnel(row) {\r\n      this.resetPersonnel();\r\n      this.personnelForm = {\r\n        id: row.id,\r\n        personnelName: row.personnelName,\r\n        personnelPhone: row.personnelPhone,\r\n        idNumber: row.idNumber,\r\n        contractorId: row.contractorId || \"1\",\r\n        personnelStatus: row.personnelStatus,\r\n      };\r\n      this.personnelOpen = true;\r\n      this.personnelTitle = \"修改承包商人员\";\r\n    },\r\n    /** 删除人员操作 */\r\n    handleDeletePersonnel(row) {\r\n      this.$modal\r\n        .confirm('确定要删除人员\"' + row.personnelName + '\"吗？')\r\n        .then(() => {\r\n          return delZjContractorBlaklist(row.id);\r\n        })\r\n        .then(() => {\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          if (error !== \"cancel\") {\r\n            this.$modal.msgError(\"删除失败，请稍后重试\");\r\n          }\r\n        });\r\n    },\r\n    /** 导出人员操作 */\r\n    handleExportPersonnel() {\r\n      this.download(\r\n        \"contractor/zjContractorBlaklist/export\",\r\n        {\r\n          ...this.personnelQueryParams,\r\n        },\r\n        `承包商人员黑名单_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    /** 格式化身份证号 - 中间用*号隐藏 */\r\n    formatIdNumber(idNumber) {\r\n      if (!idNumber) return \"\";\r\n      if (idNumber.length < 8) return idNumber;\r\n      return (\r\n        idNumber.substring(0, 3) +\r\n        \"***********\" +\r\n        idNumber.substring(idNumber.length - 4)\r\n      );\r\n    },\r\n\r\n    // ===== 人员弹窗相关方法 =====\r\n    /** 取消人员弹窗 */\r\n    cancelPersonnel() {\r\n      this.personnelOpen = false;\r\n      this.resetPersonnel();\r\n    },\r\n    /** 重置人员表单 */\r\n    resetPersonnel() {\r\n      this.personnelForm = {\r\n        id: null,\r\n        personnelName: null,\r\n        personnelPhone: null,\r\n        idNumber: null,\r\n        contractorId: null,\r\n        personnelStatus: \"0\",\r\n      };\r\n      this.resetForm(\"personnelForm\");\r\n    },\r\n    /** 提交人员表单 */\r\n    submitPersonnelForm() {\r\n      this.$refs[\"personnelForm\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.personnelForm.id != null) {\r\n            // 修改\r\n            this.$modal.msgSuccess(\"修改成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          } else {\r\n            // 新增\r\n            this.$modal.msgSuccess(\"新增成功\");\r\n            this.personnelOpen = false;\r\n            this.getPersonnelList();\r\n          }\r\n        }\r\n      });\r\n    },\r\n\r\n    // ===== 加入黑名单相关方法 =====\r\n    /** 初始化可选择的人员列表 */\r\n    initAvailablePersonnelList() {\r\n      // 调用API获取可拉黑的承包商人员信息\r\n      getUserInfo(1)\r\n        .then((response) => {\r\n          this.availablePersonnelList =\r\n            response.data || response.rows || response || [];\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"获取可选择人员列表失败:\", error);\r\n          this.$modal.msgError(\"获取可选择人员列表失败\");\r\n          // 失败时使用现有人员列表作为备选\r\n          this.availablePersonnelList = this.personnelList.filter((person) => {\r\n            return person.personnelStatus === \"0\"; // 只显示在职人员\r\n          });\r\n        });\r\n    },\r\n    /** 重置黑名单表单 */\r\n    resetBlacklistForm() {\r\n      this.blacklistForm = {\r\n        personnelId: null,\r\n        blacklistReason: \"\",\r\n      };\r\n      this.$nextTick(() => {\r\n        if (this.$refs.blacklistForm) {\r\n          this.$refs.blacklistForm.resetFields();\r\n          this.$refs.blacklistForm.clearValidate();\r\n        }\r\n      });\r\n    },\r\n    /** 取消黑名单弹窗 */\r\n    cancelBlacklist() {\r\n      this.blacklistDialogOpen = false;\r\n      this.resetBlacklistForm();\r\n    },\r\n    /** 人员选择变化处理 */\r\n    handlePersonnelSelectChange(selectedId) {\r\n      console.log(\"选择的人员ID:\", selectedId);\r\n      // 立即验证personnel字段，如果有值则清除错误\r\n      if (this.$refs.blacklistForm) {\r\n        if (selectedId) {\r\n          this.$refs.blacklistForm.clearValidate(\"personnelId\");\r\n        } else {\r\n          // 如果清空选择，立即触发验证显示错误\r\n          this.$refs.blacklistForm.validateField(\"personnelId\");\r\n        }\r\n      }\r\n    },\r\n    /** 提交黑名单表单 */\r\n    submitBlacklistForm() {\r\n      this.$refs[\"blacklistForm\"].validate((valid) => {\r\n        if (valid) {\r\n          // 直接调用添加接口，不需要确认提示\r\n          this.addToBlacklist();\r\n        } else {\r\n          this.$message.warning(\"请检查表单填写是否正确\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    /** 执行加入黑名单操作 */\r\n    addToBlacklist() {\r\n      const selectedPerson = this.availablePersonnelList.find(\r\n        (person) => person.userId === this.blacklistForm.personnelId\r\n      );\r\n\r\n      if (!selectedPerson) {\r\n        this.$modal.msgError(\"请选择要拉黑的人员\");\r\n        return;\r\n      }\r\n\r\n      const requestData = {\r\n        personnelId: this.blacklistForm.personnelId,\r\n        blacklistReason: this.blacklistForm.blacklistReason,\r\n        personnelName: selectedPerson.nickName,\r\n        status: selectedPerson.status,\r\n        personnelPhone: selectedPerson.phonenumber,\r\n        idNumber: selectedPerson.idNumber,\r\n        blacklistState: \"2\",\r\n      };\r\n\r\n      // 调用API将人员加入黑名单\r\n      addZjContractorBlaklist(requestData)\r\n        .then((response) => {\r\n          this.$modal.msgSuccess(\r\n            `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n          );\r\n          this.blacklistDialogOpen = false;\r\n          this.resetBlacklistForm();\r\n          // 刷新人员列表\r\n          this.getPersonnelList();\r\n        })\r\n        .catch((error) => {\r\n          // 如果API调用失败，使用模拟数据响应\r\n          console.warn(\"API调用失败，使用模拟响应:\", error);\r\n          setTimeout(() => {\r\n            this.$modal.msgSuccess(\r\n              `已成功将 ${selectedPerson.nickName} 加入黑名单`\r\n            );\r\n            this.blacklistDialogOpen = false;\r\n            this.resetBlacklistForm();\r\n            // 刷新人员列表\r\n            this.getPersonnelList();\r\n          }, 500);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* 标签页样式 */\r\n.contractor-tabs {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__header {\r\n  margin: 0 0 15px;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-tabs ::v-deep .el-tabs__item.is-active {\r\n  color: #409eff;\r\n}\r\n\r\n/* 搜索表单样式 */\r\n.search-form {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.search-form-content {\r\n  margin: 0;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item {\r\n  margin-bottom: 0;\r\n  margin-right: 32px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-form-item__label {\r\n  color: #606266;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 4px;\r\n  font-size: 14px;\r\n}\r\n\r\n.search-form-content ::v-deep .el-select .el-input__inner {\r\n  border: 1px solid #d9d9d9;\r\n}\r\n\r\n.search-form-content ::v-deep .el-button {\r\n  padding: 8px 24px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 黑名单容器 */\r\n.blacklist-container,\r\n.personnel-container {\r\n  background: #ffffff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n/* 黑名单标题和操作区域 */\r\n.blacklist-header,\r\n.personnel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 16px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.blacklist-title,\r\n.personnel-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  color: #262626;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n}\r\n\r\n.record-count {\r\n  font-size: 14px;\r\n  color: #8c8c8c;\r\n  font-weight: 400;\r\n}\r\n\r\n.blacklist-actions,\r\n.personnel-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.blacklist-actions .el-button,\r\n.personnel-actions .el-button {\r\n  padding: 6px 16px;\r\n  font-size: 14px;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* 表格样式 */\r\n.blacklist-container .el-table,\r\n.personnel-container .el-table {\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header {\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table ::v-deep .el-table__header th {\r\n  background-color: #fafafa !important;\r\n  color: #595959;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n  border-bottom: 1px solid #f0f0f0;\r\n  height: 48px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body td {\r\n  font-size: 14px;\r\n  color: #262626;\r\n  height: 56px;\r\n}\r\n\r\n.el-table ::v-deep .el-table__body tr:hover > td {\r\n  background-color: #f5f7fa !important;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-active {\r\n  color: #67c23a;\r\n  font-weight: 500;\r\n}\r\n\r\n.status-inactive {\r\n  color: #f56c6c;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 操作按钮样式 */\r\n.el-table ::v-deep .el-button--text {\r\n  color: #409eff;\r\n  font-weight: 500;\r\n}\r\n\r\n.el-table ::v-deep .el-button--text:hover {\r\n  color: #66b1ff;\r\n}\r\n\r\n/* 分页样式 */\r\n.pagination {\r\n  text-align: center;\r\n  margin-top: 24px;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination {\r\n  text-align: center;\r\n}\r\n\r\n.pagination ::v-deep .el-pagination__total {\r\n  color: #595959;\r\n  font-size: 14px;\r\n}\r\n\r\n/* 承包商黑名单弹窗样式 */\r\n.contractor-blacklist-dialog ::v-deep .el-dialog {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__header {\r\n  background-color: #f8f9fa;\r\n  padding: 20px 24px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  margin: 0;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-dialog__body {\r\n  padding: 24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert {\r\n  border-radius: 6px;\r\n  border: 1px solid #fadb14;\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-alert__icon {\r\n  color: #fa8c16;\r\n  font-size: 16px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-form-item__label {\r\n  color: #262626;\r\n  font-weight: 500;\r\n  font-size: 14px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner {\r\n  border: 1px solid #d9d9d9;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  color: #262626;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-input__inner:focus,\r\n.contractor-blacklist-dialog ::v-deep .el-textarea__inner:focus {\r\n  border-color: #1890ff;\r\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\r\n}\r\n\r\n.contractor-blacklist-dialog\r\n  ::v-deep\r\n  .el-select\r\n  .el-input.is-focus\r\n  .el-input__inner {\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .el-textarea .el-input__count {\r\n  background: transparent;\r\n  color: #8c8c8c;\r\n  font-size: 12px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer {\r\n  text-align: right;\r\n  padding: 16px 24px;\r\n  background-color: #fafafa;\r\n  border-top: 1px solid #e9ecef;\r\n  margin: 0 -24px -24px -24px;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button {\r\n  margin-left: 8px;\r\n  padding: 8px 24px;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary {\r\n  background-color: #1890ff;\r\n  border-color: #1890ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--primary:hover {\r\n  background-color: #40a9ff;\r\n  border-color: #40a9ff;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default {\r\n  border-color: #d9d9d9;\r\n  color: #595959;\r\n}\r\n\r\n.contractor-blacklist-dialog ::v-deep .dialog-footer .el-button--default:hover {\r\n  border-color: #40a9ff;\r\n  color: #40a9ff;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .search-form-content {\r\n    flex-direction: column;\r\n  }\r\n\r\n  .search-form-content ::v-deep .el-form-item {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .blacklist-container {\r\n    padding: 15px;\r\n  }\r\n\r\n  .blacklist-header,\r\n  .personnel-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n\r\n  .blacklist-actions,\r\n  .personnel-actions {\r\n    width: 100%;\r\n    justify-content: flex-end;\r\n  }\r\n\r\n  .contractor-blacklist-dialog ::v-deep .el-dialog {\r\n    width: 90% !important;\r\n    margin: 0 auto !important;\r\n  }\r\n}\r\n</style>\r\n"]}]}