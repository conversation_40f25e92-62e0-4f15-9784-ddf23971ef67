<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="所属分公司" prop="company">
        <selectComponyTree
          ref="chargePersonName"
          v-model="queryParams.company"
          :people-list="companyList"
          placeholder="请搜索或选择所属分公司"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          placeholder="选择年份"
          value-format="yyyy"
          size="small"
          style="width: 120px"
        />
      </el-form-item>
      <el-form-item label="开始月份" prop="start">
        <el-select
          v-model="queryParams.start"
          placeholder="选择开始月份"
          size="small"
          style="width: 120px"
          
        >
          <el-option
            v-for="month in 12"
            :key="month"
            :label="month + '月'"
            :value="month.toString()"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          @click="handlePrint"
          >打印</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      height="calc(100vh - 230px)"
      border
      stripe
    >
      <!-- 动态生成多级表头 -->
      <template v-for="group in columns">
        <!-- 如果有子级表头 -->
        <template v-if="group.children">
          <el-table-column :key="group.name" :label="group.name" align="center">
            <template v-for="subGroup in group.children">
              <!-- 如果有三级表头 -->
              <template v-if="subGroup.children">
                <el-table-column :label="subGroup.name" align="center">
                  <el-table-column
                    v-for="subColumn in subGroup.children"
                    :key="subColumn.key"
                    :prop="subColumn.key"
                    :label="subColumn.name"
                    :width="subColumn.width || 'auto'"
                    :min-width="subColumn.minWidth || 120"
                    align="center"
                    show-overflow-tooltip
                  />
                </el-table-column>
              </template>
              <!-- 如果没有三级表头，直接显示二级表头 -->
              <template v-else>
                <el-table-column
                  :key="subGroup.key || subGroup.name"
                  :prop="subGroup.key"
                  :label="subGroup.name"
                  align="center"
                  :width="group.width || 'auto'"
                  :min-width="group.minWidth || 120"
                  show-overflow-tooltip
                />
              </template>
            </template>
          </el-table-column>
        </template>
        <!-- 如果没有子级表头，直接显示一级表头 -->
        <template v-else>
          <el-table-column
            :key="group.key || group.name"
            :prop="group.key"
            :label="group.name"
            :width="group.width || 'auto'"
            :min-width="group.minWidth || 120"
            align="center"
            show-overflow-tooltip
          />
        </template>
      </template>
    </el-table>
  </div>
</template>

<script>
import { queryAccidentsStatisticView } from "@/api/report/index";
import selectComponyTree from "@/views/components/selectComponyTree.vue";
import { getEnterpriseInfo } from "@/api/system/info";
// 事故统计报表
export default {
  name: "AccidentsStatisticReport",
  components: {
    selectComponyTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格列配置
      columns: [],
      // 表格数据
      dataList: [],
      companyList: [],
      // 查询参数
      queryParams: {
        year: "2025", // 年份
        start: "1", // 开始月份
        company: "", // 所属分公司
      },
    };
  },
  computed: {
    /** 处理数据行 */
    tableData() {
      if (!this.dataList || this.dataList.length === 0) {
        return [];
      }
      

      // 直接返回所有数据行
      return this.dataList.map((item, index) => ({
        ...item,
        
      }));
    },
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },

    /** 查询事故统计报表数据 */
    getList() {
      this.loading = true;
      queryAccidentsStatisticView(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.dataList = res.data.dataList || [];
            this.total = this.dataList.length;
            this.columns = res.data.columns || [];
          } else {
            this.$message.error(res.msg || "查询失败");
            this.dataList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.dataList = [];
          this.total = 0;
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        year: "2025",
        start: "1",
        company: "",
      };
      this.getList();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "report/accidentsStatistic/export",
        {
          ...this.queryParams,
        },
        `${this.queryParams.year}年${this.queryParams.start}月份伤亡事故统计月报表.xlsx`
      );
    },

    /** 打印按钮操作 */
    handlePrint() {
      if (this.tableData.length === 0) {
        this.$message.warning("暂无数据可打印");
        return;
      }

      // 创建打印内容
      const printContent = this.generatePrintContent();

      // 创建新窗口进行打印
      const printWindow = window.open("", "_blank");
      printWindow.document.write(printContent);
      printWindow.document.close();

      // 使用setTimeout确保内容完全加载后再打印
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        // 打印对话框关闭后关闭窗口
        printWindow.onafterprint = function () {
          printWindow.close();
        };
        // 如果用户取消打印，也关闭窗口
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.close();
          }
        }, 1000);
      }, 100);
    },

    /** 生成打印内容 */
    generatePrintContent() {
      const currentDate = new Date().toLocaleDateString();
      const year = this.queryParams.year;
      const startMonth = this.queryParams.start;
      

      // 生成三级表头
      const printTable = this.generateMultiLevelTable();

      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>${year}年${startMonth}月份伤亡事故统计月报表</title>
          <style>
            body {
              font-family: "Microsoft YaHei", Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .print-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .print-info {
              font-size: 14px;
              margin-bottom: 20px;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            .print-table th,
            .print-table td {
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
            }
            .print-table th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .level1-header {
              background-color: #e3f2fd !important;
              color: #1565c0 !important;
              font-weight: bold !important;
            }
            .level2-header {
              background-color: #f3e5f5 !important;
              color: #7b1fa2 !important;
              font-weight: 600 !important;
            }
            .level3-header {
              background-color: #e8f5e8 !important;
              color: #2e7d32 !important;
              font-weight: 500 !important;
            }
            .print-footer {
              margin-top: 20px;
              text-align: right;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .print-header { page-break-after: avoid; }
              .print-table { page-break-inside: auto; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <div class="print-title">${year}年${startMonth}月份伤亡事故统计月报表</div>
            
          </div>

          ${printTable}

          <div class="print-footer">
            共 ${this.tableData.length} 条记录
          </div>
        </body>
        </html>
      `;
    },

    /** 生成三级表头的HTML表格 */
    generateMultiLevelTable() {
      if (!this.dataList || this.dataList.length === 0) {
        return "<p>暂无数据</p>";
      }

      const columns = this.columns;

      // 生成表头HTML
      let headerHtml = '<table class="print-table">';

      // 第一行表头（一级表头）
      headerHtml += "<thead><tr>";
      columns.forEach((group, index) => {
        const colspan = this.getTotalColspan(group);
        const rowspan = this.getTotalRowspan(group, 0);
        headerHtml += `<th colspan="${colspan}" rowspan="${rowspan}" >${group.name}</th>`;
      });
      headerHtml += "</tr>";

      // 第二行表头（二级表头）
      if (columns.some((group) => group.children)) {
        headerHtml += "<tr>";
        columns.forEach((group) => {
          if (group.children) {
            group.children.forEach((child) => {
              // 只有二级表头，直接显示
              const rowspan = this.getTotalRowspan(child, 1);
              const colspan = this.getTotalColspan(child);
              headerHtml += `<th rowspan="${rowspan}" colspan="${colspan}">${child.name}</th>`;
            });
          }
        });
        headerHtml += "</tr>";
      }

      // 第三行表头（三级表头）
      if (
        columns.some(
          (group) =>
            group.children && group.children.some((child) => child.children)
        )
      ) {
        headerHtml += "<tr>";
        columns.forEach((group) => {
          if (group.children) {
            group.children.forEach((child) => {
              if (child.children) {
                // 有三级表头
                child.children.forEach((subChild) => {
                  headerHtml += `<th>${subChild.name}</th>`;
                });
              }
            });
          }
        });
        headerHtml += "</tr>";
      }
      headerHtml += "</thead>";

      // 生成数据行
      headerHtml += "<tbody>";
      this.tableData.forEach((row, index) => {
        headerHtml += "<tr>";
        columns.forEach((group) => {
          if (group.children) {
            group.children.forEach((child) => {
              if (child.children) {
                // 有三级表头
                child.children.forEach((subChild) => {
                  const value = row[subChild.key] || "";
                  headerHtml += `<td>${value}</td>`;
                });
              } else {
                // 只有二级表头
                const value = row[child.key] || "";
                headerHtml += `<td>${value}</td>`;
              }
            });
          } else {
            // 没有子级表头，直接显示数据
            const value = row[group.key] || "";
            headerHtml += `<td>${value}</td>`;
          }
        });
        headerHtml += "</tr>";
      });
      headerHtml += "</tbody></table>";

      return headerHtml;
    },

    /** 计算总列数 */
    getTotalColspan(item) {
      if (item.children) {
        return item.children.reduce((total, child) => {
          return total + this.getTotalColspan(child);
        }, 0);
      }
      return 1;
    },
    // 计算总行数
    getTotalRowspan(item, curLevel) {
      // 如果item有子级，则返回1
      if (item.children) {
        return 1;
      } else {
        // 一级表头，有三级表头，返回3，有二级表头，返回2，没有子级，返回1
        if (curLevel === 0) {
          if (
            this.columns.some(
              (group) =>
                group.children && group.children.some((child) => child.children)
            )
          ) {
            return 3;
          } else if (this.columns.some((group) => group.children)) {
            return 2;
          }
          return 1;
        } else if (curLevel === 1) {
          // 二级表头，有三级表头，返回2，没有返回1
          if (
            this.columns.some(
              (group) =>
                group.children && group.children.some((child) => child.children)
            )
          ) {
            return 2;
          }
          return 1;
        }
        return 1;
      }
    },
  },
};
</script>
<style scoped lang="scss">
.app-container {
  // 三级表头样式
  :deep(.el-table) {
    .level1-group {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
      color: #1565c0 !important;
      font-weight: bold !important;
      font-size: 14px !important;
      border-right: 2px solid #1976d2 !important;

      .cell {
        background: transparent !important;
        color: #1565c0 !important;
        font-weight: bold !important;
      }
    }

    .level2-group {
      background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
      color: #7b1fa2 !important;
      font-weight: 600 !important;
      font-size: 13px !important;
      border-right: 1px solid #9c27b0 !important;

      .cell {
        background: transparent !important;
        color: #7b1fa2 !important;
        font-weight: 600 !important;
      }
    }

    .level3-group {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
      color: #2e7d32 !important;
      font-weight: 500 !important;
      font-size: 12px !important;
      border-right: 1px solid #4caf50 !important;

      .cell {
        background: transparent !important;
        color: #2e7d32 !important;
        font-weight: 500 !important;
      }
    }

    // 表头整体样式
    .el-table__header-wrapper {
      .el-table__header {
        th {
          border-bottom: 2px solid #dee2e6;

          // 一级表头样式
          &.level1-group {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border-right: 2px solid #1976d2;
            position: relative;

            &::after {
              content: "";
              position: absolute;
              right: 0;
              top: 0;
              bottom: 0;
              width: 2px;
              background: #1976d2;
            }
          }

          // 二级表头样式
          &.level2-group {
            background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
            border-right: 1px solid #9c27b0;
            position: relative;

            &::after {
              content: "";
              position: absolute;
              right: 0;
              top: 0;
              bottom: 0;
              width: 1px;
              background: #9c27b0;
            }
          }

          // 三级表头样式
          &.level3-group {
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            border-right: 1px solid #4caf50;
            position: relative;

            &::after {
              content: "";
              position: absolute;
              right: 0;
              top: 0;
              bottom: 0;
              width: 1px;
              background: #4caf50;
            }
          }

          // 普通表头样式
          .el-table__header-cell {
            background: #ffffff;
            border-right: 1px solid #dee2e6;
            font-weight: 500;
            color: #6c757d;

            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }

    // 数据行样式
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f8f9fa;
          }

          td {
            border-right: 1px solid #dee2e6;
            transition: background-color 0.2s ease;

            &:first-child {
              background-color: #f8f9fa;
              font-weight: 600;
              color: #495057;
            }
          }
        }
      }
    }

    // 多级表头边框样式
    .el-table__header {
      .el-table__header-cell {
        border-right: 1px solid #dee2e6;

        &:last-child {
          border-right: none;
        }
      }
    }

    // 表头层级指示器
    .level1-group .cell::before {
      content: "📊";
      margin-right: 4px;
      font-size: 12px;
    }

    .level2-group .cell::before {
      content: "📈";
      margin-right: 4px;
      font-size: 11px;
    }

    .level3-group .cell::before {
      content: "📋";
      margin-right: 4px;
      font-size: 10px;
    }
  }

  // 表格整体样式优化
  .el-table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    // 表头固定时的样式
    &.el-table--fixed {
      .el-table__fixed-right-patch {
        background-color: #f5f7fa;
        border-top: 1px solid #ebeef5;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    :deep(.el-table) {
      .level1-group,
      .level2-group,
      .level3-group {
        font-size: 11px !important;

        .cell {
          padding: 4px 8px !important;
        }
      }
    }
  }
}
</style>
