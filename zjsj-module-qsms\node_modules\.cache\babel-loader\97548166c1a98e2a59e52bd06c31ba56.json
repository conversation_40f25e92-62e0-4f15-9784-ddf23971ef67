{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue", "mtime": 1757423388005}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["props", "value", "type", "String", "Number", "default", "placeholder", "peopleList", "Array", "disabled", "Boolean", "selectMode", "validator", "includes", "data", "searchText", "selectVisible", "searchTimer", "listenerBound", "inputHandler", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "treeProps", "label", "children", "computed", "treeData", "processTreeData", "watch", "immediate", "handler", "val", "_this", "$nextTick", "$refs", "peopleTree", "filter", "trim", "setTimeout", "expandMatchedNodes", "collapseAllNodes", "newVal", "_this2", "deep", "mounted", "_this3", "setupInputListener", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "selectEl", "peopleSelectRef", "$el", "input", "querySelector", "removeEventListener", "methods", "_this4", "actualInput", "inputBySelector", "tagName", "nestedInput", "e", "filterPeople", "target", "addEventListener", "handleVisibleChange", "isVisible", "_this5", "handleFocus", "_this6", "handleClear", "closeDropdown", "blur", "_this7", "parent", "arguments", "length", "undefined", "map", "item", "_parent$id", "_parent$label", "currentNode", "_objectSpread2", "parentId", "id", "parentName", "filterNode", "_this8", "searchValue", "toLowerCase", "name", "matchPinyinInitials", "keywords", "split", "k", "every", "keyword", "handleNodeClick", "node", "_this9", "console", "log", "expanded", "handleSelectChange", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "handleInput", "$emit", "selectedId", "selectedItem", "findNodeById", "cleanItem", "nodes", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "found", "err", "f", "text", "pinyinMap", "a", "b", "c", "d", "g", "h", "i", "j", "l", "m", "o", "p", "q", "r", "t", "u", "v", "w", "x", "y", "z", "char", "pinyinChars", "_this0", "_this1", "expandedKeys", "collectExpandedNodes", "uniqueKeys", "_toConsumableArray2", "Set", "for<PERSON>ach", "key", "store", "nodesMap", "expand", "allNodes", "Object", "values", "childNodes", "collapse", "parent<PERSON><PERSON>", "hasMatchedChild", "_iterator2", "_step2", "push", "childMatched", "highlightSearchText", "escapedText", "replace", "match", "escapeMap", "regex", "RegExp", "concat"], "sources": ["src/views/components/selectPeopleTree.vue"], "sourcesContent": ["<template>\r\n  <el-select\r\n    ref=\"peopleSelectRef\"\r\n    :value=\"value\"\r\n    :placeholder=\"placeholder\"\r\n    :disabled=\"disabled\"\r\n    :visible=\"selectVisible\"\r\n    :filter-method=\"filterPeople\"\r\n    clearable\r\n    filterable\r\n    style=\"width: 100%\"\r\n    class=\"people-tree-select\"\r\n    popper-class=\"people-tree-dropdown\"\r\n    @input=\"handleInput\"\r\n    @change=\"handleSelectChange\"\r\n    @visible-change=\"handleVisibleChange\"\r\n    @focus=\"handleFocus\"\r\n    @clear=\"handleClear\"\r\n  >\r\n    <el-option :value=\"value\" style=\"height: auto; padding: 0\">\r\n      <el-tree\r\n        ref=\"peopleTree\"\r\n        :data=\"treeData\"\r\n        :props=\"treeProps\"\r\n        :filter-node-method=\"filterNode\"\r\n        :expand-on-click-node=\"false\"\r\n        :class=\"['people-tree', { searching: searchText && searchText.trim() }]\"\r\n        node-key=\"id\"\r\n        highlight-current\r\n        style=\"padding: 5px 0\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <i v-if=\"data.children && data.children.length\" />\r\n          <span\r\n            :class=\"['tree-label', { department: data.type === '1' }]\"\r\n            v-html=\"highlightSearchText(node.label, searchText)\"\r\n          />\r\n        </span>\r\n      </el-tree>\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: [String, Number],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请选择\",\r\n    },\r\n    peopleList: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 新增：选择模式，支持 'people'（人员选择）和 'category'（类别选择）\r\n    selectMode: {\r\n      type: String,\r\n      default: \"people\",\r\n      validator: (value) => [\"people\", \"category\"].includes(value),\r\n    },\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      searchText: \"\",\r\n      selectVisible: false,\r\n      searchTimer: null,\r\n      listenerBound: false, // 防止重复绑定监听器\r\n      inputHandler: null, // 存储输入处理函数引用\r\n      keyupHandler: null, // 存储按键处理函数引用\r\n      compositionHandler: null, // 存储组合输入处理函数引用\r\n\r\n      treeProps: {\r\n        label: \"label\",\r\n        children: \"children\",\r\n      },\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 转换为树形结构数据\r\n    treeData() {\r\n      return this.processTreeData(this.peopleList);\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    searchText: {\r\n      immediate: true,\r\n      handler(val) {\r\n        // 使用 $nextTick 确保组件已完全渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.peopleTree) {\r\n            this.$refs.peopleTree.filter(val);\r\n\r\n            // 搜索时自动展开匹配的父节点（无论是否首次）\r\n            if (val && val.trim()) {\r\n              // 使用短延迟确保过滤完成\r\n              setTimeout(() => {\r\n                this.expandMatchedNodes(val.trim());\r\n              }, 150);\r\n            } else {\r\n              // 清空搜索时收起所有节点\r\n              this.collapseAllNodes();\r\n            }\r\n          } else {\r\n            // 如果树组件还没准备好，短暂延迟后重试\r\n            setTimeout(() => {\r\n              if (this.$refs.peopleTree && val === this.searchText) {\r\n                this.$refs.peopleTree.filter(val);\r\n                if (val && val.trim()) {\r\n                  setTimeout(() => {\r\n                    this.expandMatchedNodes(val.trim());\r\n                  }, 100);\r\n                }\r\n              }\r\n            }, 200);\r\n          }\r\n        });\r\n      },\r\n    },\r\n\r\n    peopleList: {\r\n      handler(newVal) {\r\n        // 树数据更新后重新过滤\r\n        this.$nextTick(() => {\r\n          if (this.searchText) {\r\n            this.$refs.peopleTree.filter(this.searchText);\r\n          }\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n\r\n  mounted() {\r\n    // 组件挂载后的初始化\r\n    this.$nextTick(() => {\r\n      this.setupInputListener();\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.searchTimer) {\r\n      clearTimeout(this.searchTimer);\r\n    }\r\n\r\n    // 清理事件监听器\r\n    if (this.inputHandler || this.keyupHandler || this.compositionHandler) {\r\n      const selectEl = this.$refs.peopleSelectRef;\r\n      if (selectEl && selectEl.$el) {\r\n        const input = selectEl.$el.querySelector(\"input\");\r\n        if (input) {\r\n          input.removeEventListener(\"input\", this.inputHandler);\r\n          input.removeEventListener(\"keyup\", this.keyupHandler);\r\n          input.removeEventListener(\"compositionend\", this.compositionHandler);\r\n        }\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 设置输入监听器\r\n    setupInputListener() {\r\n      // 如果已经绑定过，跳过\r\n      if (this.listenerBound) {\r\n        return;\r\n      }\r\n\r\n      // 减少延迟，提高响应速度\r\n      setTimeout(() => {\r\n        const selectEl = this.$refs.peopleSelectRef;\r\n\r\n        if (selectEl) {\r\n          // 更详细的输入元素检测\r\n          let actualInput = null;\r\n\r\n          // 方式1: 通过选择器查找\r\n          const inputBySelector = selectEl.$el\r\n            ? selectEl.$el.querySelector(\"input\")\r\n            : null;\r\n          if (inputBySelector) {\r\n            actualInput = inputBySelector;\r\n          }\r\n\r\n          // 方式2: 通过 $refs.input\r\n          else if (selectEl.$refs && selectEl.$refs.input) {\r\n            if (selectEl.$refs.input.$el) {\r\n              if (selectEl.$refs.input.$el.tagName === \"INPUT\") {\r\n                actualInput = selectEl.$refs.input.$el;\r\n              } else {\r\n                const nestedInput =\r\n                  selectEl.$refs.input.$el.querySelector(\"input\");\r\n                if (nestedInput) {\r\n                  actualInput = nestedInput;\r\n                }\r\n              }\r\n            } else if (selectEl.$refs.input.tagName === \"INPUT\") {\r\n              actualInput = selectEl.$refs.input;\r\n            }\r\n          }\r\n\r\n          if (actualInput && actualInput.tagName === \"INPUT\") {\r\n            // 移除可能存在的旧监听器\r\n            actualInput.removeEventListener(\"input\", this.inputHandler);\r\n            actualInput.removeEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.removeEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            // 创建绑定的处理函数\r\n            this.inputHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.keyupHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.compositionHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            // 添加事件监听器\r\n            actualInput.addEventListener(\"input\", this.inputHandler);\r\n            actualInput.addEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.addEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            this.listenerBound = true;\r\n          } else {\r\n            // 如果找不到输入元素，稍后重试\r\n            setTimeout(() => {\r\n              this.listenerBound = false;\r\n              this.setupInputListener();\r\n            }, 300);\r\n          }\r\n        } else {\r\n          setTimeout(() => {\r\n            this.setupInputListener();\r\n          }, 300);\r\n        }\r\n      }, 200); // 增加延迟确保DOM完全渲染\r\n    },\r\n\r\n    handleVisibleChange(isVisible) {\r\n      if (isVisible) {\r\n        // 下拉框打开时，强制重新设置监听器\r\n        this.listenerBound = false;\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n\r\n        // 如果有现有搜索文本，立即触发过滤和展开\r\n        if (this.searchText) {\r\n          this.$nextTick(() => {\r\n            this.filterPeople(this.searchText);\r\n          });\r\n        }\r\n      } else {\r\n        // 关闭下拉框时清空搜索\r\n        this.searchText = \"\";\r\n        if (this.$refs.peopleTree) {\r\n          this.$refs.peopleTree.filter(\"\");\r\n          this.collapseAllNodes();\r\n        }\r\n      }\r\n    },\r\n\r\n    handleFocus() {\r\n      // 聚焦时确保监听器已设置\r\n      if (!this.listenerBound) {\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n      }\r\n    },\r\n\r\n    handleClear() {\r\n      this.searchText = \"\";\r\n      this.filterPeople(\"\");\r\n    },\r\n    closeDropdown() {\r\n      this.$refs.peopleSelectRef.blur();\r\n    },\r\n\r\n    // 处理树形数据\r\n    // processTreeData(data) {\r\n    //   return data.map((item) => ({\r\n    //     ...item,\r\n    //     label: item.label ? item.label.trim() : \"\",\r\n    //     children: item.children ? this.processTreeData(item.children) : [],\r\n    //   }));\r\n    // },\r\n    processTreeData(data, parent = null) {\r\n      return data.map((item) => {\r\n        // 构建当前节点，继承原有属性\r\n        const currentNode = {\r\n          ...item,\r\n          label: item.label ? item.label.trim() : \"\",\r\n          parentId: parent?.id ?? null,\r\n          parentName: parent?.label ?? null,\r\n          // 递归处理子节点，并将当前节点作为父节点传递\r\n          children: item.children\r\n            ? this.processTreeData(item.children, item)\r\n            : [],\r\n        };\r\n        // console.log(\"currentNode\", currentNode);\r\n        return currentNode;\r\n      });\r\n    },\r\n\r\n    // 节点过滤方法 - 增强的模糊搜索\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n\r\n      const searchValue = value.toLowerCase().trim();\r\n      const label = data.label ? data.label.toLowerCase() : \"\";\r\n      const name = data.name ? data.name.toLowerCase() : \"\";\r\n\r\n      // 1. 精确匹配\r\n      if (label.includes(searchValue) || name.includes(searchValue)) {\r\n        return true;\r\n      }\r\n\r\n      // 2. 拼音首字母匹配\r\n      if (\r\n        this.matchPinyinInitials(label, searchValue) ||\r\n        this.matchPinyinInitials(name, searchValue)\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 3. 分词匹配 - 支持空格分隔的多个关键词\r\n      const keywords = searchValue.split(/\\s+/).filter((k) => k.length > 0);\r\n      if (keywords.length > 1) {\r\n        return keywords.every(\r\n          (keyword) =>\r\n            label.includes(keyword) ||\r\n            name.includes(keyword) ||\r\n            this.matchPinyinInitials(label, keyword) ||\r\n            this.matchPinyinInitials(name, keyword)\r\n        );\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 节点点击事件\r\n    handleNodeClick(data, node) {\r\n      console.log(\"点击当前节点\", data, node);\r\n\r\n      // 类别选择模式：所有叶子节点都可以选择\r\n      if (this.selectMode === \"category\") {\r\n        // 如果有子节点，切换展开状态\r\n        if (data.children && data.children.length > 0) {\r\n          node.expanded = !node.expanded;\r\n          return;\r\n        }\r\n\r\n        // 叶子节点，触发选择\r\n        this.handleSelectChange(data.id);\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      // 如果是 general-project 类型，可以选择\r\n      if (data.type === \"general-project\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 如果是其他类型且有子节点，切换展开状态\r\n      if (data.children && data.children.length) {\r\n        node.expanded = !node.expanded;\r\n        if (data.type != null) {\r\n          return;\r\n        }\r\n        // return;\r\n      }\r\n\r\n      // 如果是人员节点（type !== '1' 且不是 general-project），触发选择\r\n      if (data.type !== \"1\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n      }\r\n\r\n      // 其他情况（没有子节点的节点）不做任何操作\r\n    },\r\n\r\n    handleInput(value) {\r\n      this.$emit(\"input\", value);\r\n      // 主动调用 filterPeople 确保搜索生效\r\n      this.filterPeople(value);\r\n    },\r\n\r\n    handleSelectChange(selectedId) {\r\n      if (!selectedId) {\r\n        this.$emit(\"change\", null);\r\n        return;\r\n      }\r\n\r\n      const selectedItem = this.findNodeById(this.treeData, selectedId);\r\n      console.log(\"选择的信息\", selectedItem);\r\n\r\n      // 类别选择模式：直接返回选中的项目\r\n      if (this.selectMode === \"category\") {\r\n        if (selectedItem) {\r\n          const cleanItem = {\r\n            id: selectedItem.id,\r\n            label: selectedItem.label || \"\",\r\n            name: selectedItem.name || \"\",\r\n            type: selectedItem.type,\r\n          };\r\n          this.$emit(\"change\", cleanItem);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      if (selectedItem && selectedItem.type !== \"1\") {\r\n        const cleanItem = {\r\n          id: selectedItem.id,\r\n          label: selectedItem.label || \"\",\r\n          name: selectedItem.name || \"\",\r\n          type: selectedItem.type,\r\n        };\r\n\r\n        this.$emit(\"change\", cleanItem);\r\n      }\r\n    },\r\n\r\n    // 根据ID查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) return node;\r\n        if (node.children && node.children.length) {\r\n          const found = this.findNodeById(node.children, id);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 拼音首字母匹配方法\r\n    matchPinyinInitials(text, searchValue) {\r\n      if (!text || !searchValue) return false;\r\n\r\n      // 简单的中文拼音首字母映射\r\n      const pinyinMap = {\r\n        a: \"阿啊\",\r\n        b: \"不把白本部被北备比表必标\",\r\n        c: \"从程成产出常场长城创传\",\r\n        d: \"的大都到道地点电当得对多段\",\r\n        e: \"二\",\r\n        f: \"发方法分风服费发放防发\",\r\n        g: \"工个过公管国广高改工构\",\r\n        h: \"和好后会化回还海合黄行\",\r\n        i: \"一\",\r\n        j: \"就进建交机计经检基级见技结\",\r\n        k: \"可看科开口控课快客\",\r\n        l: \"了来理里立流料量联力领路类\",\r\n        m: \"没么面民明名目模美门\",\r\n        n: \"年能内南那女你农\",\r\n        o: \"哦\",\r\n        p: \"平配品排培破片跑\",\r\n        q: \"去其全清情期前起请求区\",\r\n        r: \"人如让日然认入任\",\r\n        s: \"是时实施所说三设生手市上四十\",\r\n        t: \"他通同体统头条特提图天\",\r\n        u: \"有用于\",\r\n        v: \"\",\r\n        w: \"我为文物位问外王万五网维\",\r\n        x: \"现系性新学小心选许信下项行西\",\r\n        y: \"一要用有以业已应意音元月研运\",\r\n        z: \"中在这者主专注资制知至重组\",\r\n      };\r\n\r\n      // 尝试拼音首字母匹配\r\n      for (let i = 0; i < searchValue.length; i++) {\r\n        const char = searchValue[i];\r\n        const pinyinChars = pinyinMap[char];\r\n        if (pinyinChars && i < text.length) {\r\n          if (!pinyinChars.includes(text[i])) {\r\n            return false;\r\n          }\r\n        } else if (char !== text[i]) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return searchValue.length > 0 && searchValue.length <= text.length;\r\n    },\r\n\r\n    // 即时搜索方法 - 移除防抖，确保立即响应\r\n    filterPeople(value) {\r\n      // 立即更新搜索文本并触发过滤\r\n      this.searchText = value;\r\n\r\n      // 立即触发树过滤\r\n      if (this.$refs.peopleTree) {\r\n        this.$refs.peopleTree.filter(value);\r\n\r\n        // 如果有搜索值，立即展开匹配的节点\r\n        if (value && value.trim()) {\r\n          // 使用 setTimeout 确保过滤完成后再展开\r\n          setTimeout(() => {\r\n            this.expandMatchedNodes(value.trim());\r\n          }, 50);\r\n        }\r\n      }\r\n\r\n      // 对于 filter-method，我们总是返回 true，让所有选项都显示\r\n      // 因为我们的过滤逻辑是在树组件内部处理的\r\n      return true;\r\n    },\r\n\r\n    // 增强expandMatchedNodes方法，确保节点正确展开\r\n    expandMatchedNodes(searchValue) {\r\n      if (!this.$refs.peopleTree || !searchValue) return;\r\n\r\n      const expandedKeys = [];\r\n      this.collectExpandedNodes(this.treeData, searchValue, expandedKeys);\r\n\r\n      // 去重并确保展开逻辑生效\r\n      const uniqueKeys = [...new Set(expandedKeys)];\r\n\r\n      // 立即展开节点，不使用 nextTick 延迟\r\n      uniqueKeys.forEach((key) => {\r\n        const node = this.$refs.peopleTree.store.nodesMap[key];\r\n        if (node && !node.expanded) {\r\n          node.expand();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 收起所有节点的方法\r\n    collapseAllNodes() {\r\n      if (!this.$refs.peopleTree) return;\r\n\r\n      const allNodes = this.$refs.peopleTree.store.nodesMap;\r\n      Object.values(allNodes).forEach((node) => {\r\n        if (node.expanded && node.childNodes && node.childNodes.length > 0) {\r\n          node.collapse();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 递归收集需要展开的节点\r\n    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {\r\n      let hasMatchedChild = false;\r\n\r\n      for (const node of nodes) {\r\n        // 检查当前节点是否匹配\r\n        if (this.filterNode(searchValue, node)) {\r\n          hasMatchedChild = true;\r\n\r\n          // 如果有父节点，添加到展开列表\r\n          if (parentKey) {\r\n            expandedKeys.push(parentKey);\r\n          }\r\n        }\r\n\r\n        // 递归检查子节点\r\n        if (node.children && node.children.length > 0) {\r\n          const childMatched = this.collectExpandedNodes(\r\n            node.children,\r\n            searchValue,\r\n            expandedKeys,\r\n            node.id\r\n          );\r\n\r\n          if (childMatched) {\r\n            hasMatchedChild = true;\r\n            // 如果子节点有匹配，当前节点也需要展开\r\n            if (node.id) {\r\n              expandedKeys.push(node.id);\r\n            }\r\n            // 如果有父节点，父节点也需要展开\r\n            if (parentKey) {\r\n              expandedKeys.push(parentKey);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      return hasMatchedChild;\r\n    },\r\n\r\n    // 高亮搜索文本\r\n    highlightSearchText(text, searchValue) {\r\n      if (!text) return \"\";\r\n      if (!searchValue || !searchValue.trim()) return text;\r\n\r\n      const searchText = searchValue.trim();\r\n      // 防止XSS攻击，转义HTML特殊字符\r\n      const escapedText = text.replace(/[&<>\"']/g, function (match) {\r\n        const escapeMap = {\r\n          \"&\": \"&amp;\",\r\n          \"<\": \"&lt;\",\r\n          \">\": \"&gt;\",\r\n          '\"': \"&quot;\",\r\n          \"'\": \"&#x27;\",\r\n        };\r\n        return escapeMap[match];\r\n      });\r\n\r\n      // 如果搜索文本包含在显示文本中，高亮显示\r\n      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {\r\n        const regex = new RegExp(\r\n          `(${searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`,\r\n          \"gi\"\r\n        );\r\n        return escapedText.replace(\r\n          regex,\r\n          '<span class=\"search-highlight\">$1</span>'\r\n        );\r\n      }\r\n\r\n      return escapedText;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.custom-tree-node {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.tree-icon {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.tree-label {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 调整树组件样式 */\r\n:deep(.people-tree) {\r\n  min-width: 180px;\r\n  width: auto;\r\n}\r\n\r\n/* 控制下拉框宽度 - 使用特定class确保只影响这个组件 */\r\n:deep(.people-tree-dropdown) {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n/* 全局样式 - 更高优先级 */\r\n::v-deep .people-tree-dropdown.el-select-dropdown {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n:deep(.el-tree-node__content) {\r\n  height: 32px;\r\n}\r\n\r\n:deep(\r\n    .el-tree--highlight-current\r\n      .el-tree-node.is-current\r\n      > .el-tree-node__content\r\n  ) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n:deep(.el-select-dropdown__item) {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n\r\n/* 搜索高亮样式 */\r\n:deep(.search-highlight) {\r\n  background-color: #fffacd;\r\n  color: #d32f2f;\r\n  font-weight: bold;\r\n  padding: 1px 2px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */\r\n.people-tree-dropdown.el-select-dropdown {\r\n  max-width: 650px !important;\r\n  min-width: 650px !important;\r\n  width: auto !important;\r\n}\r\n\r\n.people-tree-dropdown .el-select-dropdown__item {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCA6CA;EACAA,KAAA;IACAC,KAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,WAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAE,UAAA;MACAL,IAAA,EAAAM,KAAA;MACAH,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAI,QAAA;MACAP,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACA;IACAM,UAAA;MACAT,IAAA,EAAAC,MAAA;MACAE,OAAA;MACAO,SAAA,WAAAA,UAAAX,KAAA;QAAA,8BAAAY,QAAA,CAAAZ,KAAA;MAAA;IACA;EACA;EAEAa,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,YAAA;MAAA;MACAC,kBAAA;MAAA;;MAEAC,SAAA;QACAC,KAAA;QACAC,QAAA;MACA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,eAAA,MAAApB,UAAA;IACA;EACA;EAEAqB,KAAA;IACAb,UAAA;MACAc,SAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAC,SAAA;UACA,IAAAD,KAAA,CAAAE,KAAA,CAAAC,UAAA;YACAH,KAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAL,GAAA;;YAEA;YACA,IAAAA,GAAA,IAAAA,GAAA,CAAAM,IAAA;cACA;cACAC,UAAA;gBACAN,KAAA,CAAAO,kBAAA,CAAAR,GAAA,CAAAM,IAAA;cACA;YACA;cACA;cACAL,KAAA,CAAAQ,gBAAA;YACA;UACA;YACA;YACAF,UAAA;cACA,IAAAN,KAAA,CAAAE,KAAA,CAAAC,UAAA,IAAAJ,GAAA,KAAAC,KAAA,CAAAjB,UAAA;gBACAiB,KAAA,CAAAE,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAL,GAAA;gBACA,IAAAA,GAAA,IAAAA,GAAA,CAAAM,IAAA;kBACAC,UAAA;oBACAN,KAAA,CAAAO,kBAAA,CAAAR,GAAA,CAAAM,IAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA9B,UAAA;MACAuB,OAAA,WAAAA,QAAAW,MAAA;QAAA,IAAAC,MAAA;QACA;QACA,KAAAT,SAAA;UACA,IAAAS,MAAA,CAAA3B,UAAA;YACA2B,MAAA,CAAAR,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAM,MAAA,CAAA3B,UAAA;UACA;QACA;MACA;MACA4B,IAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAZ,SAAA;MACAY,MAAA,CAAAC,kBAAA;IACA;EACA;EAEAC,aAAA,WAAAA,cAAA;IACA;IACA,SAAA9B,WAAA;MACA+B,YAAA,MAAA/B,WAAA;IACA;;IAEA;IACA,SAAAE,YAAA,SAAAC,YAAA,SAAAC,kBAAA;MACA,IAAA4B,QAAA,QAAAf,KAAA,CAAAgB,eAAA;MACA,IAAAD,QAAA,IAAAA,QAAA,CAAAE,GAAA;QACA,IAAAC,KAAA,GAAAH,QAAA,CAAAE,GAAA,CAAAE,aAAA;QACA,IAAAD,KAAA;UACAA,KAAA,CAAAE,mBAAA,eAAAnC,YAAA;UACAiC,KAAA,CAAAE,mBAAA,eAAAlC,YAAA;UACAgC,KAAA,CAAAE,mBAAA,wBAAAjC,kBAAA;QACA;MACA;IACA;EACA;EAEAkC,OAAA;IACA;IACAT,kBAAA,WAAAA,mBAAA;MAAA,IAAAU,MAAA;MACA;MACA,SAAAtC,aAAA;QACA;MACA;;MAEA;MACAoB,UAAA;QACA,IAAAW,QAAA,GAAAO,MAAA,CAAAtB,KAAA,CAAAgB,eAAA;QAEA,IAAAD,QAAA;UACA;UACA,IAAAQ,WAAA;;UAEA;UACA,IAAAC,eAAA,GAAAT,QAAA,CAAAE,GAAA,GACAF,QAAA,CAAAE,GAAA,CAAAE,aAAA,YACA;UACA,IAAAK,eAAA;YACAD,WAAA,GAAAC,eAAA;UACA;;UAEA;UAAA,KACA,IAAAT,QAAA,CAAAf,KAAA,IAAAe,QAAA,CAAAf,KAAA,CAAAkB,KAAA;YACA,IAAAH,QAAA,CAAAf,KAAA,CAAAkB,KAAA,CAAAD,GAAA;cACA,IAAAF,QAAA,CAAAf,KAAA,CAAAkB,KAAA,CAAAD,GAAA,CAAAQ,OAAA;gBACAF,WAAA,GAAAR,QAAA,CAAAf,KAAA,CAAAkB,KAAA,CAAAD,GAAA;cACA;gBACA,IAAAS,WAAA,GACAX,QAAA,CAAAf,KAAA,CAAAkB,KAAA,CAAAD,GAAA,CAAAE,aAAA;gBACA,IAAAO,WAAA;kBACAH,WAAA,GAAAG,WAAA;gBACA;cACA;YACA,WAAAX,QAAA,CAAAf,KAAA,CAAAkB,KAAA,CAAAO,OAAA;cACAF,WAAA,GAAAR,QAAA,CAAAf,KAAA,CAAAkB,KAAA;YACA;UACA;UAEA,IAAAK,WAAA,IAAAA,WAAA,CAAAE,OAAA;YACA;YACAF,WAAA,CAAAH,mBAAA,UAAAE,MAAA,CAAArC,YAAA;YACAsC,WAAA,CAAAH,mBAAA,UAAAE,MAAA,CAAApC,YAAA;YACAqC,WAAA,CAAAH,mBAAA,CACA,kBACAE,MAAA,CAAAnC,kBACA;;YAEA;YACAmC,MAAA,CAAArC,YAAA,aAAA0C,CAAA;cACAL,MAAA,CAAAM,YAAA,CAAAD,CAAA,CAAAE,MAAA,CAAA9D,KAAA;YACA;YAEAuD,MAAA,CAAApC,YAAA,aAAAyC,CAAA;cACAL,MAAA,CAAAM,YAAA,CAAAD,CAAA,CAAAE,MAAA,CAAA9D,KAAA;YACA;YAEAuD,MAAA,CAAAnC,kBAAA,aAAAwC,CAAA;cACAL,MAAA,CAAAM,YAAA,CAAAD,CAAA,CAAAE,MAAA,CAAA9D,KAAA;YACA;;YAEA;YACAwD,WAAA,CAAAO,gBAAA,UAAAR,MAAA,CAAArC,YAAA;YACAsC,WAAA,CAAAO,gBAAA,UAAAR,MAAA,CAAApC,YAAA;YACAqC,WAAA,CAAAO,gBAAA,CACA,kBACAR,MAAA,CAAAnC,kBACA;YAEAmC,MAAA,CAAAtC,aAAA;UACA;YACA;YACAoB,UAAA;cACAkB,MAAA,CAAAtC,aAAA;cACAsC,MAAA,CAAAV,kBAAA;YACA;UACA;QACA;UACAR,UAAA;YACAkB,MAAA,CAAAV,kBAAA;UACA;QACA;MACA;IACA;IAEAmB,mBAAA,WAAAA,oBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,SAAA;QACA;QACA,KAAAhD,aAAA;QACA,KAAAe,SAAA;UACAkC,MAAA,CAAArB,kBAAA;QACA;;QAEA;QACA,SAAA/B,UAAA;UACA,KAAAkB,SAAA;YACAkC,MAAA,CAAAL,YAAA,CAAAK,MAAA,CAAApD,UAAA;UACA;QACA;MACA;QACA;QACA,KAAAA,UAAA;QACA,SAAAmB,KAAA,CAAAC,UAAA;UACA,KAAAD,KAAA,CAAAC,UAAA,CAAAC,MAAA;UACA,KAAAI,gBAAA;QACA;MACA;IACA;IAEA4B,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAnD,aAAA;QACA,KAAAe,SAAA;UACAoC,MAAA,CAAAvB,kBAAA;QACA;MACA;IACA;IAEAwB,WAAA,WAAAA,YAAA;MACA,KAAAvD,UAAA;MACA,KAAA+C,YAAA;IACA;IACAS,aAAA,WAAAA,cAAA;MACA,KAAArC,KAAA,CAAAgB,eAAA,CAAAsB,IAAA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA7C,eAAA,WAAAA,gBAAAb,IAAA;MAAA,IAAA2D,MAAA;MAAA,IAAAC,MAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,OAAA7D,IAAA,CAAAgE,GAAA,WAAAC,IAAA;QAAA,IAAAC,UAAA,EAAAC,aAAA;QACA;QACA,IAAAC,WAAA,OAAAC,cAAA,CAAA9E,OAAA,MAAA8E,cAAA,CAAA9E,OAAA,MACA0E,IAAA;UACAxD,KAAA,EAAAwD,IAAA,CAAAxD,KAAA,GAAAwD,IAAA,CAAAxD,KAAA,CAAAc,IAAA;UACA+C,QAAA,GAAAJ,UAAA,GAAAN,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAW,EAAA,cAAAL,UAAA,cAAAA,UAAA;UACAM,UAAA,GAAAL,aAAA,GAAAP,MAAA,aAAAA,MAAA,uBAAAA,MAAA,CAAAnD,KAAA,cAAA0D,aAAA,cAAAA,aAAA;UACA;UACAzD,QAAA,EAAAuD,IAAA,CAAAvD,QAAA,GACAiD,MAAA,CAAA9C,eAAA,CAAAoD,IAAA,CAAAvD,QAAA,EAAAuD,IAAA,IACA;QAAA,EACA;QACA;QACA,OAAAG,WAAA;MACA;IACA;IAEA;IACAK,UAAA,WAAAA,WAAAtF,KAAA,EAAAa,IAAA;MAAA,IAAA0E,MAAA;MACA,KAAAvF,KAAA;MAEA,IAAAwF,WAAA,GAAAxF,KAAA,CAAAyF,WAAA,GAAArD,IAAA;MACA,IAAAd,KAAA,GAAAT,IAAA,CAAAS,KAAA,GAAAT,IAAA,CAAAS,KAAA,CAAAmE,WAAA;MACA,IAAAC,IAAA,GAAA7E,IAAA,CAAA6E,IAAA,GAAA7E,IAAA,CAAA6E,IAAA,CAAAD,WAAA;;MAEA;MACA,IAAAnE,KAAA,CAAAV,QAAA,CAAA4E,WAAA,KAAAE,IAAA,CAAA9E,QAAA,CAAA4E,WAAA;QACA;MACA;;MAEA;MACA,IACA,KAAAG,mBAAA,CAAArE,KAAA,EAAAkE,WAAA,KACA,KAAAG,mBAAA,CAAAD,IAAA,EAAAF,WAAA,GACA;QACA;MACA;;MAEA;MACA,IAAAI,QAAA,GAAAJ,WAAA,CAAAK,KAAA,QAAA1D,MAAA,WAAA2D,CAAA;QAAA,OAAAA,CAAA,CAAAnB,MAAA;MAAA;MACA,IAAAiB,QAAA,CAAAjB,MAAA;QACA,OAAAiB,QAAA,CAAAG,KAAA,CACA,UAAAC,OAAA;UAAA,OACA1E,KAAA,CAAAV,QAAA,CAAAoF,OAAA,KACAN,IAAA,CAAA9E,QAAA,CAAAoF,OAAA,KACAT,MAAA,CAAAI,mBAAA,CAAArE,KAAA,EAAA0E,OAAA,KACAT,MAAA,CAAAI,mBAAA,CAAAD,IAAA,EAAAM,OAAA;QAAA,CACA;MACA;MAEA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAApF,IAAA,EAAAqF,IAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA,WAAAxF,IAAA,EAAAqF,IAAA;;MAEA;MACA,SAAAxF,UAAA;QACA;QACA,IAAAG,IAAA,CAAAU,QAAA,IAAAV,IAAA,CAAAU,QAAA,CAAAoD,MAAA;UACAuB,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAAI,QAAA;UACA;QACA;;QAEA;QACA,KAAAC,kBAAA,CAAA1F,IAAA,CAAAuE,EAAA;QACA,KAAAnD,KAAA,CAAAC,UAAA,CAAAsE,aAAA,CAAA3F,IAAA,CAAAuE,EAAA;QACA,KAAApD,SAAA;UACAmE,MAAA,CAAA7B,aAAA;QACA;QACA;MACA;;MAEA;MACA;MACA,IAAAzD,IAAA,CAAAZ,IAAA;QACA,KAAAsG,kBAAA,CAAA1F,IAAA,CAAAuE,EAAA;;QAEA;QACA,KAAAnD,KAAA,CAAAC,UAAA,CAAAsE,aAAA,CAAA3F,IAAA,CAAAuE,EAAA;QACA,KAAApD,SAAA;UACAmE,MAAA,CAAA7B,aAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAzD,IAAA,CAAAU,QAAA,IAAAV,IAAA,CAAAU,QAAA,CAAAoD,MAAA;QACAuB,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAAI,QAAA;QACA,IAAAzF,IAAA,CAAAZ,IAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAY,IAAA,CAAAZ,IAAA;QACA,KAAAsG,kBAAA,CAAA1F,IAAA,CAAAuE,EAAA;;QAEA;QACA,KAAAnD,KAAA,CAAAC,UAAA,CAAAsE,aAAA,CAAA3F,IAAA,CAAAuE,EAAA;QACA,KAAApD,SAAA;UACAmE,MAAA,CAAA7B,aAAA;QACA;MACA;;MAEA;IACA;IAEAmC,WAAA,WAAAA,YAAAzG,KAAA;MACA,KAAA0G,KAAA,UAAA1G,KAAA;MACA;MACA,KAAA6D,YAAA,CAAA7D,KAAA;IACA;IAEAuG,kBAAA,WAAAA,mBAAAI,UAAA;MACA,KAAAA,UAAA;QACA,KAAAD,KAAA;QACA;MACA;MAEA,IAAAE,YAAA,QAAAC,YAAA,MAAApF,QAAA,EAAAkF,UAAA;MACAP,OAAA,CAAAC,GAAA,UAAAO,YAAA;;MAEA;MACA,SAAAlG,UAAA;QACA,IAAAkG,YAAA;UACA,IAAAE,SAAA;YACA1B,EAAA,EAAAwB,YAAA,CAAAxB,EAAA;YACA9D,KAAA,EAAAsF,YAAA,CAAAtF,KAAA;YACAoE,IAAA,EAAAkB,YAAA,CAAAlB,IAAA;YACAzF,IAAA,EAAA2G,YAAA,CAAA3G;UACA;UACA,KAAAyG,KAAA,WAAAI,SAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAF,YAAA,IAAAA,YAAA,CAAA3G,IAAA;QACA,IAAA6G,UAAA;UACA1B,EAAA,EAAAwB,YAAA,CAAAxB,EAAA;UACA9D,KAAA,EAAAsF,YAAA,CAAAtF,KAAA;UACAoE,IAAA,EAAAkB,YAAA,CAAAlB,IAAA;UACAzF,IAAA,EAAA2G,YAAA,CAAA3G;QACA;QAEA,KAAAyG,KAAA,WAAAI,UAAA;MACA;IACA;IAEA;IACAD,YAAA,WAAAA,aAAAE,KAAA,EAAA3B,EAAA;MAAA,IAAA4B,SAAA,OAAAC,2BAAA,CAAA7G,OAAA,EACA2G,KAAA;QAAAG,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAnB,IAAA,GAAAgB,KAAA,CAAAlH,KAAA;UACA,IAAAkG,IAAA,CAAAd,EAAA,KAAAA,EAAA,SAAAc,IAAA;UACA,IAAAA,IAAA,CAAA3E,QAAA,IAAA2E,IAAA,CAAA3E,QAAA,CAAAoD,MAAA;YACA,IAAA2C,KAAA,QAAAT,YAAA,CAAAX,IAAA,CAAA3E,QAAA,EAAA6D,EAAA;YACA,IAAAkC,KAAA,SAAAA,KAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAP,SAAA,CAAApD,CAAA,CAAA2D,GAAA;MAAA;QAAAP,SAAA,CAAAQ,CAAA;MAAA;MACA;IACA;IAEA;IACA7B,mBAAA,WAAAA,oBAAA8B,IAAA,EAAAjC,WAAA;MACA,KAAAiC,IAAA,KAAAjC,WAAA;;MAEA;MACA,IAAAkC,SAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAlE,CAAA;QACA4D,CAAA;QACAO,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACApC,CAAA;QACAqC,CAAA;QACAC,CAAA;QACAhB,CAAA;QACAiB,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACArB,CAAA;QACAsB,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;QACAC,CAAA;MACA;;MAEA;MACA,SAAAd,CAAA,MAAAA,CAAA,GAAAzC,WAAA,CAAAb,MAAA,EAAAsD,CAAA;QACA,IAAAe,IAAA,GAAAxD,WAAA,CAAAyC,CAAA;QACA,IAAAgB,WAAA,GAAAvB,SAAA,CAAAsB,IAAA;QACA,IAAAC,WAAA,IAAAhB,CAAA,GAAAR,IAAA,CAAA9C,MAAA;UACA,KAAAsE,WAAA,CAAArI,QAAA,CAAA6G,IAAA,CAAAQ,CAAA;YACA;UACA;QACA,WAAAe,IAAA,KAAAvB,IAAA,CAAAQ,CAAA;UACA;QACA;MACA;MAEA,OAAAzC,WAAA,CAAAb,MAAA,QAAAa,WAAA,CAAAb,MAAA,IAAA8C,IAAA,CAAA9C,MAAA;IACA;IAEA;IACAd,YAAA,WAAAA,aAAA7D,KAAA;MAAA,IAAAkJ,MAAA;MACA;MACA,KAAApI,UAAA,GAAAd,KAAA;;MAEA;MACA,SAAAiC,KAAA,CAAAC,UAAA;QACA,KAAAD,KAAA,CAAAC,UAAA,CAAAC,MAAA,CAAAnC,KAAA;;QAEA;QACA,IAAAA,KAAA,IAAAA,KAAA,CAAAoC,IAAA;UACA;UACAC,UAAA;YACA6G,MAAA,CAAA5G,kBAAA,CAAAtC,KAAA,CAAAoC,IAAA;UACA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEA;IACAE,kBAAA,WAAAA,mBAAAkD,WAAA;MAAA,IAAA2D,MAAA;MACA,UAAAlH,KAAA,CAAAC,UAAA,KAAAsD,WAAA;MAEA,IAAA4D,YAAA;MACA,KAAAC,oBAAA,MAAA5H,QAAA,EAAA+D,WAAA,EAAA4D,YAAA;;MAEA;MACA,IAAAE,UAAA,OAAAC,mBAAA,CAAAnJ,OAAA,MAAAoJ,GAAA,CAAAJ,YAAA;;MAEA;MACAE,UAAA,CAAAG,OAAA,WAAAC,GAAA;QACA,IAAAxD,IAAA,GAAAiD,MAAA,CAAAlH,KAAA,CAAAC,UAAA,CAAAyH,KAAA,CAAAC,QAAA,CAAAF,GAAA;QACA,IAAAxD,IAAA,KAAAA,IAAA,CAAAI,QAAA;UACAJ,IAAA,CAAA2D,MAAA;QACA;MACA;IACA;IAEA;IACAtH,gBAAA,WAAAA,iBAAA;MACA,UAAAN,KAAA,CAAAC,UAAA;MAEA,IAAA4H,QAAA,QAAA7H,KAAA,CAAAC,UAAA,CAAAyH,KAAA,CAAAC,QAAA;MACAG,MAAA,CAAAC,MAAA,CAAAF,QAAA,EAAAL,OAAA,WAAAvD,IAAA;QACA,IAAAA,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAA+D,UAAA,IAAA/D,IAAA,CAAA+D,UAAA,CAAAtF,MAAA;UACAuB,IAAA,CAAAgE,QAAA;QACA;MACA;IACA;IAEA;IACAb,oBAAA,WAAAA,qBAAAtC,KAAA,EAAAvB,WAAA,EAAA4D,YAAA;MAAA,IAAAe,SAAA,GAAAzF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MACA,IAAA0F,eAAA;MAAA,IAAAC,UAAA,OAAApD,2BAAA,CAAA7G,OAAA,EAEA2G,KAAA;QAAAuD,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAlD,CAAA,MAAAmD,MAAA,GAAAD,UAAA,CAAAjD,CAAA,IAAAC,IAAA;UAAA,IAAAnB,IAAA,GAAAoE,MAAA,CAAAtK,KAAA;UACA;UACA,SAAAsF,UAAA,CAAAE,WAAA,EAAAU,IAAA;YACAkE,eAAA;;YAEA;YACA,IAAAD,SAAA;cACAf,YAAA,CAAAmB,IAAA,CAAAJ,SAAA;YACA;UACA;;UAEA;UACA,IAAAjE,IAAA,CAAA3E,QAAA,IAAA2E,IAAA,CAAA3E,QAAA,CAAAoD,MAAA;YACA,IAAA6F,YAAA,QAAAnB,oBAAA,CACAnD,IAAA,CAAA3E,QAAA,EACAiE,WAAA,EACA4D,YAAA,EACAlD,IAAA,CAAAd,EACA;YAEA,IAAAoF,YAAA;cACAJ,eAAA;cACA;cACA,IAAAlE,IAAA,CAAAd,EAAA;gBACAgE,YAAA,CAAAmB,IAAA,CAAArE,IAAA,CAAAd,EAAA;cACA;cACA;cACA,IAAA+E,SAAA;gBACAf,YAAA,CAAAmB,IAAA,CAAAJ,SAAA;cACA;YACA;UACA;QACA;MAAA,SAAA5C,GAAA;QAAA8C,UAAA,CAAAzG,CAAA,CAAA2D,GAAA;MAAA;QAAA8C,UAAA,CAAA7C,CAAA;MAAA;MAEA,OAAA4C,eAAA;IACA;IAEA;IACAK,mBAAA,WAAAA,oBAAAhD,IAAA,EAAAjC,WAAA;MACA,KAAAiC,IAAA;MACA,KAAAjC,WAAA,KAAAA,WAAA,CAAApD,IAAA,WAAAqF,IAAA;MAEA,IAAA3G,UAAA,GAAA0E,WAAA,CAAApD,IAAA;MACA;MACA,IAAAsI,WAAA,GAAAjD,IAAA,CAAAkD,OAAA,uBAAAC,KAAA;QACA,IAAAC,SAAA;UACA;UACA;UACA;UACA;UACA;QACA;QACA,OAAAA,SAAA,CAAAD,KAAA;MACA;;MAEA;MACA,IAAAF,WAAA,CAAAjF,WAAA,GAAA7E,QAAA,CAAAE,UAAA,CAAA2E,WAAA;QACA,IAAAqF,KAAA,OAAAC,MAAA,KAAAC,MAAA,CACAlK,UAAA,CAAA6J,OAAA,uCACA,IACA;QACA,OAAAD,WAAA,CAAAC,OAAA,CACAG,KAAA,EACA,0CACA;MACA;MAEA,OAAAJ,WAAA;IACA;EACA;AACA", "ignoreList": []}]}