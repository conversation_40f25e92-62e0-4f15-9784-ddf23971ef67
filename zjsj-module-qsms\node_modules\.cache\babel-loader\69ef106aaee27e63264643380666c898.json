{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue", "mtime": 1757425168080}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zjQualityInspectionInfo", "require", "_info", "name", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "zjQualityInspectionInfoList", "title", "open", "queryParams", "pageNum", "pageSize", "projectId", "creatorId", "unitName", "routineId", "projectName", "projectStatus", "recordId", "regionId", "regionFullId", "regionName", "regionFullName", "dangerTypeId", "relation", "dangerTypeName", "dangerTypeFullName", "autoRecg", "dangerItemContent", "dangerDesc", "changeTime", "changeLimitTime", "level", "levelName", "status", "changeId", "participationIds", "participationNames", "form", "rules", "id", "required", "message", "trigger", "detailDialog", "detailData", "companyTreeLoading", "companyTreeData", "companyTreeProps", "children", "label", "checkResultOptions", "value", "created", "getList", "getCompanyTreeData", "methods", "getStatusClass", "statusMap", "getCheckResultText", "_this", "listZjQualityInspectionInfo", "then", "response", "rows", "cancel", "reset", "createTime", "updateTime", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getZjQualityInspectionInfo", "submitForm", "_this3", "$refs", "validate", "valid", "updateZjQualityInspectionInfo", "$modal", "msgSuccess", "addZjQualityInspectionInfo", "handleDelete", "_this4", "confirm", "delZjQualityInspectionInfo", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleDetail", "_this5", "getRectificationStatusText", "getReviewStatusText", "parseImageUrls", "imageUrl", "dataSource", "urlStr", "startsWith", "substring", "split", "filter", "url", "trim", "process", "env", "VUE_APP_BASE_API", "getImageUrl", "_this$detailData", "urls", "getAllImageUrls", "_this$detailData2", "previewImage", "$alert", "dangerouslyUseHTMLString", "customClass", "showConfirmButton", "showCancelButton", "cancelButtonText", "handleQueryCompanyNodeClick", "queryCompanySelect", "blur", "_this6", "getEnterpriseInfo", "res", "deptList", "for<PERSON>ach", "push", "err", "console", "error"], "sources": ["src/views/inspection/problemLedger/components/zjQualityInspectionInfo/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      v-show=\"showSearch\"\r\n      ref=\"queryForm\"\r\n      :model=\"queryParams\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <!-- <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n        <el-input\r\n          v-model=\"queryParams.projectId\"\r\n          placeholder=\"请输入项目ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n        <el-input\r\n          v-model=\"queryParams.creatorId\"\r\n          placeholder=\"请输入检查人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"所属公司\" prop=\"unitName\">\r\n        <el-select\r\n          ref=\"queryCompanySelect\"\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请选择所属公司\"\r\n          clearable\r\n          style=\"width: 180px\"\r\n          popper-class=\"org-tree-select-dropdown\"\r\n        >\r\n          <el-option\r\n            :value=\"queryParams.unitName\"\r\n            :label=\"queryParams.unitName\"\r\n            style=\"height: auto; padding: 0; border: none\"\r\n          >\r\n            <div class=\"tree-select-wrapper\">\r\n              <el-tree\r\n                v-loading=\"companyTreeLoading\"\r\n                :data=\"companyTreeData\"\r\n                :props=\"companyTreeProps\"\r\n                highlight-current\r\n                @node-click=\"handleQueryCompanyNodeClick\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">\r\n                      {{ node.label }}\r\n                    </span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </div>\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"常规id\" prop=\"routineId\">\r\n        <el-input\r\n          v-model=\"queryParams.routineId\"\r\n          placeholder=\"请输入常规id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"记录id\" prop=\"recordId\">\r\n        <el-input\r\n          v-model=\"queryParams.recordId\"\r\n          placeholder=\"请输入记录id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionId\"\r\n          placeholder=\"请输入责任区域ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullId\"\r\n          placeholder=\"请输入责任区域全id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionName\"\r\n          placeholder=\"请输入区域名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullName\"\r\n          placeholder=\"请输入区域全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeId\"\r\n          placeholder=\"请输入隐患类别id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关系\" prop=\"relation\">\r\n        <el-input\r\n          v-model=\"queryParams.relation\"\r\n          placeholder=\"请输入关系\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别\" prop=\"dangerTypeName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeName\"\r\n          placeholder=\"请输入隐患类别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeFullName\"\r\n          placeholder=\"请输入隐患类别全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n        <el-input\r\n          v-model=\"queryParams.autoRecg\"\r\n          placeholder=\"请输入auto_recg\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n       <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerDesc\"\r\n          placeholder=\"请输入备注\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时间\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>-->\r\n      <!-- <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeLimitTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时限(天)\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"级别\" prop=\"level\">\r\n        <el-input\r\n          v-model=\"queryParams.level\"\r\n          placeholder=\"请输入级别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入级别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n        <el-input\r\n          v-model=\"queryParams.changeId\"\r\n          placeholder=\"请输入整改人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n        <el-input\r\n          v-model=\"queryParams.participationIds\"\r\n          placeholder=\"请输入参与人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人\" prop=\"participationNames\">\r\n        <el-input\r\n          v-model=\"queryParams.participationNames\"\r\n          placeholder=\"请输入参与人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- 检查结果 -->\r\n      <el-form-item label=\"检查结果\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择检查结果\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in checkResultOptions\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.value\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button v-hasPermi=\"['inspection:zjQualityInspectionInfo:add']\" type=\"primary\" plain icon=\"el-icon-plus\"\r\n          size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"zjQualityInspectionInfoList\"\r\n      height=\"calc(100vh - 250px)\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"left\" />\r\n      <el-table-column label=\"序号\" width=\"55\" align=\"left\">\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 检查结果/截止时限 -->\r\n      <el-table-column label=\"检查结果/截止时限\" align=\"left\" width=\"200\">\r\n        <template slot-scope=\"{ row }\">\r\n          <div class=\"font-12\">\r\n            <span class=\"circle\" :class=\"getStatusClass(row.status)\" />\r\n            {{ getCheckResultText(row.status) }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            复查时限:{{\r\n              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : \"\"\r\n            }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 所属公司 -->\r\n      <el-table-column\r\n        label=\"所属公司\"\r\n        align=\"left\"\r\n        prop=\"ownerOrgStr\"\r\n        width=\"160\"\r\n        show-overflow-tooltip\r\n      />\r\n\r\n      <el-table-column\r\n        label=\"项目名称\"\r\n        align=\"left\"\r\n        prop=\"projectName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"检查人/检查时间\"\r\n        align=\"left\"\r\n        prop=\"creatorName\"\r\n        width=\"140\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"font-12\">\r\n            {{ scope.row.creatorName }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            {{ scope.row.createTime }}\r\n          </div>\r\n          <!-- {{ scope.row.creatorName + \"/\" + scope.row.createTime }} -->\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 质量问题信息 -->\r\n      <el-table-column\r\n        label=\"质量问题信息\"\r\n        align=\"left\"\r\n        width=\"200\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- levelName dangerTypeFullName dangerDesc  -->\r\n          <el-tag type=\"primary\"> {{ scope.row.levelName }}</el-tag>\r\n\r\n          {{ scope.row.dangerTypeFullName + \",\" + scope.row.dangerDesc }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"状态\" align=\"left\" prop=\"projectStatus\" /> -->\r\n      <!-- <el-table-column\r\n        label=\"区域名称\"\r\n        align=\"left\"\r\n        prop=\"regionName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"隐患类别名称\"\r\n        align=\"left\"\r\n        width=\"120\"\r\n        prop=\"dangerTypeName\"\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"整改时间\"\r\n        align=\"left\"\r\n        prop=\"changeTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column label=\"整改人\" align=\"left\" prop=\"changeName\" />\r\n      <!-- <el-table-column label=\"参与人\" align=\"left\" prop=\"participationNames\" /> -->\r\n      <!-- 复查人 -->\r\n      <el-table-column label=\"复查人\" align=\"left\" prop=\"reviewName\" />\r\n      <!-- 核验人 -->\r\n      <el-table-column label=\"核验人\" align=\"left\" prop=\"verifyManName\" />\r\n      <!-- 例行检查 -->\r\n      <el-table-column label=\"例行检查\" align=\"left\" prop=\"routineCheckNames\" />\r\n      <!-- 说明 -->\r\n      <el-table-column\r\n        label=\"说明\"\r\n        align=\"left\"\r\n        prop=\"remark\"\r\n        show-overflow-tooltip\r\n      />\r\n      <!-- <el-table-column label=\"项目ID\" align=\"left\" prop=\"projectId\" />\r\n      <el-table-column label=\"检查人id\" align=\"left\" prop=\"creatorId\" />\r\n\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"remark\" />\r\n      <el-table-column label=\"常规id\" align=\"left\" prop=\"routineId\" />\r\n      <el-table-column label=\"记录id\" align=\"left\" prop=\"recordId\" />\r\n      <el-table-column label=\"责任区域ID\" align=\"left\" prop=\"regionId\" />\r\n      <el-table-column\r\n        label=\"责任区域全id\"\r\n        align=\"left\"\r\n        prop=\"regionFullId\"\r\n      />\r\n      <el-table-column label=\"区域全称\" align=\"left\" prop=\"regionFullName\" />\r\n      <el-table-column label=\"关系\" align=\"left\" prop=\"relation\" />\r\n      <el-table-column label=\"隐患类别id\" align=\"left\" prop=\"dangerTypeId\" />\r\n\r\n      <el-table-column\r\n        label=\"隐患类别全称\"\r\n        align=\"left\"\r\n        prop=\"dangerTypeFullName\"\r\n      />\r\n      <el-table-column label=\"auto_recg\" align=\"left\" prop=\"autoRecg\" />\r\n      <el-table-column\r\n        label=\"隐患类目内容\"\r\n        align=\"left\"\r\n        prop=\"dangerItemContent\"\r\n      />\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"dangerDesc\" />\r\n\r\n      <el-table-column\r\n        label=\"整改时限(天)\"\r\n        align=\"left\"\r\n        prop=\"changeLimitTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeLimitTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"级别\" align=\"left\" prop=\"level\" />\r\n      <el-table-column label=\"级别名称\" align=\"left\" prop=\"levelName\" />\r\n      <el-table-column label=\"状态\" align=\"left\" prop=\"status\" />\r\n      <el-table-column label=\"整改人id\" align=\"left\" prop=\"changeId\" />\r\n      <el-table-column\r\n        label=\"参与人id\"\r\n        align=\"left\"\r\n        prop=\"participationIds\"\r\n      /> -->\r\n\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"left\"\r\n        class-name=\"small-padding fixed-width\"\r\n        fixed=\"right\"\r\n        width=\"150\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 查看详情 -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button\r\n          >\r\n          <!-- <el-button size=\"mini\" type=\"text\">打印</el-button> -->\r\n\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n            >修改</el-button\r\n          > -->\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n            >删除</el-button\r\n          > -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 详情弹窗 -->\r\n    <el-dialog\r\n      title=\"查看详情\"\r\n      :visible.sync=\"detailDialog\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      custom-class=\"detail-dialog\"\r\n    >\r\n      <div v-if=\"detailData\" class=\"detail-content\">\r\n        <!-- 问题记录部分 -->\r\n        <div class=\"independent-section\">\r\n          <h3 class=\"independent-title\">问题记录</h3>\r\n          <!-- 左右两列布局 -->\r\n          <div class=\"record-columns\">\r\n            <!-- 左列 -->\r\n            <div class=\"left-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查部位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.regionName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题描述:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerItemContent || detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题分类:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerTypeFullName ||\r\n                  detailData.dangerTypeName ||\r\n                  \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">补充说明:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题等级:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.levelName || detailData.level || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改要求:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.rectificationRequirements || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">轴线位置:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.axisPosition || \"8号18-17/D\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">分包单位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.contractorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右列 -->\r\n            <div class=\"right-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime || \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">紧急程度:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.urgencyLevel || \"一般\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime\r\n                    ? detailData.createTime.slice(0, 10)\r\n                    : \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改时限:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.changeLimitTime\r\n                    ? detailData.changeLimitTime.slice(0, 10)\r\n                    : \"2025-08-27\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewTime\r\n                    ? detailData.reviewTime.slice(0, 10)\r\n                    : \"2025-08-30\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">通知人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.notifierName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 现状整改部分 -->\r\n          <div class=\"field-row\">\r\n            <span class=\"field-label\">现状整改:</span>\r\n            <div class=\"status-row\">\r\n              <label\r\n                ><input\r\n                  type=\"checkbox\"\r\n                  :checked=\"detailData.status !== '1'\"\r\n                  disabled\r\n                />\r\n                未完成</label\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.hiddenDangerPictures\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.hiddenDangerPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"问题照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 整改记录部分 - 待整改状态(status=1)时不显示 -->\r\n        <div v-if=\"detailData.status !== '1'\" class=\"independent-section\">\r\n          <h3 class=\"independent-title\">整改记录</h3>\r\n          <!-- 整改信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getRectificationStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeTime\r\n                  ? detailData.changeTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 整改说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">整改说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.rectificationDesc || \"已整改\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div\r\n              v-if=\"detailData.rectificationPictures\"\r\n              class=\"photo-container\"\r\n            >\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.rectificationPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"整改照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 复查记录部分 - 待整改(status=1)和待复查(status=2)状态时不显示 -->\r\n        <div\r\n          v-if=\"detailData.status !== '1' && detailData.status !== '2'\"\r\n          class=\"independent-section\"\r\n        >\r\n          <h3 class=\"independent-title\">复查记录</h3>\r\n          <!-- 复查信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getReviewStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewTime\r\n                  ? detailData.reviewTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 复查说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">复查说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.reviewComments || \"-\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 复查照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.reviewPicUrl\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.reviewPicUrl\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"复查照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改质量检查台账对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"主键ID\" prop=\"id\">\r\n              <el-input v-model=\"form.id\" placeholder=\"请输入主键ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n              <el-input v-model=\"form.projectId\" placeholder=\"请输入项目ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n              <el-input v-model=\"form.creatorId\" placeholder=\"请输入检查人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人姓名\" prop=\"creatorName\">\r\n              <el-input\r\n                v-model=\"form.creatorName\"\r\n                placeholder=\"请输入检查人姓名\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"常规id\" prop=\"routineId\">\r\n              <el-input v-model=\"form.routineId\" placeholder=\"请输入常规id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input\r\n                v-model=\"form.projectName\"\r\n                placeholder=\"请输入项目名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"记录id\" prop=\"recordId\">\r\n              <el-input v-model=\"form.recordId\" placeholder=\"请输入记录id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n              <el-input\r\n                v-model=\"form.regionId\"\r\n                placeholder=\"请输入责任区域ID\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n              <el-input\r\n                v-model=\"form.regionFullId\"\r\n                placeholder=\"请输入责任区域全id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n              <el-input\r\n                v-model=\"form.regionName\"\r\n                placeholder=\"请输入区域名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n              <el-input\r\n                v-model=\"form.regionFullName\"\r\n                placeholder=\"请输入区域全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeId\"\r\n                placeholder=\"请输入隐患类别id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"关系\" prop=\"relation\">\r\n              <el-input v-model=\"form.relation\" placeholder=\"请输入关系\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别名称\" prop=\"dangerTypeName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeName\"\r\n                placeholder=\"请输入隐患类别名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeFullName\"\r\n                placeholder=\"请输入隐患类别全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n              <el-input v-model=\"form.autoRecg\" placeholder=\"请输入auto_recg\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n              <el-input v-model=\"form.dangerDesc\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"隐患类目内容\">\r\n              <editor v-model=\"form.dangerItemContent\" :min-height=\"192\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时间\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeLimitTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时限(天)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别\" prop=\"level\">\r\n              <el-input v-model=\"form.level\" placeholder=\"请输入级别\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n              <el-input v-model=\"form.levelName\" placeholder=\"请输入级别名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n              <el-input v-model=\"form.changeId\" placeholder=\"请输入整改人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人名称\" prop=\"changeName\">\r\n              <el-input\r\n                v-model=\"form.changeName\"\r\n                placeholder=\"请输入整改人名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n          <el-input\r\n            v-model=\"form.participationIds\"\r\n            placeholder=\"请输入参与人id\"\r\n          />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"参与人姓名\" prop=\"participationNames\">\r\n          <el-input\r\n            v-model=\"form.participationNames\"\r\n            placeholder=\"请输入参与人姓名\"\r\n          />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjQualityInspectionInfo,\r\n  getZjQualityInspectionInfo,\r\n  delZjQualityInspectionInfo,\r\n  addZjQualityInspectionInfo,\r\n  updateZjQualityInspectionInfo,\r\n} from \"@/api/inspection/zjQualityInspectionInfo\";\r\nimport { getEnterpriseInfo } from \"@/api/system/info\";\r\n\r\nexport default {\r\n  name: \"ZjQualityInspectionInfo\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 质量检查台账表格数据\r\n      zjQualityInspectionInfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectId: null,\r\n        creatorId: null,\r\n        unitName: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: \"主键ID不能为空\", trigger: \"blur\" }],\r\n      },\r\n      // 详情弹窗\r\n      detailDialog: false,\r\n      // 详情数据\r\n      detailData: {},\r\n      // 公司树相关数据\r\n      companyTreeLoading: false,\r\n      companyTreeData: [],\r\n      companyTreeProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n      },\r\n      // 检查结果选择项\r\n      checkResultOptions: [\r\n        {\r\n          label: \"无需整改\",\r\n          value: 0,\r\n        },\r\n        {\r\n          label: \"待整改\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"已整改\",\r\n          value: 2,\r\n        },\r\n        {\r\n          label: \"已合格\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"不合格\",\r\n          value: 4,\r\n        },\r\n        {\r\n          label: \"待核验\",\r\n          value: 7,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCompanyTreeData();\r\n  },\r\n  methods: {\r\n    getStatusClass(status) {\r\n      const statusMap = {\r\n        0: \"bg-blue\", // 无需整改\r\n        1: \"bg-orange\", // 待整改\r\n        2: \"bg-green\", // 已整改\r\n        3: \"bg-green\", // 已合格\r\n        4: \"bg-orange\", // 不合格\r\n        7: \"bg-blue\", // 待核验\r\n      };\r\n      return statusMap[status] || \"bg-blue\";\r\n    },\r\n    // 获取检查结果文字\r\n    getCheckResultText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    /** 查询质量检查台账列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjQualityInspectionInfo(this.queryParams).then((response) => {\r\n        this.zjQualityInspectionInfoList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        projectId: null,\r\n        createTime: null,\r\n        creatorId: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加质量检查台账\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改质量检查台账\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除质量检查台账编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjQualityInspectionInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/zjQualityInspectionInfo/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjQualityInspectionInfo_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    // 查看详情\r\n    handleDetail(row) {\r\n      const id = row.id;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.detailData = response.data;\r\n        this.detailDialog = true;\r\n      });\r\n    },\r\n    // 获取整改状态文字\r\n    getRectificationStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    // 获取复查状态文字\r\n    getReviewStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需复查\",\r\n        1: \"待复查\",\r\n        2: \"待复查\",\r\n        3: \"复查合格\",\r\n        4: \"复查不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未复查\";\r\n    },\r\n    // 解析图片URL列表\r\n    parseImageUrls(imageUrl, dataSource) {\r\n      if (!imageUrl) return [];\r\n\r\n      // 当dataSource为2时，处理后端拼接好的URL\r\n      if (dataSource === 2 || dataSource === \"2\") {\r\n        // 移除开头的@符号，然后按逗号分割\r\n        const urlStr = imageUrl.startsWith(\"@\")\r\n          ? imageUrl.substring(1)\r\n          : imageUrl;\r\n        return urlStr\r\n          .split(\",\")\r\n          .filter((url) => url.trim())\r\n          .map((url) => url.trim());\r\n      }\r\n\r\n      // 默认情况：前端拼接\r\n      if (imageUrl.startsWith(\"/\")) {\r\n        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`];\r\n      }\r\n      return [imageUrl];\r\n    },\r\n    // 获取图片URL（兼容原有逻辑）\r\n    getImageUrl(imageUrl) {\r\n      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n      return urls.length > 0 ? urls[0] : \"\";\r\n    },\r\n    // 获取所有图片URL\r\n    getAllImageUrls(imageUrl) {\r\n      return this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n    },\r\n    // 预览图片\r\n    previewImage(imageUrl) {\r\n      if (!imageUrl) return;\r\n      // 使用element-ui的图片预览功能\r\n      this.$alert(\r\n        `<img src=\"${imageUrl}\" style=\"width: 100%; max-width: 500px;\" alt=\"预览图片\">`,\r\n        \"图片预览\",\r\n        {\r\n          dangerouslyUseHTMLString: true,\r\n          customClass: \"image-preview-dialog\",\r\n          showConfirmButton: false,\r\n          showCancelButton: true,\r\n          cancelButtonText: \"关闭\",\r\n        }\r\n      );\r\n    },\r\n    // 处理公司节点点击\r\n    handleQueryCompanyNodeClick(data) {\r\n      this.queryParams.unitName = data.label;\r\n      this.$refs.queryCompanySelect.blur();\r\n    },\r\n    // 获取公司树数据\r\n    getCompanyTreeData() {\r\n      this.companyTreeLoading = true;\r\n      getEnterpriseInfo()\r\n        .then((res) => {\r\n          const deptList = res.data;\r\n          this.companyTreeData = [];\r\n          deptList.forEach((item) => {\r\n            this.companyTreeData.push({\r\n              label: item.label,\r\n              id: item.id,\r\n              children: item.children,\r\n            });\r\n          });\r\n          this.companyTreeLoading = false;\r\n        })\r\n        .catch((err) => {\r\n          this.companyTreeLoading = false;\r\n          console.error(err);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.font-12 {\r\n  font-size: 12px;\r\n}\r\n\r\n.circle {\r\n  width: 8px;\r\n  height: 8px;\r\n  display: inline-block;\r\n  border-radius: 50%;\r\n  margin-right: 2px;\r\n}\r\n\r\n.bg-orange {\r\n  background-color: #ffa500;\r\n}\r\n\r\n.bg-blue {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bg-green {\r\n  background-color: #28a745;\r\n}\r\n\r\n/* 详情弹窗样式 */\r\n:deep(.detail-dialog) {\r\n  .el-dialog__header {\r\n    background-color: #f5f5f5;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n    padding: 0px 20px !important;\r\n  }\r\n\r\n  ::v-deep(.el-dialog__body) {\r\n    padding: 0px 20px !important;\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  padding: 0 20px;\r\n\r\n  .independent-section {\r\n    margin-bottom: 10px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .independent-title {\r\n    margin: 0 0 20px 0;\r\n    padding: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n\r\n    &:after {\r\n      content: \"\";\r\n      flex: 1;\r\n      height: 2px;\r\n      border-top: 2px dashed #409eff;\r\n    }\r\n  }\r\n\r\n  // 左右两列布局\r\n  .record-columns {\r\n    display: flex;\r\n    gap: 40px;\r\n  }\r\n\r\n  .left-column,\r\n  .right-column {\r\n    flex: 1;\r\n  }\r\n\r\n  .field-row {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 16px;\r\n\r\n    &.full-width {\r\n      width: 100%;\r\n    }\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    &.highlighted-field {\r\n      background-color: #ebecf0;\r\n      padding: 8px 0;\r\n      margin-bottom: 0;\r\n      margin-left: -20px;\r\n      margin-right: -20px;\r\n      padding-left: 20px;\r\n      padding-right: 20px;\r\n    }\r\n  }\r\n\r\n  // 非高亮字段与高亮字段之间的间距\r\n  .field-row:not(.highlighted-field) + .field-row.highlighted-field,\r\n  .field-row.highlighted-field + .field-row:not(.highlighted-field) {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  .field-label {\r\n    min-width: 70px;\r\n    font-weight: 400;\r\n    color: #666;\r\n    margin-right: 10px;\r\n    white-space: nowrap;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .field-value {\r\n    color: #333;\r\n    word-break: break-all;\r\n    line-height: 1.5;\r\n    font-size: 14px;\r\n    flex: 1;\r\n\r\n    &.status-tag {\r\n      padding: 2px 8px;\r\n      border-radius: 3px;\r\n      font-size: 12px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  // 整改记录部分样式\r\n  .rectification-info {\r\n    display: flex;\r\n    gap: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #ebecf0;\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n    padding: 8px 20px;\r\n\r\n    .field-row {\r\n      margin-bottom: 0;\r\n      white-space: nowrap;\r\n      flex: 1;\r\n      margin-left: 0;\r\n      margin-right: 0;\r\n      padding: 0 20px;\r\n      background-color: transparent;\r\n\r\n      &:first-child {\r\n        padding-left: 0;\r\n      }\r\n\r\n      &:last-child {\r\n        padding-right: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 状态标签颜色\r\n  .status-no-need {\r\n    background-color: #f0f9ff;\r\n    color: #0369a1;\r\n    border: 1px solid #7dd3fc;\r\n  }\r\n\r\n  .status-pending {\r\n    background-color: transparent;\r\n    color: #d97706;\r\n    border: none;\r\n  }\r\n\r\n  .status-rectified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-qualified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-unqualified {\r\n    background-color: #fee2e2;\r\n    color: #dc2626;\r\n    border: 1px solid #f87171;\r\n  }\r\n\r\n  .status-verify {\r\n    background-color: #ede9fe;\r\n    color: #7c3aed;\r\n    border: 1px solid #a78bfa;\r\n  }\r\n\r\n  .status-unknown {\r\n    background-color: #f3f4f6;\r\n    color: #6b7280;\r\n    border: 1px solid #d1d5db;\r\n  }\r\n\r\n  // 状态行样式\r\n  .status-row {\r\n    margin-top: 8px;\r\n\r\n    label {\r\n      margin-right: 15px;\r\n      color: #666;\r\n      font-size: 14px;\r\n\r\n      input[type=\"checkbox\"] {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 照片相关样式\r\n  .photo-section {\r\n    padding-top: 10px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .photo-container {\r\n    margin-top: 10px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n  }\r\n\r\n  .photo-item,\r\n  .photo-placeholder {\r\n    border: 1px solid #e6e6e6;\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n    cursor: pointer;\r\n    transition: all 0.2s;\r\n    background: #fafafa;\r\n\r\n    &:hover {\r\n      transform: scale(1.02);\r\n      border-color: #409eff;\r\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n    }\r\n\r\n    img {\r\n      width: 120px;\r\n      height: 90px;\r\n      object-fit: cover;\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .photo-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #f8f9fa;\r\n    border: 1px dashed #d1d5db;\r\n\r\n    &:hover {\r\n      transform: none;\r\n      border-color: #d1d5db;\r\n      box-shadow: none;\r\n    }\r\n\r\n    img {\r\n      opacity: 0.3;\r\n      background: transparent;\r\n    }\r\n  }\r\n\r\n  .no-photo {\r\n    color: #999;\r\n    font-style: italic;\r\n    font-size: 12px;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    .record-columns {\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    .rectification-info {\r\n      flex-direction: column;\r\n      gap: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n// 图片预览弹窗样式\r\n:deep(.image-preview-dialog) {\r\n  .el-message-box__content {\r\n    text-align: center;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// 公司筛选下拉框样式\r\n.tree-select-wrapper {\r\n  padding: 8px;\r\n  min-height: 200px;\r\n  max-height: 300px;\r\n  overflow: auto;\r\n}\r\n\r\n:deep(.org-tree-select-dropdown) {\r\n  .el-select-dropdown__item {\r\n    height: auto;\r\n    max-height: 300px;\r\n    padding: 0;\r\n    overflow: hidden;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .el-tree-node__content {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .el-tree-node__label {\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAohCA,IAAAA,wBAAA,GAAAC,OAAA;AAOA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,2BAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,SAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,UAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,QAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,UAAA;QACAC,eAAA;QACAC,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,kBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,EAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,kBAAA;MACAC,eAAA;MACAC,gBAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,kBAAA,GACA;QACAD,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAvB,MAAA;MACA,IAAAwB,SAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;QAAA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,MAAA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAzB,MAAA;MACA,IAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,MAAA;IACA;IACA,iBACAoB,OAAA,WAAAA,QAAA;MAAA,IAAAM,KAAA;MACA,KAAA5D,OAAA;MACA,IAAA6D,oDAAA,OAAApD,WAAA,EAAAqD,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAtD,2BAAA,GAAAyD,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAvD,KAAA,GAAA0D,QAAA,CAAA1D,KAAA;QACAuD,KAAA,CAAA5D,OAAA;MACA;IACA;IACA;IACAiE,MAAA,WAAAA,OAAA;MACA,KAAAzD,IAAA;MACA,KAAA0D,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA5B,IAAA;QACAE,EAAA;QACA5B,SAAA;QACAuD,UAAA;QACAtD,SAAA;QACAuD,UAAA;QACAC,MAAA;QACAtD,SAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,UAAA;QACAC,cAAA;QACAC,YAAA;QACAC,QAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,QAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,UAAA;QACAC,eAAA;QACAC,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,kBAAA;MACA;MACA,KAAAiC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA9D,WAAA,CAAAC,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzE,GAAA,GAAAyE,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApC,EAAA;MAAA;MACA,KAAAtC,MAAA,GAAAwE,SAAA,CAAAG,MAAA;MACA,KAAA1E,QAAA,IAAAuE,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAZ,KAAA;MACA,KAAA1D,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAwE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAf,KAAA;MACA,IAAA1B,EAAA,GAAAwC,GAAA,CAAAxC,EAAA,SAAAvC,GAAA;MACA,IAAAiF,mDAAA,EAAA1C,EAAA,EAAAsB,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA3C,IAAA,GAAAyB,QAAA,CAAAhE,IAAA;QACAkF,MAAA,CAAAzE,IAAA;QACAyE,MAAA,CAAA1E,KAAA;MACA;IACA;IACA,WACA4E,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9C,IAAA,CAAAE,EAAA;YACA,IAAAgD,sDAAA,EAAAJ,MAAA,CAAA9C,IAAA,EAAAwB,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA5E,IAAA;cACA4E,MAAA,CAAA9B,OAAA;YACA;UACA;YACA,IAAAqC,mDAAA,EAAAP,MAAA,CAAA9C,IAAA,EAAAwB,IAAA,WAAAC,QAAA;cACAqB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA5E,IAAA;cACA4E,MAAA,CAAA9B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAsC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAA5F,GAAA,GAAA+E,GAAA,CAAAxC,EAAA,SAAAvC,GAAA;MACA,KAAAwF,MAAA,CACAK,OAAA,sBAAA7F,GAAA,aACA6D,IAAA;QACA,WAAAiC,mDAAA,EAAA9F,GAAA;MACA,GACA6D,IAAA;QACA+B,MAAA,CAAAvC,OAAA;QACAuC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,iDAAAC,cAAA,CAAAC,OAAA,MAEA,KAAA3F,WAAA,8BAAA4F,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAxB,GAAA;MAAA,IAAAyB,MAAA;MACA,IAAAjE,EAAA,GAAAwC,GAAA,CAAAxC,EAAA;MACA,IAAA0C,mDAAA,EAAA1C,EAAA,EAAAsB,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAA5D,UAAA,GAAAkB,QAAA,CAAAhE,IAAA;QACA0G,MAAA,CAAA7D,YAAA;MACA;IACA;IACA;IACA8D,0BAAA,WAAAA,2BAAAxE,MAAA;MACA,IAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,MAAA;IACA;IACA;IACAyE,mBAAA,WAAAA,oBAAAzE,MAAA;MACA,IAAAwB,SAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAxB,MAAA;IACA;IACA;IACA0E,cAAA,WAAAA,eAAAC,QAAA,EAAAC,UAAA;MACA,KAAAD,QAAA;;MAEA;MACA,IAAAC,UAAA,UAAAA,UAAA;QACA;QACA,IAAAC,MAAA,GAAAF,QAAA,CAAAG,UAAA,QACAH,QAAA,CAAAI,SAAA,MACAJ,QAAA;QACA,OAAAE,MAAA,CACAG,KAAA,MACAC,MAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,CAAAC,IAAA;QAAA,GACA1C,GAAA,WAAAyC,GAAA;UAAA,OAAAA,GAAA,CAAAC,IAAA;QAAA;MACA;;MAEA;MACA,IAAAR,QAAA,CAAAG,UAAA;QACA,WAAAX,MAAA,CAAAiB,OAAA,CAAAC,GAAA,CAAAC,gBAAA,EAAAnB,MAAA,CAAAQ,QAAA;MACA;MACA,QAAAA,QAAA;IACA;IACA;IACAY,WAAA,WAAAA,YAAAZ,QAAA;MAAA,IAAAa,gBAAA;MACA,IAAAC,IAAA,QAAAf,cAAA,CAAAC,QAAA,GAAAa,gBAAA,QAAA7E,UAAA,cAAA6E,gBAAA,uBAAAA,gBAAA,CAAAZ,UAAA;MACA,OAAAa,IAAA,CAAA9C,MAAA,OAAA8C,IAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAf,QAAA;MAAA,IAAAgB,iBAAA;MACA,YAAAjB,cAAA,CAAAC,QAAA,GAAAgB,iBAAA,QAAAhF,UAAA,cAAAgF,iBAAA,uBAAAA,iBAAA,CAAAf,UAAA;IACA;IACA;IACAgB,YAAA,WAAAA,aAAAjB,QAAA;MACA,KAAAA,QAAA;MACA;MACA,KAAAkB,MAAA,eAAA1B,MAAA,CACAQ,QAAA,oFACA,QACA;QACAmB,wBAAA;QACAC,WAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,gBAAA;MACA,CACA;IACA;IACA;IACAC,2BAAA,WAAAA,4BAAAtI,IAAA;MACA,KAAAU,WAAA,CAAAK,QAAA,GAAAf,IAAA,CAAAmD,KAAA;MACA,KAAAmC,KAAA,CAAAiD,kBAAA,CAAAC,IAAA;IACA;IACA;IACAhF,kBAAA,WAAAA,mBAAA;MAAA,IAAAiF,MAAA;MACA,KAAA1F,kBAAA;MACA,IAAA2F,uBAAA,IACA3E,IAAA,WAAA4E,GAAA;QACA,IAAAC,QAAA,GAAAD,GAAA,CAAA3I,IAAA;QACAyI,MAAA,CAAAzF,eAAA;QACA4F,QAAA,CAAAC,OAAA,WAAAhE,IAAA;UACA4D,MAAA,CAAAzF,eAAA,CAAA8F,IAAA;YACA3F,KAAA,EAAA0B,IAAA,CAAA1B,KAAA;YACAV,EAAA,EAAAoC,IAAA,CAAApC,EAAA;YACAS,QAAA,EAAA2B,IAAA,CAAA3B;UACA;QACA;QACAuF,MAAA,CAAA1F,kBAAA;MACA,GACAkD,KAAA,WAAA8C,GAAA;QACAN,MAAA,CAAA1F,kBAAA;QACAiG,OAAA,CAAAC,KAAA,CAAAF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}