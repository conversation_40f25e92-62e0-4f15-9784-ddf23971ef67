{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue", "mtime": 1757425168080}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0WmpRdWFsaXR5SW5zcGVjdGlvbkluZm8sDQogIGdldFpqUXVhbGl0eUluc3BlY3Rpb25JbmZvLA0KICBkZWxaalF1YWxpdHlJbnNwZWN0aW9uSW5mbywNCiAgYWRkWmpRdWFsaXR5SW5zcGVjdGlvbkluZm8sDQogIHVwZGF0ZVpqUXVhbGl0eUluc3BlY3Rpb25JbmZvLA0KfSBmcm9tICJAL2FwaS9pbnNwZWN0aW9uL3pqUXVhbGl0eUluc3BlY3Rpb25JbmZvIjsNCmltcG9ydCB7IGdldEVudGVycHJpc2VJbmZvIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2luZm8iOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJaalF1YWxpdHlJbnNwZWN0aW9uSW5mbyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDotKjph4/mo4Dmn6Xlj7DotKbooajmoLzmlbDmja4NCiAgICAgIHpqUXVhbGl0eUluc3BlY3Rpb25JbmZvTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcHJvamVjdElkOiBudWxsLA0KICAgICAgICBjcmVhdG9ySWQ6IG51bGwsDQogICAgICAgIHVuaXROYW1lOiBudWxsLA0KICAgICAgICByb3V0aW5lSWQ6IG51bGwsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBwcm9qZWN0U3RhdHVzOiBudWxsLA0KICAgICAgICByZWNvcmRJZDogbnVsbCwNCiAgICAgICAgcmVnaW9uSWQ6IG51bGwsDQogICAgICAgIHJlZ2lvbkZ1bGxJZDogbnVsbCwNCiAgICAgICAgcmVnaW9uTmFtZTogbnVsbCwNCiAgICAgICAgcmVnaW9uRnVsbE5hbWU6IG51bGwsDQogICAgICAgIGRhbmdlclR5cGVJZDogbnVsbCwNCiAgICAgICAgcmVsYXRpb246IG51bGwsDQogICAgICAgIGRhbmdlclR5cGVOYW1lOiBudWxsLA0KICAgICAgICBkYW5nZXJUeXBlRnVsbE5hbWU6IG51bGwsDQogICAgICAgIGF1dG9SZWNnOiBudWxsLA0KICAgICAgICBkYW5nZXJJdGVtQ29udGVudDogbnVsbCwNCiAgICAgICAgZGFuZ2VyRGVzYzogbnVsbCwNCiAgICAgICAgY2hhbmdlVGltZTogbnVsbCwNCiAgICAgICAgY2hhbmdlTGltaXRUaW1lOiBudWxsLA0KICAgICAgICBsZXZlbDogbnVsbCwNCiAgICAgICAgbGV2ZWxOYW1lOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGNoYW5nZUlkOiBudWxsLA0KICAgICAgICBwYXJ0aWNpcGF0aW9uSWRzOiBudWxsLA0KICAgICAgICBwYXJ0aWNpcGF0aW9uTmFtZXM6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgaWQ6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5Li76ZSuSUTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfV0sDQogICAgICB9LA0KICAgICAgLy8g6K+m5oOF5by556qXDQogICAgICBkZXRhaWxEaWFsb2c6IGZhbHNlLA0KICAgICAgLy8g6K+m5oOF5pWw5o2uDQogICAgICBkZXRhaWxEYXRhOiB7fSwNCiAgICAgIC8vIOWFrOWPuOagkeebuOWFs+aVsOaNrg0KICAgICAgY29tcGFueVRyZWVMb2FkaW5nOiBmYWxzZSwNCiAgICAgIGNvbXBhbnlUcmVlRGF0YTogW10sDQogICAgICBjb21wYW55VHJlZVByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLA0KICAgICAgICBsYWJlbDogImxhYmVsIiwNCiAgICAgIH0sDQogICAgICAvLyDmo4Dmn6Xnu5PmnpzpgInmi6npobkNCiAgICAgIGNoZWNrUmVzdWx0T3B0aW9uczogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLml6DpnIDmlbTmlLkiLA0KICAgICAgICAgIHZhbHVlOiAwLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlvoXmlbTmlLkiLA0KICAgICAgICAgIHZhbHVlOiAxLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlt7LmlbTmlLkiLA0KICAgICAgICAgIHZhbHVlOiAyLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlt7LlkIjmoLwiLA0KICAgICAgICAgIHZhbHVlOiAzLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLkuI3lkIjmoLwiLA0KICAgICAgICAgIHZhbHVlOiA0LA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlvoXmoLjpqowiLA0KICAgICAgICAgIHZhbHVlOiA3LA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0Q29tcGFueVRyZWVEYXRhKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRTdGF0dXNDbGFzcyhzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgMDogImJnLWJsdWUiLCAvLyDml6DpnIDmlbTmlLkNCiAgICAgICAgMTogImJnLW9yYW5nZSIsIC8vIOW+heaVtOaUuQ0KICAgICAgICAyOiAiYmctZ3JlZW4iLCAvLyDlt7LmlbTmlLkNCiAgICAgICAgMzogImJnLWdyZWVuIiwgLy8g5bey5ZCI5qC8DQogICAgICAgIDQ6ICJiZy1vcmFuZ2UiLCAvLyDkuI3lkIjmoLwNCiAgICAgICAgNzogImJnLWJsdWUiLCAvLyDlvoXmoLjpqowNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgImJnLWJsdWUiOw0KICAgIH0sDQogICAgLy8g6I635Y+W5qOA5p+l57uT5p6c5paH5a2XDQogICAgZ2V0Q2hlY2tSZXN1bHRUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAwOiAi5peg6ZyA5pW05pS5IiwNCiAgICAgICAgMTogIuW+heaVtOaUuSIsDQogICAgICAgIDI6ICLlt7LmlbTmlLkiLA0KICAgICAgICAzOiAi5bey5ZCI5qC8IiwNCiAgICAgICAgNDogIuS4jeWQiOagvCIsDQogICAgICAgIDc6ICLlvoXmoLjpqowiLA0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAi5pyq55+l54q25oCBIjsNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LotKjph4/mo4Dmn6Xlj7DotKbliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RaalF1YWxpdHlJbnNwZWN0aW9uSW5mbyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnpqUXVhbGl0eUluc3BlY3Rpb25JbmZvTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwcm9qZWN0SWQ6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGNyZWF0b3JJZDogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICByb3V0aW5lSWQ6IG51bGwsDQogICAgICAgIHByb2plY3ROYW1lOiBudWxsLA0KICAgICAgICBwcm9qZWN0U3RhdHVzOiBudWxsLA0KICAgICAgICByZWNvcmRJZDogbnVsbCwNCiAgICAgICAgcmVnaW9uSWQ6IG51bGwsDQogICAgICAgIHJlZ2lvbkZ1bGxJZDogbnVsbCwNCiAgICAgICAgcmVnaW9uTmFtZTogbnVsbCwNCiAgICAgICAgcmVnaW9uRnVsbE5hbWU6IG51bGwsDQogICAgICAgIGRhbmdlclR5cGVJZDogbnVsbCwNCiAgICAgICAgcmVsYXRpb246IG51bGwsDQogICAgICAgIGRhbmdlclR5cGVOYW1lOiBudWxsLA0KICAgICAgICBkYW5nZXJUeXBlRnVsbE5hbWU6IG51bGwsDQogICAgICAgIGF1dG9SZWNnOiBudWxsLA0KICAgICAgICBkYW5nZXJJdGVtQ29udGVudDogbnVsbCwNCiAgICAgICAgZGFuZ2VyRGVzYzogbnVsbCwNCiAgICAgICAgY2hhbmdlVGltZTogbnVsbCwNCiAgICAgICAgY2hhbmdlTGltaXRUaW1lOiBudWxsLA0KICAgICAgICBsZXZlbDogbnVsbCwNCiAgICAgICAgbGV2ZWxOYW1lOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIGNoYW5nZUlkOiBudWxsLA0KICAgICAgICBwYXJ0aWNpcGF0aW9uSWRzOiBudWxsLA0KICAgICAgICBwYXJ0aWNpcGF0aW9uTmFtZXM6IG51bGwsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtKSA9PiBpdGVtLmlkKTsNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMTsNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDotKjph4/mo4Dmn6Xlj7DotKYiOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0WmpRdWFsaXR5SW5zcGVjdGlvbkluZm8oaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS56LSo6YeP5qOA5p+l5Y+w6LSmIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlWmpRdWFsaXR5SW5zcGVjdGlvbkluZm8odGhpcy5mb3JtKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFpqUXVhbGl0eUluc3BlY3Rpb25JbmZvKHRoaXMuZm9ybSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaWRzID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOi0qOmHj+ajgOafpeWPsOi0pue8luWPt+S4uiInICsgaWRzICsgJyLnmoTmlbDmja7pobnvvJ8nKQ0KICAgICAgICAudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgICAgcmV0dXJuIGRlbFpqUXVhbGl0eUluc3BlY3Rpb25JbmZvKGlkcyk7DQogICAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAiaW5zcGVjdGlvbi96alF1YWxpdHlJbnNwZWN0aW9uSW5mby9leHBvcnQiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYHpqUXVhbGl0eUluc3BlY3Rpb25JbmZvXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQogICAgICApOw0KICAgIH0sDQogICAgLy8g5p+l55yL6K+m5oOFDQogICAgaGFuZGxlRGV0YWlsKHJvdykgew0KICAgICAgY29uc3QgaWQgPSByb3cuaWQ7DQogICAgICBnZXRaalF1YWxpdHlJbnNwZWN0aW9uSW5mbyhpZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5kZXRhaWxEYXRhID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5kZXRhaWxEaWFsb2cgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDojrflj5bmlbTmlLnnirbmgIHmloflrZcNCiAgICBnZXRSZWN0aWZpY2F0aW9uU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgMDogIuaXoOmcgOaVtOaUuSIsDQogICAgICAgIDE6ICLlvoXmlbTmlLkiLA0KICAgICAgICAyOiAi5bey5pW05pS5IiwNCiAgICAgICAgMzogIuW3suWQiOagvCIsDQogICAgICAgIDQ6ICLkuI3lkIjmoLwiLA0KICAgICAgICA3OiAi5b6F5qC46aqMIiwNCiAgICAgIH07DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgIuacquefpeeKtuaAgSI7DQogICAgfSwNCiAgICAvLyDojrflj5blpI3mn6XnirbmgIHmloflrZcNCiAgICBnZXRSZXZpZXdTdGF0dXNUZXh0KHN0YXR1cykgew0KICAgICAgY29uc3Qgc3RhdHVzTWFwID0gew0KICAgICAgICAwOiAi5peg6ZyA5aSN5p+lIiwNCiAgICAgICAgMTogIuW+heWkjeafpSIsDQogICAgICAgIDI6ICLlvoXlpI3mn6UiLA0KICAgICAgICAzOiAi5aSN5p+l5ZCI5qC8IiwNCiAgICAgICAgNDogIuWkjeafpeS4jeWQiOagvCIsDQogICAgICAgIDc6ICLlvoXmoLjpqowiLA0KICAgICAgfTsNCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAi5pyq5aSN5p+lIjsNCiAgICB9LA0KICAgIC8vIOino+aekOWbvueJh1VSTOWIl+ihqA0KICAgIHBhcnNlSW1hZ2VVcmxzKGltYWdlVXJsLCBkYXRhU291cmNlKSB7DQogICAgICBpZiAoIWltYWdlVXJsKSByZXR1cm4gW107DQoNCiAgICAgIC8vIOW9k2RhdGFTb3VyY2XkuLoy5pe277yM5aSE55CG5ZCO56uv5ou85o6l5aW955qEVVJMDQogICAgICBpZiAoZGF0YVNvdXJjZSA9PT0gMiB8fCBkYXRhU291cmNlID09PSAiMiIpIHsNCiAgICAgICAgLy8g56e76Zmk5byA5aS055qEQOespuWPt++8jOeEtuWQjuaMiemAl+WPt+WIhuWJsg0KICAgICAgICBjb25zdCB1cmxTdHIgPSBpbWFnZVVybC5zdGFydHNXaXRoKCJAIikNCiAgICAgICAgICA/IGltYWdlVXJsLnN1YnN0cmluZygxKQ0KICAgICAgICAgIDogaW1hZ2VVcmw7DQogICAgICAgIHJldHVybiB1cmxTdHINCiAgICAgICAgICAuc3BsaXQoIiwiKQ0KICAgICAgICAgIC5maWx0ZXIoKHVybCkgPT4gdXJsLnRyaW0oKSkNCiAgICAgICAgICAubWFwKCh1cmwpID0+IHVybC50cmltKCkpOw0KICAgICAgfQ0KDQogICAgICAvLyDpu5jorqTmg4XlhrXvvJrliY3nq6/mi7zmjqUNCiAgICAgIGlmIChpbWFnZVVybC5zdGFydHNXaXRoKCIvIikpIHsNCiAgICAgICAgcmV0dXJuIFtgJHtwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJfSR7aW1hZ2VVcmx9YF07DQogICAgICB9DQogICAgICByZXR1cm4gW2ltYWdlVXJsXTsNCiAgICB9LA0KICAgIC8vIOiOt+WPluWbvueJh1VSTO+8iOWFvOWuueWOn+aciemAu+i+ke+8iQ0KICAgIGdldEltYWdlVXJsKGltYWdlVXJsKSB7DQogICAgICBjb25zdCB1cmxzID0gdGhpcy5wYXJzZUltYWdlVXJscyhpbWFnZVVybCwgdGhpcy5kZXRhaWxEYXRhPy5kYXRhU291cmNlKTsNCiAgICAgIHJldHVybiB1cmxzLmxlbmd0aCA+IDAgPyB1cmxzWzBdIDogIiI7DQogICAgfSwNCiAgICAvLyDojrflj5bmiYDmnInlm77niYdVUkwNCiAgICBnZXRBbGxJbWFnZVVybHMoaW1hZ2VVcmwpIHsNCiAgICAgIHJldHVybiB0aGlzLnBhcnNlSW1hZ2VVcmxzKGltYWdlVXJsLCB0aGlzLmRldGFpbERhdGE/LmRhdGFTb3VyY2UpOw0KICAgIH0sDQogICAgLy8g6aKE6KeI5Zu+54mHDQogICAgcHJldmlld0ltYWdlKGltYWdlVXJsKSB7DQogICAgICBpZiAoIWltYWdlVXJsKSByZXR1cm47DQogICAgICAvLyDkvb/nlKhlbGVtZW50LXVp55qE5Zu+54mH6aKE6KeI5Yqf6IO9DQogICAgICB0aGlzLiRhbGVydCgNCiAgICAgICAgYDxpbWcgc3JjPSIke2ltYWdlVXJsfSIgc3R5bGU9IndpZHRoOiAxMDAlOyBtYXgtd2lkdGg6IDUwMHB4OyIgYWx0PSLpooTop4jlm77niYciPmAsDQogICAgICAgICLlm77niYfpooTop4giLA0KICAgICAgICB7DQogICAgICAgICAgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlLA0KICAgICAgICAgIGN1c3RvbUNsYXNzOiAiaW1hZ2UtcHJldmlldy1kaWFsb2ciLA0KICAgICAgICAgIHNob3dDb25maXJtQnV0dG9uOiBmYWxzZSwNCiAgICAgICAgICBzaG93Q2FuY2VsQnV0dG9uOiB0cnVlLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlhbPpl60iLA0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgLy8g5aSE55CG5YWs5Y+46IqC54K554K55Ye7DQogICAgaGFuZGxlUXVlcnlDb21wYW55Tm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMudW5pdE5hbWUgPSBkYXRhLmxhYmVsOw0KICAgICAgdGhpcy4kcmVmcy5xdWVyeUNvbXBhbnlTZWxlY3QuYmx1cigpOw0KICAgIH0sDQogICAgLy8g6I635Y+W5YWs5Y+45qCR5pWw5o2uDQogICAgZ2V0Q29tcGFueVRyZWVEYXRhKCkgew0KICAgICAgdGhpcy5jb21wYW55VHJlZUxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2V0RW50ZXJwcmlzZUluZm8oKQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgY29uc3QgZGVwdExpc3QgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlUcmVlRGF0YSA9IFtdOw0KICAgICAgICAgIGRlcHRMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIHRoaXMuY29tcGFueVRyZWVEYXRhLnB1c2goew0KICAgICAgICAgICAgICBsYWJlbDogaXRlbS5sYWJlbCwNCiAgICAgICAgICAgICAgaWQ6IGl0ZW0uaWQsDQogICAgICAgICAgICAgIGNoaWxkcmVuOiBpdGVtLmNoaWxkcmVuLA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5jb21wYW55VHJlZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChlcnIpID0+IHsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlUcmVlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyKTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAohCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/problemLedger/components/zjQualityInspectionInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      v-show=\"showSearch\"\r\n      ref=\"queryForm\"\r\n      :model=\"queryParams\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <!-- <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n        <el-input\r\n          v-model=\"queryParams.projectId\"\r\n          placeholder=\"请输入项目ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n        <el-input\r\n          v-model=\"queryParams.creatorId\"\r\n          placeholder=\"请输入检查人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"所属公司\" prop=\"unitName\">\r\n        <el-select\r\n          ref=\"queryCompanySelect\"\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请选择所属公司\"\r\n          clearable\r\n          style=\"width: 180px\"\r\n          popper-class=\"org-tree-select-dropdown\"\r\n        >\r\n          <el-option\r\n            :value=\"queryParams.unitName\"\r\n            :label=\"queryParams.unitName\"\r\n            style=\"height: auto; padding: 0; border: none\"\r\n          >\r\n            <div class=\"tree-select-wrapper\">\r\n              <el-tree\r\n                v-loading=\"companyTreeLoading\"\r\n                :data=\"companyTreeData\"\r\n                :props=\"companyTreeProps\"\r\n                highlight-current\r\n                @node-click=\"handleQueryCompanyNodeClick\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">\r\n                      {{ node.label }}\r\n                    </span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </div>\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"常规id\" prop=\"routineId\">\r\n        <el-input\r\n          v-model=\"queryParams.routineId\"\r\n          placeholder=\"请输入常规id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"记录id\" prop=\"recordId\">\r\n        <el-input\r\n          v-model=\"queryParams.recordId\"\r\n          placeholder=\"请输入记录id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionId\"\r\n          placeholder=\"请输入责任区域ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullId\"\r\n          placeholder=\"请输入责任区域全id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionName\"\r\n          placeholder=\"请输入区域名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullName\"\r\n          placeholder=\"请输入区域全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeId\"\r\n          placeholder=\"请输入隐患类别id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关系\" prop=\"relation\">\r\n        <el-input\r\n          v-model=\"queryParams.relation\"\r\n          placeholder=\"请输入关系\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别\" prop=\"dangerTypeName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeName\"\r\n          placeholder=\"请输入隐患类别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeFullName\"\r\n          placeholder=\"请输入隐患类别全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n        <el-input\r\n          v-model=\"queryParams.autoRecg\"\r\n          placeholder=\"请输入auto_recg\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n       <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerDesc\"\r\n          placeholder=\"请输入备注\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时间\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>-->\r\n      <!-- <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeLimitTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时限(天)\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"级别\" prop=\"level\">\r\n        <el-input\r\n          v-model=\"queryParams.level\"\r\n          placeholder=\"请输入级别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入级别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n        <el-input\r\n          v-model=\"queryParams.changeId\"\r\n          placeholder=\"请输入整改人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n        <el-input\r\n          v-model=\"queryParams.participationIds\"\r\n          placeholder=\"请输入参与人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人\" prop=\"participationNames\">\r\n        <el-input\r\n          v-model=\"queryParams.participationNames\"\r\n          placeholder=\"请输入参与人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- 检查结果 -->\r\n      <el-form-item label=\"检查结果\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择检查结果\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in checkResultOptions\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.value\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button v-hasPermi=\"['inspection:zjQualityInspectionInfo:add']\" type=\"primary\" plain icon=\"el-icon-plus\"\r\n          size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"zjQualityInspectionInfoList\"\r\n      height=\"calc(100vh - 250px)\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"left\" />\r\n      <el-table-column label=\"序号\" width=\"55\" align=\"left\">\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 检查结果/截止时限 -->\r\n      <el-table-column label=\"检查结果/截止时限\" align=\"left\" width=\"200\">\r\n        <template slot-scope=\"{ row }\">\r\n          <div class=\"font-12\">\r\n            <span class=\"circle\" :class=\"getStatusClass(row.status)\" />\r\n            {{ getCheckResultText(row.status) }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            复查时限:{{\r\n              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : \"\"\r\n            }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 所属公司 -->\r\n      <el-table-column\r\n        label=\"所属公司\"\r\n        align=\"left\"\r\n        prop=\"ownerOrgStr\"\r\n        width=\"160\"\r\n        show-overflow-tooltip\r\n      />\r\n\r\n      <el-table-column\r\n        label=\"项目名称\"\r\n        align=\"left\"\r\n        prop=\"projectName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"检查人/检查时间\"\r\n        align=\"left\"\r\n        prop=\"creatorName\"\r\n        width=\"140\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"font-12\">\r\n            {{ scope.row.creatorName }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            {{ scope.row.createTime }}\r\n          </div>\r\n          <!-- {{ scope.row.creatorName + \"/\" + scope.row.createTime }} -->\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 质量问题信息 -->\r\n      <el-table-column\r\n        label=\"质量问题信息\"\r\n        align=\"left\"\r\n        width=\"200\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- levelName dangerTypeFullName dangerDesc  -->\r\n          <el-tag type=\"primary\"> {{ scope.row.levelName }}</el-tag>\r\n\r\n          {{ scope.row.dangerTypeFullName + \",\" + scope.row.dangerDesc }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"状态\" align=\"left\" prop=\"projectStatus\" /> -->\r\n      <!-- <el-table-column\r\n        label=\"区域名称\"\r\n        align=\"left\"\r\n        prop=\"regionName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"隐患类别名称\"\r\n        align=\"left\"\r\n        width=\"120\"\r\n        prop=\"dangerTypeName\"\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"整改时间\"\r\n        align=\"left\"\r\n        prop=\"changeTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column label=\"整改人\" align=\"left\" prop=\"changeName\" />\r\n      <!-- <el-table-column label=\"参与人\" align=\"left\" prop=\"participationNames\" /> -->\r\n      <!-- 复查人 -->\r\n      <el-table-column label=\"复查人\" align=\"left\" prop=\"reviewName\" />\r\n      <!-- 核验人 -->\r\n      <el-table-column label=\"核验人\" align=\"left\" prop=\"verifyManName\" />\r\n      <!-- 例行检查 -->\r\n      <el-table-column label=\"例行检查\" align=\"left\" prop=\"routineCheckNames\" />\r\n      <!-- 说明 -->\r\n      <el-table-column\r\n        label=\"说明\"\r\n        align=\"left\"\r\n        prop=\"remark\"\r\n        show-overflow-tooltip\r\n      />\r\n      <!-- <el-table-column label=\"项目ID\" align=\"left\" prop=\"projectId\" />\r\n      <el-table-column label=\"检查人id\" align=\"left\" prop=\"creatorId\" />\r\n\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"remark\" />\r\n      <el-table-column label=\"常规id\" align=\"left\" prop=\"routineId\" />\r\n      <el-table-column label=\"记录id\" align=\"left\" prop=\"recordId\" />\r\n      <el-table-column label=\"责任区域ID\" align=\"left\" prop=\"regionId\" />\r\n      <el-table-column\r\n        label=\"责任区域全id\"\r\n        align=\"left\"\r\n        prop=\"regionFullId\"\r\n      />\r\n      <el-table-column label=\"区域全称\" align=\"left\" prop=\"regionFullName\" />\r\n      <el-table-column label=\"关系\" align=\"left\" prop=\"relation\" />\r\n      <el-table-column label=\"隐患类别id\" align=\"left\" prop=\"dangerTypeId\" />\r\n\r\n      <el-table-column\r\n        label=\"隐患类别全称\"\r\n        align=\"left\"\r\n        prop=\"dangerTypeFullName\"\r\n      />\r\n      <el-table-column label=\"auto_recg\" align=\"left\" prop=\"autoRecg\" />\r\n      <el-table-column\r\n        label=\"隐患类目内容\"\r\n        align=\"left\"\r\n        prop=\"dangerItemContent\"\r\n      />\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"dangerDesc\" />\r\n\r\n      <el-table-column\r\n        label=\"整改时限(天)\"\r\n        align=\"left\"\r\n        prop=\"changeLimitTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeLimitTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"级别\" align=\"left\" prop=\"level\" />\r\n      <el-table-column label=\"级别名称\" align=\"left\" prop=\"levelName\" />\r\n      <el-table-column label=\"状态\" align=\"left\" prop=\"status\" />\r\n      <el-table-column label=\"整改人id\" align=\"left\" prop=\"changeId\" />\r\n      <el-table-column\r\n        label=\"参与人id\"\r\n        align=\"left\"\r\n        prop=\"participationIds\"\r\n      /> -->\r\n\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"left\"\r\n        class-name=\"small-padding fixed-width\"\r\n        fixed=\"right\"\r\n        width=\"150\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 查看详情 -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button\r\n          >\r\n          <!-- <el-button size=\"mini\" type=\"text\">打印</el-button> -->\r\n\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n            >修改</el-button\r\n          > -->\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n            >删除</el-button\r\n          > -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 详情弹窗 -->\r\n    <el-dialog\r\n      title=\"查看详情\"\r\n      :visible.sync=\"detailDialog\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      custom-class=\"detail-dialog\"\r\n    >\r\n      <div v-if=\"detailData\" class=\"detail-content\">\r\n        <!-- 问题记录部分 -->\r\n        <div class=\"independent-section\">\r\n          <h3 class=\"independent-title\">问题记录</h3>\r\n          <!-- 左右两列布局 -->\r\n          <div class=\"record-columns\">\r\n            <!-- 左列 -->\r\n            <div class=\"left-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查部位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.regionName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题描述:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerItemContent || detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题分类:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerTypeFullName ||\r\n                  detailData.dangerTypeName ||\r\n                  \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">补充说明:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题等级:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.levelName || detailData.level || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改要求:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.rectificationRequirements || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">轴线位置:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.axisPosition || \"8号18-17/D\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">分包单位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.contractorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右列 -->\r\n            <div class=\"right-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime || \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">紧急程度:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.urgencyLevel || \"一般\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime\r\n                    ? detailData.createTime.slice(0, 10)\r\n                    : \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改时限:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.changeLimitTime\r\n                    ? detailData.changeLimitTime.slice(0, 10)\r\n                    : \"2025-08-27\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewTime\r\n                    ? detailData.reviewTime.slice(0, 10)\r\n                    : \"2025-08-30\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">通知人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.notifierName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 现状整改部分 -->\r\n          <div class=\"field-row\">\r\n            <span class=\"field-label\">现状整改:</span>\r\n            <div class=\"status-row\">\r\n              <label\r\n                ><input\r\n                  type=\"checkbox\"\r\n                  :checked=\"detailData.status !== '1'\"\r\n                  disabled\r\n                />\r\n                未完成</label\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.hiddenDangerPictures\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.hiddenDangerPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"问题照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 整改记录部分 - 待整改状态(status=1)时不显示 -->\r\n        <div v-if=\"detailData.status !== '1'\" class=\"independent-section\">\r\n          <h3 class=\"independent-title\">整改记录</h3>\r\n          <!-- 整改信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getRectificationStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeTime\r\n                  ? detailData.changeTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 整改说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">整改说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.rectificationDesc || \"已整改\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div\r\n              v-if=\"detailData.rectificationPictures\"\r\n              class=\"photo-container\"\r\n            >\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.rectificationPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"整改照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 复查记录部分 - 待整改(status=1)和待复查(status=2)状态时不显示 -->\r\n        <div\r\n          v-if=\"detailData.status !== '1' && detailData.status !== '2'\"\r\n          class=\"independent-section\"\r\n        >\r\n          <h3 class=\"independent-title\">复查记录</h3>\r\n          <!-- 复查信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getReviewStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewTime\r\n                  ? detailData.reviewTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 复查说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">复查说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.reviewComments || \"-\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 复查照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.reviewPicUrl\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.reviewPicUrl\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"复查照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改质量检查台账对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"主键ID\" prop=\"id\">\r\n              <el-input v-model=\"form.id\" placeholder=\"请输入主键ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n              <el-input v-model=\"form.projectId\" placeholder=\"请输入项目ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n              <el-input v-model=\"form.creatorId\" placeholder=\"请输入检查人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人姓名\" prop=\"creatorName\">\r\n              <el-input\r\n                v-model=\"form.creatorName\"\r\n                placeholder=\"请输入检查人姓名\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"常规id\" prop=\"routineId\">\r\n              <el-input v-model=\"form.routineId\" placeholder=\"请输入常规id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input\r\n                v-model=\"form.projectName\"\r\n                placeholder=\"请输入项目名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"记录id\" prop=\"recordId\">\r\n              <el-input v-model=\"form.recordId\" placeholder=\"请输入记录id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n              <el-input\r\n                v-model=\"form.regionId\"\r\n                placeholder=\"请输入责任区域ID\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n              <el-input\r\n                v-model=\"form.regionFullId\"\r\n                placeholder=\"请输入责任区域全id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n              <el-input\r\n                v-model=\"form.regionName\"\r\n                placeholder=\"请输入区域名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n              <el-input\r\n                v-model=\"form.regionFullName\"\r\n                placeholder=\"请输入区域全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeId\"\r\n                placeholder=\"请输入隐患类别id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"关系\" prop=\"relation\">\r\n              <el-input v-model=\"form.relation\" placeholder=\"请输入关系\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别名称\" prop=\"dangerTypeName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeName\"\r\n                placeholder=\"请输入隐患类别名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeFullName\"\r\n                placeholder=\"请输入隐患类别全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n              <el-input v-model=\"form.autoRecg\" placeholder=\"请输入auto_recg\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n              <el-input v-model=\"form.dangerDesc\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"隐患类目内容\">\r\n              <editor v-model=\"form.dangerItemContent\" :min-height=\"192\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时间\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeLimitTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时限(天)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别\" prop=\"level\">\r\n              <el-input v-model=\"form.level\" placeholder=\"请输入级别\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n              <el-input v-model=\"form.levelName\" placeholder=\"请输入级别名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n              <el-input v-model=\"form.changeId\" placeholder=\"请输入整改人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人名称\" prop=\"changeName\">\r\n              <el-input\r\n                v-model=\"form.changeName\"\r\n                placeholder=\"请输入整改人名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n          <el-input\r\n            v-model=\"form.participationIds\"\r\n            placeholder=\"请输入参与人id\"\r\n          />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"参与人姓名\" prop=\"participationNames\">\r\n          <el-input\r\n            v-model=\"form.participationNames\"\r\n            placeholder=\"请输入参与人姓名\"\r\n          />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjQualityInspectionInfo,\r\n  getZjQualityInspectionInfo,\r\n  delZjQualityInspectionInfo,\r\n  addZjQualityInspectionInfo,\r\n  updateZjQualityInspectionInfo,\r\n} from \"@/api/inspection/zjQualityInspectionInfo\";\r\nimport { getEnterpriseInfo } from \"@/api/system/info\";\r\n\r\nexport default {\r\n  name: \"ZjQualityInspectionInfo\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 质量检查台账表格数据\r\n      zjQualityInspectionInfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectId: null,\r\n        creatorId: null,\r\n        unitName: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: \"主键ID不能为空\", trigger: \"blur\" }],\r\n      },\r\n      // 详情弹窗\r\n      detailDialog: false,\r\n      // 详情数据\r\n      detailData: {},\r\n      // 公司树相关数据\r\n      companyTreeLoading: false,\r\n      companyTreeData: [],\r\n      companyTreeProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n      },\r\n      // 检查结果选择项\r\n      checkResultOptions: [\r\n        {\r\n          label: \"无需整改\",\r\n          value: 0,\r\n        },\r\n        {\r\n          label: \"待整改\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"已整改\",\r\n          value: 2,\r\n        },\r\n        {\r\n          label: \"已合格\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"不合格\",\r\n          value: 4,\r\n        },\r\n        {\r\n          label: \"待核验\",\r\n          value: 7,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCompanyTreeData();\r\n  },\r\n  methods: {\r\n    getStatusClass(status) {\r\n      const statusMap = {\r\n        0: \"bg-blue\", // 无需整改\r\n        1: \"bg-orange\", // 待整改\r\n        2: \"bg-green\", // 已整改\r\n        3: \"bg-green\", // 已合格\r\n        4: \"bg-orange\", // 不合格\r\n        7: \"bg-blue\", // 待核验\r\n      };\r\n      return statusMap[status] || \"bg-blue\";\r\n    },\r\n    // 获取检查结果文字\r\n    getCheckResultText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    /** 查询质量检查台账列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjQualityInspectionInfo(this.queryParams).then((response) => {\r\n        this.zjQualityInspectionInfoList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        projectId: null,\r\n        createTime: null,\r\n        creatorId: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加质量检查台账\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改质量检查台账\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除质量检查台账编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjQualityInspectionInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/zjQualityInspectionInfo/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjQualityInspectionInfo_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    // 查看详情\r\n    handleDetail(row) {\r\n      const id = row.id;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.detailData = response.data;\r\n        this.detailDialog = true;\r\n      });\r\n    },\r\n    // 获取整改状态文字\r\n    getRectificationStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    // 获取复查状态文字\r\n    getReviewStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需复查\",\r\n        1: \"待复查\",\r\n        2: \"待复查\",\r\n        3: \"复查合格\",\r\n        4: \"复查不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未复查\";\r\n    },\r\n    // 解析图片URL列表\r\n    parseImageUrls(imageUrl, dataSource) {\r\n      if (!imageUrl) return [];\r\n\r\n      // 当dataSource为2时，处理后端拼接好的URL\r\n      if (dataSource === 2 || dataSource === \"2\") {\r\n        // 移除开头的@符号，然后按逗号分割\r\n        const urlStr = imageUrl.startsWith(\"@\")\r\n          ? imageUrl.substring(1)\r\n          : imageUrl;\r\n        return urlStr\r\n          .split(\",\")\r\n          .filter((url) => url.trim())\r\n          .map((url) => url.trim());\r\n      }\r\n\r\n      // 默认情况：前端拼接\r\n      if (imageUrl.startsWith(\"/\")) {\r\n        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`];\r\n      }\r\n      return [imageUrl];\r\n    },\r\n    // 获取图片URL（兼容原有逻辑）\r\n    getImageUrl(imageUrl) {\r\n      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n      return urls.length > 0 ? urls[0] : \"\";\r\n    },\r\n    // 获取所有图片URL\r\n    getAllImageUrls(imageUrl) {\r\n      return this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n    },\r\n    // 预览图片\r\n    previewImage(imageUrl) {\r\n      if (!imageUrl) return;\r\n      // 使用element-ui的图片预览功能\r\n      this.$alert(\r\n        `<img src=\"${imageUrl}\" style=\"width: 100%; max-width: 500px;\" alt=\"预览图片\">`,\r\n        \"图片预览\",\r\n        {\r\n          dangerouslyUseHTMLString: true,\r\n          customClass: \"image-preview-dialog\",\r\n          showConfirmButton: false,\r\n          showCancelButton: true,\r\n          cancelButtonText: \"关闭\",\r\n        }\r\n      );\r\n    },\r\n    // 处理公司节点点击\r\n    handleQueryCompanyNodeClick(data) {\r\n      this.queryParams.unitName = data.label;\r\n      this.$refs.queryCompanySelect.blur();\r\n    },\r\n    // 获取公司树数据\r\n    getCompanyTreeData() {\r\n      this.companyTreeLoading = true;\r\n      getEnterpriseInfo()\r\n        .then((res) => {\r\n          const deptList = res.data;\r\n          this.companyTreeData = [];\r\n          deptList.forEach((item) => {\r\n            this.companyTreeData.push({\r\n              label: item.label,\r\n              id: item.id,\r\n              children: item.children,\r\n            });\r\n          });\r\n          this.companyTreeLoading = false;\r\n        })\r\n        .catch((err) => {\r\n          this.companyTreeLoading = false;\r\n          console.error(err);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.font-12 {\r\n  font-size: 12px;\r\n}\r\n\r\n.circle {\r\n  width: 8px;\r\n  height: 8px;\r\n  display: inline-block;\r\n  border-radius: 50%;\r\n  margin-right: 2px;\r\n}\r\n\r\n.bg-orange {\r\n  background-color: #ffa500;\r\n}\r\n\r\n.bg-blue {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bg-green {\r\n  background-color: #28a745;\r\n}\r\n\r\n/* 详情弹窗样式 */\r\n:deep(.detail-dialog) {\r\n  .el-dialog__header {\r\n    background-color: #f5f5f5;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n    padding: 0px 20px !important;\r\n  }\r\n\r\n  ::v-deep(.el-dialog__body) {\r\n    padding: 0px 20px !important;\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  padding: 0 20px;\r\n\r\n  .independent-section {\r\n    margin-bottom: 10px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .independent-title {\r\n    margin: 0 0 20px 0;\r\n    padding: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n\r\n    &:after {\r\n      content: \"\";\r\n      flex: 1;\r\n      height: 2px;\r\n      border-top: 2px dashed #409eff;\r\n    }\r\n  }\r\n\r\n  // 左右两列布局\r\n  .record-columns {\r\n    display: flex;\r\n    gap: 40px;\r\n  }\r\n\r\n  .left-column,\r\n  .right-column {\r\n    flex: 1;\r\n  }\r\n\r\n  .field-row {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 16px;\r\n\r\n    &.full-width {\r\n      width: 100%;\r\n    }\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    &.highlighted-field {\r\n      background-color: #ebecf0;\r\n      padding: 8px 0;\r\n      margin-bottom: 0;\r\n      margin-left: -20px;\r\n      margin-right: -20px;\r\n      padding-left: 20px;\r\n      padding-right: 20px;\r\n    }\r\n  }\r\n\r\n  // 非高亮字段与高亮字段之间的间距\r\n  .field-row:not(.highlighted-field) + .field-row.highlighted-field,\r\n  .field-row.highlighted-field + .field-row:not(.highlighted-field) {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  .field-label {\r\n    min-width: 70px;\r\n    font-weight: 400;\r\n    color: #666;\r\n    margin-right: 10px;\r\n    white-space: nowrap;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .field-value {\r\n    color: #333;\r\n    word-break: break-all;\r\n    line-height: 1.5;\r\n    font-size: 14px;\r\n    flex: 1;\r\n\r\n    &.status-tag {\r\n      padding: 2px 8px;\r\n      border-radius: 3px;\r\n      font-size: 12px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  // 整改记录部分样式\r\n  .rectification-info {\r\n    display: flex;\r\n    gap: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #ebecf0;\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n    padding: 8px 20px;\r\n\r\n    .field-row {\r\n      margin-bottom: 0;\r\n      white-space: nowrap;\r\n      flex: 1;\r\n      margin-left: 0;\r\n      margin-right: 0;\r\n      padding: 0 20px;\r\n      background-color: transparent;\r\n\r\n      &:first-child {\r\n        padding-left: 0;\r\n      }\r\n\r\n      &:last-child {\r\n        padding-right: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 状态标签颜色\r\n  .status-no-need {\r\n    background-color: #f0f9ff;\r\n    color: #0369a1;\r\n    border: 1px solid #7dd3fc;\r\n  }\r\n\r\n  .status-pending {\r\n    background-color: transparent;\r\n    color: #d97706;\r\n    border: none;\r\n  }\r\n\r\n  .status-rectified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-qualified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-unqualified {\r\n    background-color: #fee2e2;\r\n    color: #dc2626;\r\n    border: 1px solid #f87171;\r\n  }\r\n\r\n  .status-verify {\r\n    background-color: #ede9fe;\r\n    color: #7c3aed;\r\n    border: 1px solid #a78bfa;\r\n  }\r\n\r\n  .status-unknown {\r\n    background-color: #f3f4f6;\r\n    color: #6b7280;\r\n    border: 1px solid #d1d5db;\r\n  }\r\n\r\n  // 状态行样式\r\n  .status-row {\r\n    margin-top: 8px;\r\n\r\n    label {\r\n      margin-right: 15px;\r\n      color: #666;\r\n      font-size: 14px;\r\n\r\n      input[type=\"checkbox\"] {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 照片相关样式\r\n  .photo-section {\r\n    padding-top: 10px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .photo-container {\r\n    margin-top: 10px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n  }\r\n\r\n  .photo-item,\r\n  .photo-placeholder {\r\n    border: 1px solid #e6e6e6;\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n    cursor: pointer;\r\n    transition: all 0.2s;\r\n    background: #fafafa;\r\n\r\n    &:hover {\r\n      transform: scale(1.02);\r\n      border-color: #409eff;\r\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n    }\r\n\r\n    img {\r\n      width: 120px;\r\n      height: 90px;\r\n      object-fit: cover;\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .photo-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #f8f9fa;\r\n    border: 1px dashed #d1d5db;\r\n\r\n    &:hover {\r\n      transform: none;\r\n      border-color: #d1d5db;\r\n      box-shadow: none;\r\n    }\r\n\r\n    img {\r\n      opacity: 0.3;\r\n      background: transparent;\r\n    }\r\n  }\r\n\r\n  .no-photo {\r\n    color: #999;\r\n    font-style: italic;\r\n    font-size: 12px;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    .record-columns {\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    .rectification-info {\r\n      flex-direction: column;\r\n      gap: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n// 图片预览弹窗样式\r\n:deep(.image-preview-dialog) {\r\n  .el-message-box__content {\r\n    text-align: center;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// 公司筛选下拉框样式\r\n.tree-select-wrapper {\r\n  padding: 8px;\r\n  min-height: 200px;\r\n  max-height: 300px;\r\n  overflow: auto;\r\n}\r\n\r\n:deep(.org-tree-select-dropdown) {\r\n  .el-select-dropdown__item {\r\n    height: auto;\r\n    max-height: 300px;\r\n    padding: 0;\r\n    overflow: hidden;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .el-tree-node__content {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .el-tree-node__label {\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}