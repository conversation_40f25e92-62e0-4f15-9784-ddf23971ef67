<template>
  <div class="app-container">
    <el-row>
      <!-- <el-col :span="4">
        <orgTree :type="'1'" @nodeClick="handleOrgTreeNodeClick"></orgTree>
      </el-col> -->
      <el-col :span="24" style="margin-left: 2px">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <el-form-item label="项目名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入项目名称"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <!-- 所属公司 -->
          <el-form-item label="所属公司" prop="belongCompany">
            <el-select
              v-model="queryParams.belongCompany"
              placeholder="请选择所属公司"
              style="width: 300px"
              filterable
              clearable
            >
              <el-option
                v-for="item in companyList"
                :key="item.belongCompany"
                :label="item.belongCompany"
                :value="item.belongCompany"
              />
            </el-select>
          </el-form-item>

          <!-- 项目状态 -->
          <el-form-item label="项目状态" prop="constructStatus">
            <el-select
              v-model="queryParams.constructStatus"
              placeholder="请选择项目状态"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleQuery"
            >
              <el-option
                v-for="dict in dict.type.zj_construct_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="项目简称" prop="shortName">
        <el-input
          v-model="queryParams.shortName"
          placeholder="请输入项目简称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目同步编码" prop="syncCode">
        <el-input
          v-model="queryParams.syncCode"
          placeholder="请输入项目同步编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <!-- <el-form-item label="项目经理" prop="manager">
            <el-input v-model="queryParams.manager" placeholder="请输入项目经理" clearable style="width: 300px"
              @keyup.enter.native="handleQuery" />
          </el-form-item> -->
          <!-- <el-form-item label="项目经理电话" prop="managerMobile">
        <el-input
          v-model="queryParams.managerMobile"
          placeholder="请输入项目经理电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工程用途" prop="constructPurpose">
        <el-input
          v-model="queryParams.constructPurpose"
          placeholder="请输入工程用途"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="合同工期开始" prop="planStart">
        <el-date-picker
          clearable
          v-model="queryParams.planStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择合同工期开始"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="合同工期结束" prop="planEnd">
        <el-date-picker
          clearable
          v-model="queryParams.planEnd"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择合同工期结束"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="实际工期开始" prop="actualStart">
        <el-date-picker
          clearable
          v-model="queryParams.actualStart"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择实际工期开始"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="建筑面积(平方米)" prop="area">
        <el-input
          v-model="queryParams.area"
          placeholder="请输入建筑面积(平方米)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工程地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入工程地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="经度" prop="longitude">
        <el-input
          v-model="queryParams.longitude"
          placeholder="请输入经度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="纬度" prop="latitude">
        <el-input
          v-model="queryParams.latitude"
          placeholder="请输入纬度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="中标单位" prop="biddingUnit">
        <el-input
          v-model="queryParams.biddingUnit"
          placeholder="请输入中标单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="建设单位" prop="constructUnit">
        <el-input
          v-model="queryParams.constructUnit"
          placeholder="请输入建设单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="监理单位" prop="supervisingUnit">
        <el-input
          v-model="queryParams.supervisingUnit"
          placeholder="请输入监理单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新日期" prop="updateDate">
        <el-date-picker
          clearable
          v-model="queryParams.updateDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="删除状态" prop="deleted">
        <el-input
          v-model="queryParams.deleted"
          placeholder="请输入删除状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省份"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入市"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="区" prop="district">
        <el-input
          v-model="queryParams.district"
          placeholder="请输入区"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="组织全路径ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入组织全路径ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:zjProjectInfo:add']"
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:zjProjectInfo:edit']"
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              >修改</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:zjProjectInfo:remove']"
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:zjProjectInfo:export']"
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table
          v-loading="loading"
          :data="zjProjectInfoList"
          height="calc(100vh - 230px)"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="id" /> -->
          <!-- <el-table-column label="${comment}" align="center" prop="orgId" />
      <el-table-column label="${comment}" align="center" prop="parentOrgId" /> -->
          <!-- <el-table-column label="组织类型" align="center" prop="orgType" /> -->
          <el-table-column
            label="项目名称"
            align="center"
            prop="name"
            width="200"
            show-overflow-tooltip
          />
          <!-- <el-table-column
            label="编码"
            align="center"
            prop="code"
            width="200"
            show-overflow-tooltip
          /> -->
          <!-- 所属公司 -->
          <el-table-column
            label="所属公司"
            align="center"
            prop="belongCompany"
          />

          <!-- <el-table-column label="项目简称" align="center" prop="shortName" />
      <el-table-column label="项目同步编码" align="center" prop="syncCode" /> -->
          <!-- <el-table-column label="手机" align="center" prop="managerMobile" /> -->
          <el-table-column
            label="工程类别"
            align="center"
            prop="constructType"
            show-overflow-tooltip
          />
          <el-table-column
            label="项目状态"
            align="center"
            prop="constructStatus"
          >
            <template slot-scope="{ row }">
              {{ getConstructStatus(row.constructStatus) }}
            </template>
          </el-table-column>
          <el-table-column label="项目经理" align="center" prop="manager" />

          <!-- <el-table-column
            label="所属单位"
            align="center"
            prop="biddingUnit"
            width="180"
            show-overflow-tooltip
          >
          </el-table-column> -->
          <!-- <el-table-column
        label="工程用途"
        align="center"
        prop="constructPurpose"
      /> -->
          <!-- <el-table-column label="结构类型" align="center" prop="structType" />
      <el-table-column
        label="合同工期开始"
        align="center"
        prop="planStart"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planStart, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="合同工期结束"
        align="center"
        prop="planEnd"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.planEnd, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="实际工期开始"
        align="center"
        prop="actualStart"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.actualStart, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="建筑面积(平方米)" align="center" prop="area" />
      <el-table-column label="工程地点" align="center" prop="location" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitude" />
      <el-table-column label="中标单位" align="center" prop="biddingUnit" />
      <el-table-column label="建设单位" align="center" prop="constructUnit" />
      <el-table-column label="监理单位" align="center" prop="supervisingUnit" />
      <el-table-column
        label="更新日期"
        align="center"
        prop="updateDate"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateDate, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="删除状态" align="center" prop="deleted" />
      <el-table-column label="省份" align="center" prop="province" />
      <el-table-column label="市" align="center" prop="city" />
      <el-table-column label="区" align="center" prop="district" />
      <el-table-column label="项目编码" align="center" prop="projectCode" />
      <el-table-column label="组织全路径ID" align="center" prop="id" /> -->
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="180"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['inspection:zjProjectInfo:detail']"
                size="mini"
                type="text"
                icon="el-icon-view"
                @click="handleDetail(scope.row)"
                >查看</el-button
              >

              <el-button
                v-hasPermi="['inspection:zjProjectInfo:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                v-hasPermi="['inspection:zjProjectInfo:remove']"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改项目信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-form-item label="${comment}" prop="orgId">
          <el-input v-model="form.orgId" placeholder="请输入${comment}" />
        </el-form-item>
        <el-form-item label="${comment}" prop="parentOrgId">
          <el-input v-model="form.parentOrgId" placeholder="请输入${comment}" />
        </el-form-item> -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入项目名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="请输入项目简称" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="项目同步编码" prop="syncCode">
          <el-input v-model="form.syncCode" placeholder="请输入项目同步编码" />
        </el-form-item> -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目经理" prop="manager">
              <el-input v-model="form.manager" placeholder="请输入项目经理" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目经理电话" prop="managerMobile">
              <el-input v-model="form.managerMobile" placeholder="请输入手机" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目状态" prop="constructStatus">
              <el-select
                v-model="form.constructStatus"
                placeholder="请选择项目状态"
                style="width: 100%"
              >
                <el-option
                  v-for="dict in dict.type.zj_construct_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 项目状态 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- 所属单位 -->
            <el-form-item label="部门名称" prop="belongCompany">
              <selectPeopleTree
                v-model="form.belongCompany"
                :people-list="allCompanyList"
                placeholder="请搜索或选择部门名称"
                @change="handleChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工程类别" prop="constructType">
              <el-input
                v-model="form.constructType"
                placeholder="请输入工程类别"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工程用途" prop="constructPurpose">
              <el-input
                v-model="form.constructPurpose"
                placeholder="请输入工程用途"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结构类型" prop="structureType">
              <el-input
                v-model="form.structureType"
                placeholder="请输入结构类型"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同工期开始" prop="planStart">
              <el-date-picker
                v-model="form.planStart"
                clearable
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择合同工期开始"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同工期结束" prop="planEnd">
              <el-date-picker
                v-model="form.planEnd"
                clearable
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择合同工期结束"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际工期开始" prop="actualStart">
              <el-date-picker
                v-model="form.actualStart"
                clearable
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择实际工期开始"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建筑面积(平方米)" prop="area">
              <el-input
                v-model="form.area"
                placeholder="请输入建筑面积(平方米)"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工程地点" prop="location">
              <el-input v-model="form.location" placeholder="请输入工程地点" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="中标单位" prop="biddingUnit">
              <el-input
                v-model="form.biddingUnit"
                placeholder="请输入中标单位"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建设单位" prop="constructUnit">
              <el-input
                v-model="form.constructUnit"
                placeholder="请输入建设单位"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监理单位" prop="supervisingUnit">
              <el-input
                v-model="form.supervisingUnit"
                placeholder="请输入监理单位"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="更新日期" prop="updateDate">
          <el-date-picker
            clearable
            v-model="form.updateDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除状态" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除状态" />
        </el-form-item> -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="省份" prop="province">
              <el-input v-model="form.province" placeholder="请输入省份" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="市" prop="city">
              <el-input v-model="form.city" placeholder="请输入市" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="区" prop="district">
          <el-input v-model="form.district" placeholder="请输入区" />
        </el-form-item>
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="组织全路径ID" prop="id">
          <el-input v-model="form.id" placeholder="请输入组织全路径ID" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="查看项目详情"
      :visible.sync="openView"
      width="900px"
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              {{ form.name || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目简称" prop="shortName">
              {{ form.shortName || "-" }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目经理" prop="manager">
              {{ form.manager || "-" }}
            </el-form-item></el-col
          >
          <el-col :span="8">
            <el-form-item label="项目经理电话" prop="managerMobile">
              {{ form.managerMobile || "-" }}
            </el-form-item></el-col
          >
          <el-col :span="8">
            <el-form-item label="项目状态" prop="constructStatus">
              {{ getConstructStatus(form.constructStatus) || "-" }}
            </el-form-item></el-col
          >
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属公司" prop="belongCompany">
              {{ form.belongCompany || "-" }}
            </el-form-item></el-col
          >
          <el-col :span="12">
            <el-form-item label="工程类别" prop="constructType">
              {{ form.constructType || "-" }}
            </el-form-item></el-col
          >
        </el-row>

        <!-- 行业类别 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工程用途" prop="constructPurpose">
              {{ form.constructPurpose || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结构类型" prop="structureType">
              {{ form.structureType || "-" }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合同工期开始" prop="planStart">
              {{ form.planStart || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同工期结束" prop="planEnd">
              {{ form.planEnd || "-" }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际工期开始" prop="actualStart">
              {{ form.actualStart }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建筑面积(平方米)" prop="area">
              {{ form.area || "-" }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工程地点" prop="location">
              {{ form.location || "-" }}
            </el-form-item></el-col
          >
          <el-col :span="12">
            <el-form-item label="中标单位" prop="biddingUnit">
              {{ form.biddingUnit || "-" }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="建设单位" prop="constructUnit">
              {{ form.constructUnit || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="监理单位" prop="supervisingUnit">
              {{ form.supervisingUnit || "-" }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="省份" prop="province">
              {{ form.province || "-" }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="市" prop="city">
              {{ form.city || "-" }}
            </el-form-item>
          </el-col>
        </el-row>

        <!--
        <el-form-item label="经度" prop="longitude">
          <el-input v-model="form.longitude" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度" prop="latitude">
          <el-input v-model="form.latitude" placeholder="请输入纬度" />
        </el-form-item>

        <el-form-item label="更新日期" prop="updateDate">
          <el-date-picker
            clearable
            v-model="form.updateDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择更新日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="删除状态" prop="deleted">
          <el-input v-model="form.deleted" placeholder="请输入删除状态" />
        </el-form-item>
        <el-form-item label="区" prop="district">
          <el-input v-model="form.district" placeholder="请输入区" />
        </el-form-item>
        <el-form-item label="项目编码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目编码" />
        </el-form-item>
        <el-form-item label="组织全路径ID" prop="id">
          <el-input v-model="form.id" placeholder="请输入组织全路径ID" />
        </el-form-item>-->
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjProjectInfo,
  getZjProjectInfo,
  delZjProjectInfo,
  addZjProjectInfo,
  updateZjProjectInfo,
} from "@/api/inspection/zjProjectInfo";
import { getDicts } from "@/api/system/dict/data";
import { getCompanyList, querytree } from "@/api/system/info";
// import orgTree from "../../components/orgTree.vue";
import selectPeopleTree from "@/views/components/selectPeopleTree.vue";

export default {
  name: "ZjProjectInfo",
  dicts: ["zj_construct_status"],
  components: {
    // orgTree,
    selectPeopleTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目信息表格数据
      zjProjectInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openView: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentOrgId: null,
        orgId: null,
        belongCompany: null,
        orgType: null,
        code: null,
        name: null,
        shortName: null,
        syncCode: null,
        manager: null,
        managerMobile: null,
        constructStatus: "1",
        constructType: null,
        constructPurpose: null,
        structType: null,
        planStart: null,
        planEnd: null,
        actualStart: null,
        area: null,
        location: null,
        longitude: null,
        latitude: null,
        biddingUnit: null,
        constructUnit: null,
        supervisingUnit: null,
        updateDate: null,
        deleted: null,
        province: null,
        city: null,
        district: null,
        projectCode: null,
        id: null,
      },

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        shortName: [
          { required: true, message: "项目简称不能为空", trigger: "blur" },
        ],
        manager: [
          { required: true, message: "项目经理不能为空", trigger: "blur" },
        ],
        managerMobile: [
          { required: true, message: "项目经理电话不能为空", trigger: "blur" },
        ],
        belongCompany: [
          { required: true, message: "部门名称不能为空", trigger: "blur" },
        ],
        constructStatus: [
          { required: true, message: "项目状态不能为空", trigger: "blur" },
        ],
      },
      placeholder: "请输入所属单位",
      constructStatusDict: [],
      companyList: [],
      allCompanyList: [],
    };
  },
  created() {
    this.getList();
    this.getConstructStatusDict();
    this.getCompanyList();
    this.getAllCompanyList();
  },
  methods: {
    handleChange(selectedItem) {
      if (selectedItem.id == "654470716198912") {
        this.$message.error("请选择下级单位");
        return;
      }
      if (selectedItem) {
        this.form.belongCompany = selectedItem.label;

        this.form.orgId = selectedItem.id;
      } else {
        this.form.belongCompany = null;
        this.form.orgId = null;
      }
    },
    getAllCompanyList() {
      querytree().then((res) => {
        if (res.code == 200) {
          this.allCompanyList = res.data;
        }
      });
    },
    getCompanyList() {
      getCompanyList().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },
    getConstructStatusDict() {
      getDicts("zj_construct_status").then((res) => {
        res.data.forEach((item) => {
          this.constructStatusDict[item.dictValue] = item.dictLabel;
        });
      });
    },
    getConstructStatus(status) {
      return this.constructStatusDict[status];
    },

    handleOrgTreeNodeClick(node) {
      // 在这里处理接收到的子组件数据
      console.log("接收到子组件数据:", node.id);
      // 可以根据nodeData更新查询参数或其他状态
      this.queryParams.parentOrgId = node.id; // 假设nodeData中有id字段
      this.handleQuery(); // 触发查询
    },

    /** 查询项目信息列表 */
    getList() {
      this.loading = true;
      listZjProjectInfo(this.queryParams).then((res) => {
        this.zjProjectInfoList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        orgId: null,
        belongCompany: null,
        parentOrgId: null,
        orgType: null,
        code: null,
        name: null,
        shortName: null,
        syncCode: null,
        manager: null,
        managerMobile: null,
        constructStatus: null,
        constructType: null,
        constructPurpose: null,
        structType: null,
        planStart: null,
        planEnd: null,
        actualStart: null,
        area: null,
        location: null,
        longitude: null,
        latitude: null,
        biddingUnit: null,
        constructUnit: null,
        supervisingUnit: null,
        updateDate: null,
        deleted: null,
        province: null,
        city: null,
        district: null,
        projectCode: null,
        id: null,
      };
      this.belongCompany = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");

      this.queryParams.parentOrgId = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目信息";
    },
    handleDetail(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjProjectInfo(id).then((res) => {
        this.form = res.data;
        this.openView = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getZjProjectInfo(id).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改项目信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateZjProjectInfo(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.getCompanyList();
            });
          } else {
            addZjProjectInfo(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.getCompanyList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除项目信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delZjProjectInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/zjProjectInfo/export",
        {
          ...this.queryParams,
        },
        `zjProjectInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
