{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjQualityProblemInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjQualityProblemInfo\\index.vue", "mtime": 1757424959633}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0WmpRdWFsaXR5UHJvYmxlbUluZm8sDQogIGdldFpqUXVhbGl0eVByb2JsZW1JbmZvLA0KICBkZWxaalF1YWxpdHlQcm9ibGVtSW5mbywNCiAgYWRkWmpRdWFsaXR5UHJvYmxlbUluZm8sDQogIHVwZGF0ZVpqUXVhbGl0eVByb2JsZW1JbmZvLA0KICBsaXN0WmpRdWFsaXR5UHJvYmxlbUNhdGVnb3J5LA0KICBsaXN0WmpRdWFsaXR5UHJvYmxlbUNhdGVnb3J5Rmlyc3QNCn0gZnJvbSAnQC9hcGkvaW5zcGVjdGlvbi96alF1YWxpdHlQcm9ibGVtSW5mbycNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnWmpRdWFsaXR5UHJvYmxlbUluZm8nLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBxdWFsaXR5VHlwZTogJ+aIv+W7uicsDQogICAgICBxdWFsaXR5VHlwZUxpc3Q6IFsNCiAgICAgICAgeyBpZDogMSwgbGFiZWw6ICfmiL/lu7onIH0sDQogICAgICAgIHsgaWQ6IDIsIGxhYmVsOiAn5biC5pS/JyB9LA0KICAgICAgICB7IGlkOiAzLCBsYWJlbDogJ+WFrOi3rycgfSwNCiAgICAgICAgeyBpZDogNCwgbGFiZWw6ICfpk4Hot68nIH0sDQogICAgICAgIHsgaWQ6IDUsIGxhYmVsOiAn5rC05YipJyB9LA0KICAgICAgICB7IGlkOiA2LCBsYWJlbDogJ+ahpeaigScgfSwNCiAgICAgICAgeyBpZDogNywgbGFiZWw6ICfpmqfpgZMnIH0sDQogICAgICAgIHsgaWQ6IDgsIGxhYmVsOiAn5Zyw6ZOBJyB9LA0KICAgICAgICB7IGlkOiA5LCBsYWJlbDogJ+a4r+WPo+iIqumBkycgfSwNCiAgICAgICAgeyBpZDogMTAsIGxhYmVsOiAn6YCa55SoJyB9DQogICAgICBdLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDotKjph4/pl67popjlupPooajmoLzmlbDmja4NCiAgICAgIHpqUXVhbGl0eVByb2JsZW1JbmZvTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAnJywNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgcGFyZW50SWQ6IG51bGwsDQogICAgICAgIGl0ZW1OYW1lOiBudWxsLA0KICAgICAgICBxdWFsaXR5Q29kZTogbnVsbCwNCiAgICAgICAgY29tbW9uUHJvYmxlbTogbnVsbCwNCiAgICAgICAgcmVjdGlmaWNhdGlvblJlcXVpcmVtZW50czogbnVsbCwNCiAgICAgICAgcHJvYmxlbUxldmVsOiBudWxsLA0KICAgICAgICBzdGF0dXM6IG51bGwsDQogICAgICAgIHJlY3RpZmljYXRpb25EZWFkbGluZTogbnVsbCwNCiAgICAgICAgcXVhbGl0eVR5cGU6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHsNCiAgICAgICAgcHJvYmxlbUxldmVsOiAn5LiA6Iis6Zeu6aKYJywNCiAgICAgICAgcXVlc3Rpb25DYXRlZ29yeTogbnVsbCwNCiAgICAgICAgcXVhbGl0eVN0YXR1czogMA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgcXVlc3Rpb25DYXRlZ29yeTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfpl67popjnsbvliKvkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIHByb2JsZW1MZXZlbDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICfpl67popjnrYnnuqfkuI3og73kuLrnqbonLCB0cmlnZ2VyOiAnY2hhbmdlJyB9DQogICAgICAgIF0sDQogICAgICAgIGNvbW1vblByb2JsZW06IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAn6Zeu6aKY5o+P6L+w5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgcmVjdGlmaWNhdGlvbkRlYWRsaW5lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+aVtOaUueaXtumZkCjlpKkp5LiN6IO95Li656m6JywgdHJpZ2dlcjogJ2JsdXInIH0sDQogICAgICAgICAgeyBwYXR0ZXJuOiAvXlsxLTldXGQqJC8sIG1lc3NhZ2U6ICfmlbTmlLnml7bpmZDlv4XpobvkuLrmraPmlbTmlbAnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBxdWFsaXR5U3RhdHVzOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+eKtuaAgeS4jeiDveS4uuepuicsIHRyaWdnZXI6ICdjaGFuZ2UnIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIHRyZWVEYXRhOiBbXSwNCiAgICAgIGRlZmF1bHRQcm9wczogew0KICAgICAgICBjaGlsZHJlbjogJ2NoaWxkcmVuJywNCiAgICAgICAgbGFiZWw6ICdsYWJlbCcsDQogICAgICAgIGlzTGVhZjogJ2lzTGVhZicNCiAgICAgIH0sDQogICAgICBzZWxlY3RJZDogbnVsbA0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEhhemFyZEhhemFyZENhdGVnb3J5KCkNCiAgICB0aGlzLmdldExpc3QoKSAvLyDpobXpnaLliJ3lp4vljJbml7bpu5jorqTmn6Xor6LmlbDmja4NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNvbW1hbmQoY29tbWFuZCkgew0KICAgICAgaWYgKHRoaXMuaWRzLmxlbmd0aCA8PSAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfor7fpgInmi6notKjph4/pl67popjmnaHnm64nLA0KICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJw0KICAgICAgICB9KQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIC8vIGNvbnNvbGUubG9nKHRoaXMuaWRzLCAidGhpcy5pZHMiKTsNCiAgICAgIGNvbnN0IHN0YXR1cyA9IGNvbW1hbmQgPT09ICdlbmFibGUnID8gMCA6IDENCiAgICAgIGNvbnN0IHByb21pc2VzID0gdGhpcy5pZHMubWFwKChpdGVtKSA9PiB7DQogICAgICAgIHJldHVybiB1cGRhdGVaalF1YWxpdHlQcm9ibGVtSW5mbyh7DQogICAgICAgICAgcXVhbGl0eUlkOiBpdGVtLnF1YWxpdHlJZCwNCiAgICAgICAgICBxdWFsaXR5U3RhdHVzOiBzdGF0dXMNCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgICBQcm9taXNlLmFsbFNldHRsZWQocHJvbWlzZXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCBzdWNjZXNzQ291bnQgPSByZXMuZmlsdGVyKA0KICAgICAgICAgIChpdGVtKSA9PiBpdGVtLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcNCiAgICAgICAgKS5sZW5ndGgNCiAgICAgICAgY29uc3QgZmFpbGVkUmVzdWx0cyA9IHJlcy5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uc3RhdHVzID09PSAncmVqZWN0ZWQnKQ0KICAgICAgICBpZiAoc3VjY2Vzc0NvdW50ID4gMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYCR7Y29tbWFuZCA9PT0gJ2VuYWJsZScgPyAn5ZCv55SoJyA6ICfnpoHnlKgnfeaIkOWKn2AsDQogICAgICAgICAgICB0eXBlOiAnc3VjY2VzcycNCiAgICAgICAgICB9KQ0KICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnN0IGVycm9yTWVzc2FnZXMgPSBmYWlsZWRSZXN1bHRzDQogICAgICAgICAgICAubWFwKChyZXN1bHQsIGluZGV4KSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IGlkID0gdGhpcy5pZHNbaW5kZXhdLnNlcmlhbE51bWJlcg0KICAgICAgICAgICAgICBjb25zdCBlcnJvck1zZyA9IGAke2NvbW1hbmQgPT09ICdlbmFibGUnID8gJ+WQr+eUqCcgOiAn56aB55SoJ33lpLHotKVgDQogICAgICAgICAgICAgIHJldHVybiBg5bqP5Y+35Li6ICR7aWR9IOeahOi0qOmHj+mXrumimOadoeebru+8miR7ZXJyb3JNc2d9YA0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5qb2luKCdcbicpDQoNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IGAke2Vycm9yTWVzc2FnZXN9YCwNCiAgICAgICAgICAgIHR5cGU6ICdlcnJvcicsDQogICAgICAgICAgICBkYW5nZXJvdXNseVVzZUhUTUxTdHJpbmc6IHRydWUNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2V0SGF6YXJkSGF6YXJkQ2F0ZWdvcnkoKSB7DQogICAgICB0aGlzLnRyZWVEYXRhID0gW10NCiAgICAgIHRoaXMudHJlZUxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBwYXJhbXMgPSB7DQogICAgICAgIHBhcmVudElkOiAnMCcsDQogICAgICAgIHF1YWxpdHlUeXBlOiB0aGlzLnF1YWxpdHlUeXBlDQogICAgICB9DQogICAgICBsaXN0WmpRdWFsaXR5UHJvYmxlbUNhdGVnb3J5Rmlyc3QocGFyYW1zKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgLy8gdGhpcy50cmVlRGF0YSA9IHJlcy5yb3dzOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXMucm93cw0KICAgICAgICAgIGRhdGEuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgdGhpcy50cmVlRGF0YS5wdXNoKHsNCiAgICAgICAgICAgICAgaWQ6IGl0ZW0ucXVhbGl0eUlkLA0KICAgICAgICAgICAgICBsYWJlbDogaXRlbS5pdGVtTmFtZSwNCiAgICAgICAgICAgICAgaXNMZWFmOiBmYWxzZSwNCiAgICAgICAgICAgICAgY2hpbGRyZW46IG51bGwNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2UNCiAgICAgICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLnRyZWVEYXRhLCAidHJlZURhdGEiKTsNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHJlZnJlc2hUcmVlKCkgew0KICAgICAgdGhpcy50cmVlRGF0YSA9IFtdIC8vIOa4heepuueOsOacieagkeaVsOaNrg0KICAgICAgdGhpcy5nZXRIYXphcmRIYXphcmRDYXRlZ29yeSgpIC8vIOmHjeaWsOWKoOi9veagkeaVsOaNrg0KICAgIH0sDQogICAgbG9hZE5vZGUobm9kZSwgcmVzb2x2ZSkgew0KICAgICAgaWYgKG5vZGUubGV2ZWwgPT09IDApIHsNCiAgICAgICAgcmV0dXJuIHJlc29sdmUoW10pDQogICAgICB9DQoNCiAgICAgIGNvbnN0IGN1cnJlbnROb2RlID0gbm9kZS5kYXRhDQogICAgICAvLyDoi6Xor6XoioLngrnlt7LmnIkgY2hpbGRyZW7vvIzor7TmmI7lt7Lor7fmsYLov4fvvIznm7TmjqXop6PmnpDnjrDmnIkgY2hpbGRyZW4NCiAgICAgIGlmIChjdXJyZW50Tm9kZS5jaGlsZHJlbikgew0KICAgICAgICByZXR1cm4gcmVzb2x2ZShjdXJyZW50Tm9kZS5jaGlsZHJlbikNCiAgICAgIH0NCiAgICAgIGNvbnN0IHBhcmVudElkID0gY3VycmVudE5vZGUuaWQNCiAgICAgIGxpc3RaalF1YWxpdHlQcm9ibGVtQ2F0ZWdvcnkoeyBwYXJlbnRJZCB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIGNvbnN0IGNoaWxkcmVuID0gcmVzLnJvd3MubWFwKChpdGVtKSA9PiAoew0KICAgICAgICAgICAgICBpZDogaXRlbS5xdWFsaXR5SWQsDQogICAgICAgICAgICAgIGxhYmVsOiBpdGVtLml0ZW1OYW1lLA0KICAgICAgICAgICAgICAvLyDmoLnmja7lrZDoioLngrnmmK/lkKblrZjlnKjliKTmlq3mmK/lkKbkuLrlj7blrZDoioLngrkNCiAgICAgICAgICAgICAgaXNMZWFmOiAhaXRlbS5jaGlsZHJlbiB8fCBpdGVtLmNoaWxkcmVuLmxlbmd0aCA9PT0gMA0KICAgICAgICAgICAgfSkpDQogICAgICAgICAgICAvLyDlsIblrZDoioLngrnmlbDmja7otYvlgLznu5nlvZPliY3oioLngrnnmoQgY2hpbGRyZW4g5bGe5oCnDQogICAgICAgICAgICBjdXJyZW50Tm9kZS5jaGlsZHJlbiA9IGNoaWxkcmVuDQogICAgICAgICAgICByZXNvbHZlKGNoaWxkcmVuKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICByZXNvbHZlKFtdKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICByZXNvbHZlKFtdKQ0KICAgICAgICB9KQ0KICAgIH0sDQogICAgdHJhbnNmb3JtQ2hpbGRyZW4oY2hpbGRyZW4pIHsNCiAgICAgIGNvbnNvbGUubG9nKGNoaWxkcmVuKQ0KICAgICAgaWYgKCFjaGlsZHJlbiB8fCBjaGlsZHJlbi5sZW5ndGggPT09IDApIHJldHVybiBbXQ0KICAgICAgcmV0dXJuIGNoaWxkcmVuLm1hcCgoY2hpbGQpID0+ICh7DQogICAgICAgIGxhYmVsOiBjaGlsZC5pdGVtTmFtZSwNCiAgICAgICAgaWQ6IGNoaWxkLnF1YWxpdHlJZCwNCiAgICAgICAgY2hpbGRyZW46IHRoaXMudHJhbnNmb3JtQ2hpbGRyZW4oY2hpbGQuY2hpbGRyZW4pDQogICAgICB9KSkNCiAgICB9LA0KICAgIGhhbmRsZU5vZGVDbGljayhub2RlRGF0YSwgbm9kZSkgew0KICAgICAgLy8gY29uc29sZS5sb2cobm9kZURhdGEsIG5vZGUsICJub2RlRGF0YSIpOw0KICAgICAgLy8gdGhpcy5xdWVyeVBhcmFtcy5oYXphcmRJZCA9IG5vZGVEYXRhLmlkOw0KICAgICAgaWYgKG5vZGUuaXNMZWFmKSB7DQogICAgICAgIC8vIOaJp+ihjOetm+mAieaTjeS9nA0KICAgICAgICB0aGlzLnNlbGVjdElkID0gbm9kZURhdGEuaWQNCiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICB9DQogICAgfSwNCiAgICBzZXRMYWJlbFJlZihlbCwgbm9kZSkgew0KICAgICAgaWYgKGVsKSB7DQogICAgICAgIHRoaXMubGFiZWxSZWZzLnNldChub2RlLmlkIHx8IG5vZGUubGFiZWwsIGVsKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOafpeivoui0qOmHj+mXrumimOW6k+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0WmpRdWFsaXR5UHJvYmxlbUluZm8odGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuempRdWFsaXR5UHJvYmxlbUluZm9MaXN0ID0gcmVzLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBxdWFsaXR5SWQ6IG51bGwsDQogICAgICAgIHBhcmVudElkOiBudWxsLA0KICAgICAgICBpdGVtTmFtZTogbnVsbCwNCiAgICAgICAgcXVhbGl0eUNvZGU6IG51bGwsDQogICAgICAgIGNvbW1vblByb2JsZW06IG51bGwsDQogICAgICAgIHJlY3RpZmljYXRpb25SZXF1aXJlbWVudHM6IG51bGwsDQogICAgICAgIHByb2JsZW1MZXZlbDogJ+S4gOiIrOmXrumimCcsDQogICAgICAgIHF1ZXN0aW9uQ2F0ZWdvcnk6IG51bGwsDQogICAgICAgIHF1YWxpdHlTdGF0dXM6IDAsDQogICAgICAgIHN0YXR1czogbnVsbCwNCiAgICAgICAgcmVjdGlmaWNhdGlvbkRlYWRsaW5lOiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHF1YWxpdHlUeXBlOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgnZm9ybScpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmVudElkID0gdGhpcy5zZWxlY3RJZA0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oJ3F1ZXJ5Rm9ybScpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5xdWFsaXR5SWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIC8vIOWmguaenOaciemAieS4reeahOagkeiKgueCue+8jOWImeWwhuWFtuiuvue9ruS4uuaWsOWinumhueeahOeItuiKgueCuQ0KICAgICAgaWYgKHRoaXMuc2VsZWN0SWQpIHsNCiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gdGhpcy5zZWxlY3RJZA0KICAgICAgICB0aGlzLmZvcm0ucXVhbGl0eVR5cGUgPSB0aGlzLnF1YWxpdHlUeXBlDQogICAgICB9DQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICB0aGlzLnRpdGxlID0gJ+a3u+WKoOi0qOmHj+mXrumimOW6kycNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIGNvbnN0IHF1YWxpdHlJZCA9IHJvdy5xdWFsaXR5SWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldFpqUXVhbGl0eVByb2JsZW1JbmZvKHF1YWxpdHlJZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlcy5kYXRhDQogICAgICAgIC8vIOehruS/nemXrumimOexu+WIq+Wtl+auteS7juihqOagvOihjOaVsOaNruS4reiOt+WPlg0KICAgICAgICB0aGlzLmZvcm0ucXVlc3Rpb25DYXRlZ29yeSA9IHJvdy5xdWVzdGlvbkNhdGVnb3J5DQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICfkv67mlLnotKjph4/pl67popjlupMnDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWydmb3JtJ10udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ucXVhbGl0eUlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVpqUXVhbGl0eVByb2JsZW1JbmZvKHRoaXMuZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoJ+S/ruaUueaIkOWKnycpDQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRaalF1YWxpdHlQcm9ibGVtSW5mbyh0aGlzLmZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCfmlrDlop7miJDlip8nKQ0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgcXVhbGl0eUlkcyA9IHJvdyAmJiByb3cucXVhbGl0eUlkID8gcm93LnF1YWxpdHlJZCA6IHRoaXMuaWRzDQogICAgICBjb25zdCBtZXNzYWdlID0gcm93ICYmIHJvdy5xdWFsaXR5SWQNCiAgICAgICAgPyBg5piv5ZCm56Gu6K6k5Yig6Zmk6LSo6YeP6Zeu6aKY5bqT57yW5Y+35Li6IiR7cXVhbGl0eUlkc30i55qE5pWw5o2u6aG577yfYA0KICAgICAgICA6IGDmmK/lkKbnoa7orqTliKDpmaTpgInkuK3nmoQke3RoaXMuaWRzLmxlbmd0aH3mnaHmlbDmja7pobnvvJ9gDQogICAgICANCiAgICAgIHRoaXMuJG1vZGFsDQogICAgICAgIC5jb25maXJtKG1lc3NhZ2UpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsWmpRdWFsaXR5UHJvYmxlbUluZm8ocXVhbGl0eUlkcykNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygn5Yig6Zmk5oiQ5YqfJykNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsgfSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICAvLyDlpoLmnpzmnInpgInkuK3orrDlvZXvvIzlr7zlh7rpgInkuK3nmoTvvJvlkKbliJnlr7zlh7rlhajpg6gNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPiAwKSB7DQogICAgICAgIC8vIOWvvOWHuumAieS4reiusOW9lQ0KICAgICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTlr7zlh7rpgInkuK3nmoQnICsgdGhpcy5pZHMubGVuZ3RoICsgJ+adoeiusOW9le+8nycpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICAgICAnaW5zcGVjdGlvbi96alF1YWxpdHlQcm9ibGVtSW5mby9leHBvcnQnLA0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICBpZHM6IHRoaXMuaWRzLmpvaW4oJywnKQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIGB6alF1YWxpdHlQcm9ibGVtSW5mb19zZWxlY3RlZF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgICAgICkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWvvOWHuuWFqOmDqOiusOW9le+8iOagueaNruafpeivouadoeS7tu+8jOS9huS4jeWMheWQq+WIhumhteWPguaVsO+8iQ0KICAgICAgICBjb25zdCBleHBvcnRQYXJhbXMgPSB7IC4uLnRoaXMucXVlcnlQYXJhbXMgfQ0KICAgICAgICAvLyDnp7vpmaTliIbpobXlj4LmlbDvvIznoa7kv53lr7zlh7rlhajpg6jmlbDmja4NCiAgICAgICAgZGVsZXRlIGV4cG9ydFBhcmFtcy5wYWdlTnVtDQogICAgICAgIGRlbGV0ZSBleHBvcnRQYXJhbXMucGFnZVNpemUNCg0KICAgICAgICB0aGlzLmRvd25sb2FkKA0KICAgICAgICAgICdpbnNwZWN0aW9uL3pqUXVhbGl0eVByb2JsZW1JbmZvL2V4cG9ydCcsDQogICAgICAgICAgZXhwb3J0UGFyYW1zLA0KICAgICAgICAgIGB6alF1YWxpdHlQcm9ibGVtSW5mb18ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgICApDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/zjQualityProblemInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- <el-col :span=\"3\"> -->\r\n        <!-- 左侧树结构 -->\r\n        <!-- <div class=\"title mb-2\" @click=\"goToHazardCategory\">质量问题类别</div> -->\r\n        <!-- 搜索框 -->\r\n        <!-- <el-input\r\n          v-model=\"searchText\"\r\n          placeholder=\"请输入搜索内容\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        /> -->\r\n        <!-- 下拉框 选择质量问题类别 -->\r\n        <!-- <el-select v-model=\"qualityType\" placeholder=\"请选择质量问题类别\" clearable @change=\"getHazardHazardCategory\">\r\n          <el-option v-for=\"item in qualityTypeList\" :key=\"item.id\" :label=\"item.label\" :value=\"item.label\" />\r\n        </el-select>\r\n\r\n        <el-tree v-loading=\"treeLoading\" :data=\"treeData\" :props=\"defaultProps\" :load=\"loadNode\" lazy\r\n          @node-click=\"handleNodeClick\">\r\n          <template #default=\"{ node, data }\">\r\n            <el-tooltip effect=\"dark\" :content=\"data.label\" placement=\"top\">\r\n              <span :ref=\"(el) => setLabelRef(el, node)\" class=\"el-tree-node__label\">\r\n                {{ node.label }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-tree> -->\r\n      <!-- </el-col> -->\r\n      <el-col :span=\"24\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :model=\"queryParams\" size=\"small\" :inline=\"true\"\r\n          label-width=\"68px\">\r\n          <!-- <el-form-item label=\"父类\" prop=\"parentId\">\r\n            <el-input\r\n              v-model=\"queryParams.parentId\"\r\n              placeholder=\"请输入父类\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item> -->\r\n          <!-- <el-form-item label=\"分部分项工程\" prop=\"itemName\">\r\n            <el-input\r\n              v-model=\"queryParams.itemName\"\r\n              placeholder=\"请输入分部分项工程\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"序号\" prop=\"qualityCode\">\r\n            <el-input\r\n              v-model=\"queryParams.qualityCode\"\r\n              placeholder=\"请输入序号\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item> -->\r\n          <el-form-item label=\"问题类别\" prop=\"qualityType\">\r\n            <el-input v-model=\"queryParams.qualityType\" placeholder=\"请输入问题类别\" clearable\r\n              @keyup.enter.native=\"handleQuery\" style=\"width: 180px\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"问题描述\" prop=\"commonProblem\">\r\n            <el-input v-model=\"queryParams.commonProblem\" placeholder=\"请输入问题描述\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <!-- <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n            <el-input\r\n              v-model=\"queryParams.rectificationRequirements\"\r\n              placeholder=\"请输入整改要求\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"问题级别\" prop=\"problemLevel\">\r\n            <el-input\r\n              v-model=\"queryParams.problemLevel\"\r\n              placeholder=\"请输入问题级别\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n            <el-input\r\n              v-model=\"queryParams.rectificationDeadline\"\r\n              placeholder=\"请输入整改时限(天)\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item> -->\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button v-hasPermi=\"['inspection:zjQualityProblemInfo:add']\" type=\"primary\" plain icon=\"el-icon-plus\"\r\n              size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-dropdown @command=\"handleCommand\">\r\n              <el-button v-hasPermi=\"['inspection:hazard:edit']\" type=\"success\" plain icon=\"el-icon-edit\"\r\n                size=\"mini\">修改状态<i class=\"el-icon-arrow-down el-icon--right\" /></el-button>\r\n              <template #dropdown>\r\n                <el-dropdown-menu style=\"width: 100px; text-align: center\">\r\n                  <!-- 下拉选项，可根据实际需求修改 -->\r\n                  <el-dropdown-item command=\"enable\">启用</el-dropdown-item>\r\n                  <el-dropdown-item command=\"disable\">禁用</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button v-hasPermi=\"['inspection:zjQualityProblemInfo:remove']\" type=\"danger\" plain icon=\"el-icon-delete\"\r\n              size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\">删除</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button v-hasPermi=\"['inspection:zjQualityProblemInfo:export']\" type=\"warning\" plain\r\n              icon=\"el-icon-download\" size=\"mini\" @click=\"handleExport\">导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"zjQualityProblemInfoList\" height=\"calc(100vh - 250px)\"\r\n          @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"主键\" align=\"center\" prop=\"qualityId\" />\r\n          <el-table-column label=\"父类\" align=\"center\" prop=\"parentId\" /> -->\r\n          <!-- <el-table-column\r\n            label=\"分部分项工程\"\r\n            align=\"center\"\r\n            prop=\"itemName\"\r\n          />\r\n          <el-table-column label=\"序号\" align=\"center\" prop=\"qualityCode\" /> -->\r\n          <el-table-column label=\"问题类别\" align=\"center\" prop=\"questionCategory\" width=\"180\" show-overflow-tooltip />\r\n          <el-table-column label=\"问题等级\" align=\"center\" prop=\"problemLevel\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.problemLevel === '严重问题'\">\r\n                <el-tag type=\"danger\">{{ scope.row.problemLevel }}</el-tag>\r\n              </span>\r\n              <span v-else>\r\n                <el-tag type=\"success\">{{ scope.row.problemLevel }}</el-tag>\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"问题描述\" align=\"center\" prop=\"commonProblem\">\r\n            <template slot-scope=\"{ row }\">\r\n              <div class=\"two-lines-ellipsis\">\r\n                {{ row.commonProblem }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <!-- <el-table-column\r\n            label=\"整改要求\"\r\n            align=\"center\"\r\n            prop=\"rectificationRequirements\"\r\n          /> -->\r\n          <el-table-column label=\"整改时限(天)\" align=\"center\" prop=\"rectificationDeadline\" show-overflow-tooltip />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"qualityStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"scope.row.qualityStatus === '0'\">\r\n                <el-tag type=\"success\">启用</el-tag>\r\n              </span>\r\n              <span v-else-if=\"scope.row.qualityStatus === '1'\">\r\n                <el-tag type=\"danger\">禁用</el-tag>\r\n              </span>\r\n              <span v-else>\r\n                <el-tag type=\"info\">-</el-tag>\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <!-- <el-table-column label=\"质量类别\" align=\"center\" prop=\"qualityType\" /> -->\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" fixed=\"right\" width=\"150\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-hasPermi=\"['inspection:zjQualityProblemInfo:edit']\" size=\"mini\" type=\"text\"\r\n                icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n              <el-button v-hasPermi=\"['inspection:zjQualityProblemInfo:remove']\" size=\"mini\" type=\"text\"\r\n                icon=\"el-icon-delete\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination v-show=\"total > 0\" :total=\"total\" :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\" @pagination=\"getList\" />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改质量问题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <!-- <el-form-item label=\"父类\" prop=\"parentId\">\r\n          <el-input v-model=\"form.parentId\" placeholder=\"请输入父类\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分部分项工程\" prop=\"itemName\">\r\n          <el-input v-model=\"form.itemName\" placeholder=\"请输入分部分项工程\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"序号\" prop=\"qualityCode\">\r\n          <el-input v-model=\"form.qualityCode\" placeholder=\"请输入序号\" />\r\n        </el-form-item> -->\r\n        <el-form-item label=\"问题类别\" prop=\"questionCategory\">\r\n          <!-- 修改模式下只读显示 -->\r\n          <div v-if=\"form.qualityId != null\" style=\"padding: 8px 0; min-height: 32px; line-height: 16px;\">\r\n            {{ form.questionCategory }}\r\n          </div>\r\n          <!-- 新增模式下可选择 -->\r\n          <el-select v-else v-model=\"form.questionCategory\" placeholder=\"请选择问题类别\" style=\"width: 100%\">\r\n            <el-option v-for=\"item in qualityTypeList\" :key=\"item.id\" :label=\"item.label\" :value=\"item.label\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"问题等级\" prop=\"problemLevel\">\r\n          <!-- <el-input v-model=\"form.problemLevel\" placeholder=\"请输入问题级别\" /> -->\r\n          <!-- 严重问题 一般问题 -->\r\n          <el-select v-model=\"form.problemLevel\" placeholder=\"请选择问题等级\" style=\"width: 100%\">\r\n            <el-option label=\"严重问题\" value=\"严重问题\" />\r\n            <el-option label=\"一般问题\" value=\"一般问题\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"问题描述\" prop=\"commonProblem\">\r\n          <el-input v-model=\"form.commonProblem\" placeholder=\"请输入常见问题\" type=\"textarea\" rows=\"5\" />\r\n        </el-form-item>\r\n\r\n        <!-- <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n          <el-input\r\n            v-model=\"form.rectificationRequirements\"\r\n            placeholder=\"请输入整改要求\"\r\n            type=\"textarea\"\r\n            rows=\"5\"\r\n          />\r\n        </el-form-item> -->\r\n\r\n        <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n          <el-input v-model=\"form.rectificationDeadline\" type=\"number\" min=\"0\" style=\"width: 100%\"\r\n            placeholder=\"请输入整改时限(天)\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"qualityStatus\">\r\n          <el-select v-model=\"form.qualityStatus\" placeholder=\"请选择状态\" style=\"width: 100%\">\r\n            <el-option label=\"启用\" :value=\"0\" />\r\n            <el-option label=\"禁用\" :value=\"1\" />\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjQualityProblemInfo,\r\n  getZjQualityProblemInfo,\r\n  delZjQualityProblemInfo,\r\n  addZjQualityProblemInfo,\r\n  updateZjQualityProblemInfo,\r\n  listZjQualityProblemCategory,\r\n  listZjQualityProblemCategoryFirst\r\n} from '@/api/inspection/zjQualityProblemInfo'\r\n\r\nexport default {\r\n  name: 'ZjQualityProblemInfo',\r\n  data() {\r\n    return {\r\n      qualityType: '房建',\r\n      qualityTypeList: [\r\n        { id: 1, label: '房建' },\r\n        { id: 2, label: '市政' },\r\n        { id: 3, label: '公路' },\r\n        { id: 4, label: '铁路' },\r\n        { id: 5, label: '水利' },\r\n        { id: 6, label: '桥梁' },\r\n        { id: 7, label: '隧道' },\r\n        { id: 8, label: '地铁' },\r\n        { id: 9, label: '港口航道' },\r\n        { id: 10, label: '通用' }\r\n      ],\r\n      // 遮罩层\r\n      loading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 质量问题库表格数据\r\n      zjQualityProblemInfoList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parentId: null,\r\n        itemName: null,\r\n        qualityCode: null,\r\n        commonProblem: null,\r\n        rectificationRequirements: null,\r\n        problemLevel: null,\r\n        status: null,\r\n        rectificationDeadline: null,\r\n        qualityType: null\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        problemLevel: '一般问题',\r\n        questionCategory: null,\r\n        qualityStatus: 0\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        questionCategory: [\r\n          { required: true, message: '问题类别不能为空', trigger: 'change' }\r\n        ],\r\n        problemLevel: [\r\n          { required: true, message: '问题等级不能为空', trigger: 'change' }\r\n        ],\r\n        commonProblem: [\r\n          { required: true, message: '问题描述不能为空', trigger: 'blur' }\r\n        ],\r\n        rectificationDeadline: [\r\n          { required: true, message: '整改时限(天)不能为空', trigger: 'blur' },\r\n          { pattern: /^[1-9]\\d*$/, message: '整改时限必须为正整数', trigger: 'blur' }\r\n        ],\r\n        qualityStatus: [\r\n          { required: true, message: '状态不能为空', trigger: 'change' }\r\n        ]\r\n      },\r\n      treeData: [],\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label',\r\n        isLeaf: 'isLeaf'\r\n      },\r\n      selectId: null\r\n    }\r\n  },\r\n  created() {\r\n    this.getHazardHazardCategory()\r\n    this.getList() // 页面初始化时默认查询数据\r\n  },\r\n  methods: {\r\n    handleCommand(command) {\r\n      if (this.ids.length <= 0) {\r\n        this.$message({\r\n          message: '请选择质量问题条目',\r\n          type: 'warning'\r\n        })\r\n        return\r\n      }\r\n      // console.log(this.ids, \"this.ids\");\r\n      const status = command === 'enable' ? 0 : 1\r\n      const promises = this.ids.map((item) => {\r\n        return updateZjQualityProblemInfo({\r\n          qualityId: item.qualityId,\r\n          qualityStatus: status\r\n        })\r\n      })\r\n      Promise.allSettled(promises).then((res) => {\r\n        const successCount = res.filter(\r\n          (item) => item.status === 'fulfilled'\r\n        ).length\r\n        const failedResults = res.filter((item) => item.status === 'rejected')\r\n        if (successCount > 0) {\r\n          this.$message({\r\n            message: `${command === 'enable' ? '启用' : '禁用'}成功`,\r\n            type: 'success'\r\n          })\r\n          this.handleQuery()\r\n        } else {\r\n          const errorMessages = failedResults\r\n            .map((result, index) => {\r\n              const id = this.ids[index].serialNumber\r\n              const errorMsg = `${command === 'enable' ? '启用' : '禁用'}失败`\r\n              return `序号为 ${id} 的质量问题条目：${errorMsg}`\r\n            })\r\n            .join('\\n')\r\n\r\n          this.$message({\r\n            message: `${errorMessages}`,\r\n            type: 'error',\r\n            dangerouslyUseHTMLString: true\r\n          })\r\n        }\r\n      })\r\n    },\r\n    getHazardHazardCategory() {\r\n      this.treeData = []\r\n      this.treeLoading = true\r\n      const params = {\r\n        parentId: '0',\r\n        qualityType: this.qualityType\r\n      }\r\n      listZjQualityProblemCategoryFirst(params).then((res) => {\r\n        // this.treeData = res.rows;\r\n        if (res.code === 200) {\r\n          const data = res.rows\r\n          data.forEach((item) => {\r\n            this.treeData.push({\r\n              id: item.qualityId,\r\n              label: item.itemName,\r\n              isLeaf: false,\r\n              children: null\r\n            })\r\n          })\r\n          this.treeLoading = false\r\n          // console.log(this.treeData, \"treeData\");\r\n        }\r\n      })\r\n    },\r\n    refreshTree() {\r\n      this.treeData = [] // 清空现有树数据\r\n      this.getHazardHazardCategory() // 重新加载树数据\r\n    },\r\n    loadNode(node, resolve) {\r\n      if (node.level === 0) {\r\n        return resolve([])\r\n      }\r\n\r\n      const currentNode = node.data\r\n      // 若该节点已有 children，说明已请求过，直接解析现有 children\r\n      if (currentNode.children) {\r\n        return resolve(currentNode.children)\r\n      }\r\n      const parentId = currentNode.id\r\n      listZjQualityProblemCategory({ parentId })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            const children = res.rows.map((item) => ({\r\n              id: item.qualityId,\r\n              label: item.itemName,\r\n              // 根据子节点是否存在判断是否为叶子节点\r\n              isLeaf: !item.children || item.children.length === 0\r\n            }))\r\n            // 将子节点数据赋值给当前节点的 children 属性\r\n            currentNode.children = children\r\n            resolve(children)\r\n          } else {\r\n            resolve([])\r\n          }\r\n        })\r\n        .catch(() => {\r\n          resolve([])\r\n        })\r\n    },\r\n    transformChildren(children) {\r\n      console.log(children)\r\n      if (!children || children.length === 0) return []\r\n      return children.map((child) => ({\r\n        label: child.itemName,\r\n        id: child.qualityId,\r\n        children: this.transformChildren(child.children)\r\n      }))\r\n    },\r\n    handleNodeClick(nodeData, node) {\r\n      // console.log(nodeData, node, \"nodeData\");\r\n      // this.queryParams.hazardId = nodeData.id;\r\n      if (node.isLeaf) {\r\n        // 执行筛选操作\r\n        this.selectId = nodeData.id\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    setLabelRef(el, node) {\r\n      if (el) {\r\n        this.labelRefs.set(node.id || node.label, el)\r\n      }\r\n    },\r\n    /** 查询质量问题库列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listZjQualityProblemInfo(this.queryParams).then((res) => {\r\n        this.zjQualityProblemInfoList = res.rows\r\n        this.total = res.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        qualityId: null,\r\n        parentId: null,\r\n        itemName: null,\r\n        qualityCode: null,\r\n        commonProblem: null,\r\n        rectificationRequirements: null,\r\n        problemLevel: '一般问题',\r\n        questionCategory: null,\r\n        qualityStatus: 0,\r\n        status: null,\r\n        rectificationDeadline: null,\r\n        createTime: null,\r\n        createBy: null,\r\n        updateTime: null,\r\n        updateBy: null,\r\n        qualityType: null\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.queryParams.parentId = this.selectId\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.qualityId);\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      // 如果有选中的树节点，则将其设置为新增项的父节点\r\n      if (this.selectId) {\r\n        this.form.parentId = this.selectId\r\n        this.form.qualityType = this.qualityType\r\n      }\r\n      this.open = true\r\n      this.title = '添加质量问题库'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const qualityId = row.qualityId || this.ids\r\n      getZjQualityProblemInfo(qualityId).then((res) => {\r\n        this.form = res.data\r\n        // 确保问题类别字段从表格行数据中获取\r\n        this.form.questionCategory = row.questionCategory\r\n        this.open = true\r\n        this.title = '修改质量问题库'\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs['form'].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.qualityId != null) {\r\n            updateZjQualityProblemInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess('修改成功')\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addZjQualityProblemInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess('新增成功')\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const qualityIds = row && row.qualityId ? row.qualityId : this.ids\r\n      const message = row && row.qualityId\r\n        ? `是否确认删除质量问题库编号为\"${qualityIds}\"的数据项？`\r\n        : `是否确认删除选中的${this.ids.length}条数据项？`\r\n      \r\n      this.$modal\r\n        .confirm(message)\r\n        .then(function () {\r\n          return delZjQualityProblemInfo(qualityIds)\r\n        })\r\n        .then(() => {\r\n          this.getList()\r\n          this.$modal.msgSuccess('删除成功')\r\n        })\r\n        .catch(() => { })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      // 如果有选中记录，导出选中的；否则导出全部\r\n      if (this.ids.length > 0) {\r\n        // 导出选中记录\r\n        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {\r\n          this.download(\r\n            'inspection/zjQualityProblemInfo/export',\r\n            {\r\n              ids: this.ids.join(',')\r\n            },\r\n            `zjQualityProblemInfo_selected_${new Date().getTime()}.xlsx`\r\n          )\r\n        })\r\n      } else {\r\n        // 导出全部记录（根据查询条件，但不包含分页参数）\r\n        const exportParams = { ...this.queryParams }\r\n        // 移除分页参数，确保导出全部数据\r\n        delete exportParams.pageNum\r\n        delete exportParams.pageSize\r\n\r\n        this.download(\r\n          'inspection/zjQualityProblemInfo/export',\r\n          exportParams,\r\n          `zjQualityProblemInfo_${new Date().getTime()}.xlsx`\r\n        )\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n.title {\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n::v-deep .el-tree-node__label {\r\n  display: inline-block;\r\n  max-width: 180px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n::v-deep .el-tree-node.is-current>.el-tree-node__content {\r\n  background-color: #f0f7ff;\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n.two-lines-ellipsis {\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2;\r\n  line-clamp: 2;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  word-break: break-word;\r\n}\r\n</style>\r\n"]}]}