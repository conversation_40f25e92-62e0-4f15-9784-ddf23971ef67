<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="所属分公司" prop="company">
        <selectComponyTree
          ref="chargePersonName"
          v-model="queryParams.company"
          :people-list="companyList"
          placeholder="请搜索或选择所属分公司"
          @change="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-date-picker
          v-model="queryParams.year"
          type="year"
          placeholder="选择年份"
          value-format="yyyy"
          size="small"
          style="width: 120px"
        />
      </el-form-item>
      <el-form-item label="月份" prop="start">
        <el-select
          v-model="queryParams.start"
          placeholder="选择月份"
          size="small"
          style="width: 120px"
        >
          <el-option
            v-for="month in 12"
            :key="month"
            :label="month + '月'"
            :value="month.toString()"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          @click="handlePrint"
          >打印</el-button
        >
      </el-col>
      <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
    </el-row>

    <el-table
      v-loading="loading"
      :data="tableData"
      height="calc(100vh - 230px)"
      border
      stripe
    >
     
      
      <!-- 动态生成多级表头 -->
      <template v-for="group in groupedColumns">
        <el-table-column
          :key="group.name"
          :label="group.name"
          align="center"
          :class-name="group.className"
        >
          <el-table-column
            v-for="subColumn in group.children"
            :key="subColumn.key"
            :prop="subColumn.key"
            :label="subColumn.name"
            :width="subColumn.width || 'auto'"
            :min-width="subColumn.minWidth || 120"
            align="center"
            show-overflow-tooltip
          />
        </el-table-column>
      </template>
    </el-table>
  </div>
</template>

<script>
import { querySafetyEvaluation } from "@/api/report/index";
import selectComponyTree from "@/views/components/selectComponyTree.vue";
import { getEnterpriseInfo } from "@/api/system/info";
// 安全考核
export default {
  name: "SafetyAssesmentReport",
  components: {
    selectComponyTree,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格列配置
      columns: [],
      // 表格数据
      dataList: [],
      companyList: [],
      // 查询参数
      queryParams: {
        year: "2025", // 年份
        start: "6", // 开始月份
        company: "", // 所属分公司
      },
    };
  },
  computed: {
    /** 处理列分组，生成多级表头结构 */
    groupedColumns() {
      if (!this.columns || this.columns.length === 0 || !this.dataList || this.dataList.length === 0) {
        return [];
      }
      
      // 获取二级表头数据（第一行数据作为表头）
      const subHeaders = this.dataList[0];
      
      // 按第一行表头分组
      const grouped = [];
      const processedKeys = new Set();
      
      this.columns.forEach((column, index) => {
        if (processedKeys.has(column.key)) {
          return;
        }
        
        // 查找所有相同名称的列
        const sameNameColumns = this.columns.filter(col => col.name === column.name);
        
        if (sameNameColumns.length > 1) {
          // 有多个相同名称的列，需要分组
          const children = sameNameColumns.map(col => {
            processedKeys.add(col.key);
            
            return {
              key: col.key,
              name: subHeaders[col.key] || col.name, // 使用二级表头数据
              width: col.width,
              minWidth: col.minWidth
            };
          });
          
          grouped.push({
            name: column.name,
            children: children,
            className: 'company-header'
          });
        } else {
          // 只有一个列
          processedKeys.add(column.key);
          grouped.push({
            name: column.name,
            children: [{
              key: column.key,
              name: subHeaders[column.key], 
              width: column.width,
              minWidth: column.minWidth
            }],
            className: 'single-header'
          });
        }
      });
      
      return grouped;
    },
    
    /** 处理数据行，排除第一行（表头数据） */
    tableData() {
      if (!this.dataList || this.dataList.length <= 1) {
        return [];
      }
      
      // 排除第一行（表头数据），从第二行开始是实际数据
      return this.dataList.slice(1).map((item, index) => ({
        ...item,
        index: index + 1
      }));
    }
  },
  created() {
    this.getList();
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      getEnterpriseInfo().then((res) => {
        if (res.code == 200) {
          this.companyList = res.data;
        }
      });
    },

    /** 查询安全费用报表数据 */
    getList() {
      this.loading = true;
      querySafetyEvaluation(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.columns = res.data.columns || [];
            this.dataList = res.data.dataList || [];
            this.total = Math.max(0, this.dataList.length - 1); // 减去表头行
          } else {
            this.$message.error(res.msg || "查询失败");
            this.columns = [];
            this.dataList = [];
            this.total = 0;
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
          this.columns = [];
          this.dataList = [];
          this.total = 0;
        });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        year: "2025",
        start: "1",
        company: "",
      };
      this.getList();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "report/safetyAssesment/export",
        {
          ...this.queryParams,
        },
        `${this.queryParams.year}年${this.queryParams.start}月江苏建设安全考核汇总表(月报).xlsx`
      );
    },
    /** 打印按钮操作 */
    handlePrint() {
      if (this.tableData.length === 0) {
        this.$message.warning("暂无数据可打印");
        return;
      }
      
      // 创建打印内容
      const printContent = this.generatePrintContent();
      
      // 创建新窗口进行打印
      const printWindow = window.open('', '_blank');
      printWindow.document.write(printContent);
      printWindow.document.close();
      
      // 使用setTimeout确保内容完全加载后再打印
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        // 打印对话框关闭后关闭窗口
        printWindow.onafterprint = function() {
          printWindow.close();
        };
        // 如果用户取消打印，也关闭窗口
        setTimeout(() => {
          if (!printWindow.closed) {
            printWindow.close();
          }
        }, 1000);
      }, 100);
    },

    /** 生成打印内容 */
    generatePrintContent() {
      const currentDate = new Date().toLocaleDateString();
      const year = this.queryParams.year;
      const startMonth = this.queryParams.start;
      
      // 生成多级表头
      const printTable = this.generateMultiLevelTable();
      
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>安全评估报表</title>
          <style>
            body {
              font-family: "Microsoft YaHei", Arial, sans-serif;
              margin: 20px;
              font-size: 12px;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .print-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .print-info {
              font-size: 14px;
              margin-bottom: 20px;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
            }
            .print-table th,
            .print-table td {
              border: 1px solid #000;
              padding: 8px;
              text-align: center;
            }
            .print-table th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .print-footer {
              margin-top: 20px;
              text-align: right;
              font-size: 12px;
              color: #666;
            }
            @media print {
              body { margin: 0; }
              .print-header { page-break-after: avoid; }
              .print-table { page-break-inside: auto; }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <div class="print-title">${year}年${startMonth}月江苏建设安全考核汇总表(月报）</div>
           
          </div>
          
          ${printTable}
          
          <div class="print-footer">
            共 ${this.tableData.length} 条记录
          </div>
        </body>
        </html>
      `;
    },

    /** 生成多级表头的HTML表格 */
    generateMultiLevelTable() {
      if (!this.columns || this.columns.length === 0 || !this.dataList || this.dataList.length === 0) {
        return '<p>暂无数据</p>';
      }

      const subHeaders = this.dataList[0]; // 二级表头数据
      const groupedColumns = this.groupedColumns;
      
      // 生成表头HTML
      let headerHtml = '<table class="print-table">';
      
      // 第一行表头（公司名称）
      headerHtml += '<thead><tr>';
      groupedColumns.forEach(group => {
        const colspan = group.children.length;
        headerHtml += `<th colspan="${colspan}" style="background-color: #e0f2fe; color: #01579b; font-weight: bold;">${group.name}</th>`;
      });
      headerHtml += '</tr>';
      
      // 第二行表头（工程名称、项目经理、安全员等）
      headerHtml += '<tr>';
      groupedColumns.forEach(group => {
        group.children.forEach(child => {
          headerHtml += `<th style="background-color: #f8f9fa; color: #495057;">${child.name}</th>`;
        });
      });
      headerHtml += '</tr></thead>';
      
      // 生成数据行
      headerHtml += '<tbody>';
      this.tableData.forEach((row, index) => {
        headerHtml += '<tr>';
        groupedColumns.forEach(group => {
          group.children.forEach(child => {
            const value = row[child.key] || '';
            headerHtml += `<td>${value}</td>`;
          });
        });
        headerHtml += '</tr>';
      });
      headerHtml += '</tbody></table>';
      
      return headerHtml;
    },
  },
};
</script>
<style scoped lang="scss">
.app-container {
  // 多级表头样式
  :deep(.el-table) {
    .company-header {
      background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%) !important;
      color: #01579b !important;
      font-weight: bold !important;
      font-size: 14px !important;
      border-right: 2px solid #0277bd !important;
      
      .cell {
        background: transparent !important;
        color: #01579b !important;
        font-weight: bold !important;
      }
    }
    
    .single-header {
      background: #f8f9fa !important;
      color: #495057 !important;
      font-weight: 600 !important;
      
      .cell {
        background: transparent !important;
        color: #495057 !important;
        font-weight: 600 !important;
      }
    }
    
    // 表头整体样式
    .el-table__header-wrapper {
      .el-table__header {
        th {
          border-bottom: 2px solid #dee2e6;
          
          // 一级表头样式
          &.company-header,
          &.single-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 1px solid #dee2e6;
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 0;
              bottom: 0;
              width: 1px;
              background: #adb5bd;
            }
          }
          
          // 二级表头样式
          .el-table__header-cell {
            background: #ffffff;
            border-right: 1px solid #dee2e6;
            font-weight: 500;
            color: #6c757d;
            
            &:last-child {
              border-right: none;
            }
          }
        }
      }
    }
    
    // 数据行样式
    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f8f9fa;
          }
          
          td {
            border-right: 1px solid #dee2e6;
            transition: background-color 0.2s ease;
            
            &:first-child {
              background-color: #f8f9fa;
              font-weight: 600;
              color: #495057;
            }
          }
        }
      }
    }
  }
}
</style>
