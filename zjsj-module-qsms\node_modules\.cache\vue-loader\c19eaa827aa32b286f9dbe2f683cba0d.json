{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue?vue&type=style&index=1&id=9b357cd0&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue", "mtime": 1757423388005}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQovKiDlhajlsYDmoLflvI8gLSDkuI3lj5dzY29wZWTpmZDliLbvvIznoa7kv53og73lpJ/opobnm5ZFbGVtZW50IFVJ55qE6buY6K6k5qC35byPICovDQoucGVvcGxlLXRyZWUtZHJvcGRvd24uZWwtc2VsZWN0LWRyb3Bkb3duIHsNCiAgbWF4LXdpZHRoOiA2NTBweCAhaW1wb3J0YW50Ow0KICBtaW4td2lkdGg6IDY1MHB4ICFpbXBvcnRhbnQ7DQogIHdpZHRoOiBhdXRvICFpbXBvcnRhbnQ7DQp9DQoNCi5wZW9wbGUtdHJlZS1kcm9wZG93biAuZWwtc2VsZWN0LWRyb3Bkb3duX19pdGVtIHsNCiAgcGFkZGluZzogMDsNCiAgaGVpZ2h0OiBhdXRvOw0KfQ0K"}, {"version": 3, "sources": ["selectPeopleTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAitBA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "selectPeopleTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <el-select\r\n    ref=\"peopleSelectRef\"\r\n    :value=\"value\"\r\n    :placeholder=\"placeholder\"\r\n    :disabled=\"disabled\"\r\n    :visible=\"selectVisible\"\r\n    :filter-method=\"filterPeople\"\r\n    clearable\r\n    filterable\r\n    style=\"width: 100%\"\r\n    class=\"people-tree-select\"\r\n    popper-class=\"people-tree-dropdown\"\r\n    @input=\"handleInput\"\r\n    @change=\"handleSelectChange\"\r\n    @visible-change=\"handleVisibleChange\"\r\n    @focus=\"handleFocus\"\r\n    @clear=\"handleClear\"\r\n  >\r\n    <el-option :value=\"value\" style=\"height: auto; padding: 0\">\r\n      <el-tree\r\n        ref=\"peopleTree\"\r\n        :data=\"treeData\"\r\n        :props=\"treeProps\"\r\n        :filter-node-method=\"filterNode\"\r\n        :expand-on-click-node=\"false\"\r\n        :class=\"['people-tree', { searching: searchText && searchText.trim() }]\"\r\n        node-key=\"id\"\r\n        highlight-current\r\n        style=\"padding: 5px 0\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <i v-if=\"data.children && data.children.length\" />\r\n          <span\r\n            :class=\"['tree-label', { department: data.type === '1' }]\"\r\n            v-html=\"highlightSearchText(node.label, searchText)\"\r\n          />\r\n        </span>\r\n      </el-tree>\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: [String, Number],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请选择\",\r\n    },\r\n    peopleList: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 新增：选择模式，支持 'people'（人员选择）和 'category'（类别选择）\r\n    selectMode: {\r\n      type: String,\r\n      default: \"people\",\r\n      validator: (value) => [\"people\", \"category\"].includes(value),\r\n    },\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      searchText: \"\",\r\n      selectVisible: false,\r\n      searchTimer: null,\r\n      listenerBound: false, // 防止重复绑定监听器\r\n      inputHandler: null, // 存储输入处理函数引用\r\n      keyupHandler: null, // 存储按键处理函数引用\r\n      compositionHandler: null, // 存储组合输入处理函数引用\r\n\r\n      treeProps: {\r\n        label: \"label\",\r\n        children: \"children\",\r\n      },\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 转换为树形结构数据\r\n    treeData() {\r\n      return this.processTreeData(this.peopleList);\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    searchText: {\r\n      immediate: true,\r\n      handler(val) {\r\n        // 使用 $nextTick 确保组件已完全渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.peopleTree) {\r\n            this.$refs.peopleTree.filter(val);\r\n\r\n            // 搜索时自动展开匹配的父节点（无论是否首次）\r\n            if (val && val.trim()) {\r\n              // 使用短延迟确保过滤完成\r\n              setTimeout(() => {\r\n                this.expandMatchedNodes(val.trim());\r\n              }, 150);\r\n            } else {\r\n              // 清空搜索时收起所有节点\r\n              this.collapseAllNodes();\r\n            }\r\n          } else {\r\n            // 如果树组件还没准备好，短暂延迟后重试\r\n            setTimeout(() => {\r\n              if (this.$refs.peopleTree && val === this.searchText) {\r\n                this.$refs.peopleTree.filter(val);\r\n                if (val && val.trim()) {\r\n                  setTimeout(() => {\r\n                    this.expandMatchedNodes(val.trim());\r\n                  }, 100);\r\n                }\r\n              }\r\n            }, 200);\r\n          }\r\n        });\r\n      },\r\n    },\r\n\r\n    peopleList: {\r\n      handler(newVal) {\r\n        // 树数据更新后重新过滤\r\n        this.$nextTick(() => {\r\n          if (this.searchText) {\r\n            this.$refs.peopleTree.filter(this.searchText);\r\n          }\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n\r\n  mounted() {\r\n    // 组件挂载后的初始化\r\n    this.$nextTick(() => {\r\n      this.setupInputListener();\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.searchTimer) {\r\n      clearTimeout(this.searchTimer);\r\n    }\r\n\r\n    // 清理事件监听器\r\n    if (this.inputHandler || this.keyupHandler || this.compositionHandler) {\r\n      const selectEl = this.$refs.peopleSelectRef;\r\n      if (selectEl && selectEl.$el) {\r\n        const input = selectEl.$el.querySelector(\"input\");\r\n        if (input) {\r\n          input.removeEventListener(\"input\", this.inputHandler);\r\n          input.removeEventListener(\"keyup\", this.keyupHandler);\r\n          input.removeEventListener(\"compositionend\", this.compositionHandler);\r\n        }\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 设置输入监听器\r\n    setupInputListener() {\r\n      // 如果已经绑定过，跳过\r\n      if (this.listenerBound) {\r\n        return;\r\n      }\r\n\r\n      // 减少延迟，提高响应速度\r\n      setTimeout(() => {\r\n        const selectEl = this.$refs.peopleSelectRef;\r\n\r\n        if (selectEl) {\r\n          // 更详细的输入元素检测\r\n          let actualInput = null;\r\n\r\n          // 方式1: 通过选择器查找\r\n          const inputBySelector = selectEl.$el\r\n            ? selectEl.$el.querySelector(\"input\")\r\n            : null;\r\n          if (inputBySelector) {\r\n            actualInput = inputBySelector;\r\n          }\r\n\r\n          // 方式2: 通过 $refs.input\r\n          else if (selectEl.$refs && selectEl.$refs.input) {\r\n            if (selectEl.$refs.input.$el) {\r\n              if (selectEl.$refs.input.$el.tagName === \"INPUT\") {\r\n                actualInput = selectEl.$refs.input.$el;\r\n              } else {\r\n                const nestedInput =\r\n                  selectEl.$refs.input.$el.querySelector(\"input\");\r\n                if (nestedInput) {\r\n                  actualInput = nestedInput;\r\n                }\r\n              }\r\n            } else if (selectEl.$refs.input.tagName === \"INPUT\") {\r\n              actualInput = selectEl.$refs.input;\r\n            }\r\n          }\r\n\r\n          if (actualInput && actualInput.tagName === \"INPUT\") {\r\n            // 移除可能存在的旧监听器\r\n            actualInput.removeEventListener(\"input\", this.inputHandler);\r\n            actualInput.removeEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.removeEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            // 创建绑定的处理函数\r\n            this.inputHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.keyupHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.compositionHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            // 添加事件监听器\r\n            actualInput.addEventListener(\"input\", this.inputHandler);\r\n            actualInput.addEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.addEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            this.listenerBound = true;\r\n          } else {\r\n            // 如果找不到输入元素，稍后重试\r\n            setTimeout(() => {\r\n              this.listenerBound = false;\r\n              this.setupInputListener();\r\n            }, 300);\r\n          }\r\n        } else {\r\n          setTimeout(() => {\r\n            this.setupInputListener();\r\n          }, 300);\r\n        }\r\n      }, 200); // 增加延迟确保DOM完全渲染\r\n    },\r\n\r\n    handleVisibleChange(isVisible) {\r\n      if (isVisible) {\r\n        // 下拉框打开时，强制重新设置监听器\r\n        this.listenerBound = false;\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n\r\n        // 如果有现有搜索文本，立即触发过滤和展开\r\n        if (this.searchText) {\r\n          this.$nextTick(() => {\r\n            this.filterPeople(this.searchText);\r\n          });\r\n        }\r\n      } else {\r\n        // 关闭下拉框时清空搜索\r\n        this.searchText = \"\";\r\n        if (this.$refs.peopleTree) {\r\n          this.$refs.peopleTree.filter(\"\");\r\n          this.collapseAllNodes();\r\n        }\r\n      }\r\n    },\r\n\r\n    handleFocus() {\r\n      // 聚焦时确保监听器已设置\r\n      if (!this.listenerBound) {\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n      }\r\n    },\r\n\r\n    handleClear() {\r\n      this.searchText = \"\";\r\n      this.filterPeople(\"\");\r\n    },\r\n    closeDropdown() {\r\n      this.$refs.peopleSelectRef.blur();\r\n    },\r\n\r\n    // 处理树形数据\r\n    // processTreeData(data) {\r\n    //   return data.map((item) => ({\r\n    //     ...item,\r\n    //     label: item.label ? item.label.trim() : \"\",\r\n    //     children: item.children ? this.processTreeData(item.children) : [],\r\n    //   }));\r\n    // },\r\n    processTreeData(data, parent = null) {\r\n      return data.map((item) => {\r\n        // 构建当前节点，继承原有属性\r\n        const currentNode = {\r\n          ...item,\r\n          label: item.label ? item.label.trim() : \"\",\r\n          parentId: parent?.id ?? null,\r\n          parentName: parent?.label ?? null,\r\n          // 递归处理子节点，并将当前节点作为父节点传递\r\n          children: item.children\r\n            ? this.processTreeData(item.children, item)\r\n            : [],\r\n        };\r\n        // console.log(\"currentNode\", currentNode);\r\n        return currentNode;\r\n      });\r\n    },\r\n\r\n    // 节点过滤方法 - 增强的模糊搜索\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n\r\n      const searchValue = value.toLowerCase().trim();\r\n      const label = data.label ? data.label.toLowerCase() : \"\";\r\n      const name = data.name ? data.name.toLowerCase() : \"\";\r\n\r\n      // 1. 精确匹配\r\n      if (label.includes(searchValue) || name.includes(searchValue)) {\r\n        return true;\r\n      }\r\n\r\n      // 2. 拼音首字母匹配\r\n      if (\r\n        this.matchPinyinInitials(label, searchValue) ||\r\n        this.matchPinyinInitials(name, searchValue)\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 3. 分词匹配 - 支持空格分隔的多个关键词\r\n      const keywords = searchValue.split(/\\s+/).filter((k) => k.length > 0);\r\n      if (keywords.length > 1) {\r\n        return keywords.every(\r\n          (keyword) =>\r\n            label.includes(keyword) ||\r\n            name.includes(keyword) ||\r\n            this.matchPinyinInitials(label, keyword) ||\r\n            this.matchPinyinInitials(name, keyword)\r\n        );\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 节点点击事件\r\n    handleNodeClick(data, node) {\r\n      console.log(\"点击当前节点\", data, node);\r\n\r\n      // 类别选择模式：所有叶子节点都可以选择\r\n      if (this.selectMode === \"category\") {\r\n        // 如果有子节点，切换展开状态\r\n        if (data.children && data.children.length > 0) {\r\n          node.expanded = !node.expanded;\r\n          return;\r\n        }\r\n\r\n        // 叶子节点，触发选择\r\n        this.handleSelectChange(data.id);\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      // 如果是 general-project 类型，可以选择\r\n      if (data.type === \"general-project\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 如果是其他类型且有子节点，切换展开状态\r\n      if (data.children && data.children.length) {\r\n        node.expanded = !node.expanded;\r\n        if (data.type != null) {\r\n          return;\r\n        }\r\n        // return;\r\n      }\r\n\r\n      // 如果是人员节点（type !== '1' 且不是 general-project），触发选择\r\n      if (data.type !== \"1\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n      }\r\n\r\n      // 其他情况（没有子节点的节点）不做任何操作\r\n    },\r\n\r\n    handleInput(value) {\r\n      this.$emit(\"input\", value);\r\n      // 主动调用 filterPeople 确保搜索生效\r\n      this.filterPeople(value);\r\n    },\r\n\r\n    handleSelectChange(selectedId) {\r\n      if (!selectedId) {\r\n        this.$emit(\"change\", null);\r\n        return;\r\n      }\r\n\r\n      const selectedItem = this.findNodeById(this.treeData, selectedId);\r\n      console.log(\"选择的信息\", selectedItem);\r\n\r\n      // 类别选择模式：直接返回选中的项目\r\n      if (this.selectMode === \"category\") {\r\n        if (selectedItem) {\r\n          const cleanItem = {\r\n            id: selectedItem.id,\r\n            label: selectedItem.label || \"\",\r\n            name: selectedItem.name || \"\",\r\n            type: selectedItem.type,\r\n          };\r\n          this.$emit(\"change\", cleanItem);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      if (selectedItem && selectedItem.type !== \"1\") {\r\n        const cleanItem = {\r\n          id: selectedItem.id,\r\n          label: selectedItem.label || \"\",\r\n          name: selectedItem.name || \"\",\r\n          type: selectedItem.type,\r\n        };\r\n\r\n        this.$emit(\"change\", cleanItem);\r\n      }\r\n    },\r\n\r\n    // 根据ID查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) return node;\r\n        if (node.children && node.children.length) {\r\n          const found = this.findNodeById(node.children, id);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 拼音首字母匹配方法\r\n    matchPinyinInitials(text, searchValue) {\r\n      if (!text || !searchValue) return false;\r\n\r\n      // 简单的中文拼音首字母映射\r\n      const pinyinMap = {\r\n        a: \"阿啊\",\r\n        b: \"不把白本部被北备比表必标\",\r\n        c: \"从程成产出常场长城创传\",\r\n        d: \"的大都到道地点电当得对多段\",\r\n        e: \"二\",\r\n        f: \"发方法分风服费发放防发\",\r\n        g: \"工个过公管国广高改工构\",\r\n        h: \"和好后会化回还海合黄行\",\r\n        i: \"一\",\r\n        j: \"就进建交机计经检基级见技结\",\r\n        k: \"可看科开口控课快客\",\r\n        l: \"了来理里立流料量联力领路类\",\r\n        m: \"没么面民明名目模美门\",\r\n        n: \"年能内南那女你农\",\r\n        o: \"哦\",\r\n        p: \"平配品排培破片跑\",\r\n        q: \"去其全清情期前起请求区\",\r\n        r: \"人如让日然认入任\",\r\n        s: \"是时实施所说三设生手市上四十\",\r\n        t: \"他通同体统头条特提图天\",\r\n        u: \"有用于\",\r\n        v: \"\",\r\n        w: \"我为文物位问外王万五网维\",\r\n        x: \"现系性新学小心选许信下项行西\",\r\n        y: \"一要用有以业已应意音元月研运\",\r\n        z: \"中在这者主专注资制知至重组\",\r\n      };\r\n\r\n      // 尝试拼音首字母匹配\r\n      for (let i = 0; i < searchValue.length; i++) {\r\n        const char = searchValue[i];\r\n        const pinyinChars = pinyinMap[char];\r\n        if (pinyinChars && i < text.length) {\r\n          if (!pinyinChars.includes(text[i])) {\r\n            return false;\r\n          }\r\n        } else if (char !== text[i]) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return searchValue.length > 0 && searchValue.length <= text.length;\r\n    },\r\n\r\n    // 即时搜索方法 - 移除防抖，确保立即响应\r\n    filterPeople(value) {\r\n      // 立即更新搜索文本并触发过滤\r\n      this.searchText = value;\r\n\r\n      // 立即触发树过滤\r\n      if (this.$refs.peopleTree) {\r\n        this.$refs.peopleTree.filter(value);\r\n\r\n        // 如果有搜索值，立即展开匹配的节点\r\n        if (value && value.trim()) {\r\n          // 使用 setTimeout 确保过滤完成后再展开\r\n          setTimeout(() => {\r\n            this.expandMatchedNodes(value.trim());\r\n          }, 50);\r\n        }\r\n      }\r\n\r\n      // 对于 filter-method，我们总是返回 true，让所有选项都显示\r\n      // 因为我们的过滤逻辑是在树组件内部处理的\r\n      return true;\r\n    },\r\n\r\n    // 增强expandMatchedNodes方法，确保节点正确展开\r\n    expandMatchedNodes(searchValue) {\r\n      if (!this.$refs.peopleTree || !searchValue) return;\r\n\r\n      const expandedKeys = [];\r\n      this.collectExpandedNodes(this.treeData, searchValue, expandedKeys);\r\n\r\n      // 去重并确保展开逻辑生效\r\n      const uniqueKeys = [...new Set(expandedKeys)];\r\n\r\n      // 立即展开节点，不使用 nextTick 延迟\r\n      uniqueKeys.forEach((key) => {\r\n        const node = this.$refs.peopleTree.store.nodesMap[key];\r\n        if (node && !node.expanded) {\r\n          node.expand();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 收起所有节点的方法\r\n    collapseAllNodes() {\r\n      if (!this.$refs.peopleTree) return;\r\n\r\n      const allNodes = this.$refs.peopleTree.store.nodesMap;\r\n      Object.values(allNodes).forEach((node) => {\r\n        if (node.expanded && node.childNodes && node.childNodes.length > 0) {\r\n          node.collapse();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 递归收集需要展开的节点\r\n    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {\r\n      let hasMatchedChild = false;\r\n\r\n      for (const node of nodes) {\r\n        // 检查当前节点是否匹配\r\n        if (this.filterNode(searchValue, node)) {\r\n          hasMatchedChild = true;\r\n\r\n          // 如果有父节点，添加到展开列表\r\n          if (parentKey) {\r\n            expandedKeys.push(parentKey);\r\n          }\r\n        }\r\n\r\n        // 递归检查子节点\r\n        if (node.children && node.children.length > 0) {\r\n          const childMatched = this.collectExpandedNodes(\r\n            node.children,\r\n            searchValue,\r\n            expandedKeys,\r\n            node.id\r\n          );\r\n\r\n          if (childMatched) {\r\n            hasMatchedChild = true;\r\n            // 如果子节点有匹配，当前节点也需要展开\r\n            if (node.id) {\r\n              expandedKeys.push(node.id);\r\n            }\r\n            // 如果有父节点，父节点也需要展开\r\n            if (parentKey) {\r\n              expandedKeys.push(parentKey);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      return hasMatchedChild;\r\n    },\r\n\r\n    // 高亮搜索文本\r\n    highlightSearchText(text, searchValue) {\r\n      if (!text) return \"\";\r\n      if (!searchValue || !searchValue.trim()) return text;\r\n\r\n      const searchText = searchValue.trim();\r\n      // 防止XSS攻击，转义HTML特殊字符\r\n      const escapedText = text.replace(/[&<>\"']/g, function (match) {\r\n        const escapeMap = {\r\n          \"&\": \"&amp;\",\r\n          \"<\": \"&lt;\",\r\n          \">\": \"&gt;\",\r\n          '\"': \"&quot;\",\r\n          \"'\": \"&#x27;\",\r\n        };\r\n        return escapeMap[match];\r\n      });\r\n\r\n      // 如果搜索文本包含在显示文本中，高亮显示\r\n      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {\r\n        const regex = new RegExp(\r\n          `(${searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`,\r\n          \"gi\"\r\n        );\r\n        return escapedText.replace(\r\n          regex,\r\n          '<span class=\"search-highlight\">$1</span>'\r\n        );\r\n      }\r\n\r\n      return escapedText;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.custom-tree-node {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.tree-icon {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.tree-label {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 调整树组件样式 */\r\n:deep(.people-tree) {\r\n  min-width: 180px;\r\n  width: auto;\r\n}\r\n\r\n/* 控制下拉框宽度 - 使用特定class确保只影响这个组件 */\r\n:deep(.people-tree-dropdown) {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n/* 全局样式 - 更高优先级 */\r\n::v-deep .people-tree-dropdown.el-select-dropdown {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n:deep(.el-tree-node__content) {\r\n  height: 32px;\r\n}\r\n\r\n:deep(\r\n    .el-tree--highlight-current\r\n      .el-tree-node.is-current\r\n      > .el-tree-node__content\r\n  ) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n:deep(.el-select-dropdown__item) {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n\r\n/* 搜索高亮样式 */\r\n:deep(.search-highlight) {\r\n  background-color: #fffacd;\r\n  color: #d32f2f;\r\n  font-weight: bold;\r\n  padding: 1px 2px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */\r\n.people-tree-dropdown.el-select-dropdown {\r\n  max-width: 650px !important;\r\n  min-width: 650px !important;\r\n  width: auto !important;\r\n}\r\n\r\n.people-tree-dropdown .el-select-dropdown__item {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n</style>\r\n"]}]}