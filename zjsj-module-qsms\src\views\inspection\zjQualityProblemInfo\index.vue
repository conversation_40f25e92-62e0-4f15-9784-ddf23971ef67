<template>
  <div class="app-container">
    <el-row>
      <!-- <el-col :span="3"> -->
        <!-- 左侧树结构 -->
        <!-- <div class="title mb-2" @click="goToHazardCategory">质量问题类别</div> -->
        <!-- 搜索框 -->
        <!-- <el-input
          v-model="searchText"
          placeholder="请输入搜索内容"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <!-- 下拉框 选择质量问题类别 -->
        <!-- <el-select v-model="questionCategory" placeholder="请选择质量问题类别" clearable @change="getHazardHazardCategory">
          <el-option v-for="item in questionCategoryList" :key="item.id" :label="item.label" :value="item.label" />
        </el-select>

        <el-tree v-loading="treeLoading" :data="treeData" :props="defaultProps" :load="loadNode" lazy
          @node-click="handleNodeClick">
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :content="data.label" placement="top">
              <span :ref="(el) => setLabelRef(el, node)" class="el-tree-node__label">
                {{ node.label }}
              </span>
            </el-tooltip>
          </template>
        </el-tree> -->
      <!-- </el-col> -->
      <el-col :span="24">
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true"
          label-width="68px">
          <!-- <el-form-item label="父类" prop="parentId">
            <el-input
              v-model="queryParams.parentId"
              placeholder="请输入父类"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <!-- <el-form-item label="分部分项工程" prop="itemName">
            <el-input
              v-model="queryParams.itemName"
              placeholder="请输入分部分项工程"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="序号" prop="qualityCode">
            <el-input
              v-model="queryParams.qualityCode"
              placeholder="请输入序号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item label="问题类别" prop="questionCategory">
            <el-input v-model="queryParams.questionCategory" placeholder="请输入问题类别" clearable
              @keyup.enter.native="handleQuery" style="width: 180px" />
          </el-form-item>
          <el-form-item label="问题描述" prop="commonProblem">
            <el-input v-model="queryParams.commonProblem" placeholder="请输入问题描述" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <!-- <el-form-item label="整改要求" prop="rectificationRequirements">
            <el-input
              v-model="queryParams.rectificationRequirements"
              placeholder="请输入整改要求"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="问题级别" prop="problemLevel">
            <el-input
              v-model="queryParams.problemLevel"
              placeholder="请输入问题级别"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="整改时限(天)" prop="rectificationDeadline">
            <el-input
              v-model="queryParams.rectificationDeadline"
              placeholder="请输入整改时限(天)"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:add']" type="primary" plain icon="el-icon-plus"
              size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handleCommand">
              <el-button v-hasPermi="['inspection:hazard:edit']" type="success" plain icon="el-icon-edit"
                size="mini">修改状态<i class="el-icon-arrow-down el-icon--right" /></el-button>
              <template #dropdown>
                <el-dropdown-menu style="width: 100px; text-align: center">
                  <!-- 下拉选项，可根据实际需求修改 -->
                  <el-dropdown-item command="enable">启用</el-dropdown-item>
                  <el-dropdown-item command="disable">禁用</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:remove']" type="danger" plain icon="el-icon-delete"
              size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['inspection:zjQualityProblemInfo:export']" type="warning" plain
              icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table v-loading="loading" :data="zjQualityProblemInfoList" height="calc(100vh - 250px)"
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="主键" align="center" prop="qualityId" />
          <el-table-column label="父类" align="center" prop="parentId" /> -->
          <!-- <el-table-column
            label="分部分项工程"
            align="center"
            prop="itemName"
          />
          <el-table-column label="序号" align="center" prop="qualityCode" /> -->
          <el-table-column label="问题类别" align="center" prop="questionCategory" width="200" show-overflow-tooltip />
          <el-table-column label="问题描述" align="center" prop="commonProblem" width="240" show-overflow-tooltip>
            <template slot-scope="{ row }">
              <div class="two-lines-ellipsis">
                {{ row.commonProblem }}
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="整改要求"
            align="center"
            prop="rectificationRequirements"
          /> -->
          <el-table-column label="整改时限(天)" align="center" prop="rectificationDeadline" show-overflow-tooltip />
          <el-table-column label="问题等级" align="center" prop="problemLevel">
            <template slot-scope="scope">
              <span v-if="scope.row.problemLevel === '严重问题'">
                <el-tag type="danger">{{ scope.row.problemLevel }}</el-tag>
              </span>
              <span v-else>
                <el-tag type="success">{{ scope.row.problemLevel }}</el-tag>
              </span>
            </template>
          </el-table-column>
          <!-- 新增 关联规范 -->
          <el-table-column label="关联规范" align="center" prop="relatedGuidelines" show-overflow-tooltip />
          <el-table-column label="状态" align="center" prop="qualityStatus">
            <template slot-scope="scope">
              <span v-if="scope.row.qualityStatus === '0'">
                <el-tag type="success">启用</el-tag>
              </span>
              <span v-else-if="scope.row.qualityStatus === '1'">
                <el-tag type="danger">禁用</el-tag>
              </span>
              <span v-else>
                <el-tag type="info">-</el-tag>
              </span>
            </template>
          </el-table-column>

          <!-- <el-table-column label="质量类别" align="center" prop="questionCategory" /> -->
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template slot-scope="scope">
              <el-button v-hasPermi="['inspection:zjQualityProblemInfo:edit']" size="mini" type="text"
                icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
              <el-button v-hasPermi="['inspection:zjQualityProblemInfo:remove']" size="mini" type="text"
                icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改质量问题库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <!-- <el-form-item label="父类" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父类" />
        </el-form-item>
        <el-form-item label="分部分项工程" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入分部分项工程" />
        </el-form-item>
        <el-form-item label="序号" prop="qualityCode">
          <el-input v-model="form.qualityCode" placeholder="请输入序号" />
        </el-form-item> -->
        <el-form-item label="问题类别" prop="questionCategoryPath">
          <el-cascader
            v-model="form.questionCategoryPath"
            :options="categoryTreeOptions"
            :props="cascaderProps"
            placeholder="请选择工程类型 > 问题大类 > 具体问题"
            style="width: 100%"
            clearable
            @change="handleCategoryChange"
            show-all-levels
            separator=" > "
          />
        </el-form-item>
        <el-form-item label="问题等级" prop="problemLevel">
          <!-- <el-input v-model="form.problemLevel" placeholder="请输入问题级别" /> -->
          <!-- 严重问题 一般问题 -->
          <el-select v-model="form.problemLevel" placeholder="请选择问题等级" style="width: 100%">
            <el-option label="严重问题" value="严重问题" />
            <el-option label="一般问题" value="一般问题" />
          </el-select>
        </el-form-item>
        <el-form-item label="问题描述" prop="commonProblem">
          <el-input v-model="form.commonProblem" placeholder="请输入常见问题" type="textarea" rows="5" />
        </el-form-item>

        <!-- <el-form-item label="整改要求" prop="rectificationRequirements">
          <el-input
            v-model="form.rectificationRequirements"
            placeholder="请输入整改要求"
            type="textarea"
            rows="5"
          />
        </el-form-item> -->

        <el-form-item label="整改时限(天)" prop="rectificationDeadline">
          <el-input v-model="form.rectificationDeadline" type="number" min="0" style="width: 100%"
            placeholder="请输入整改时限(天)" />
        </el-form-item>
        <el-form-item label="关联规范" prop="relatedGuidelines">
          <el-input v-model="form.relatedGuidelines" placeholder="请输入关联规范" />
        </el-form-item>
        <el-form-item label="状态" prop="qualityStatus">
          <el-select v-model="form.qualityStatus" placeholder="请选择状态" style="width: 100%">
            <el-option label="启用" :value="0" />
            <el-option label="禁用" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listZjQualityProblemInfo,
  getZjQualityProblemInfo,
  delZjQualityProblemInfo,
  addZjQualityProblemInfo,
  updateZjQualityProblemInfo,
  listZjQualityProblemCategory,
  listZjQualityProblemCategoryFirst
} from '@/api/inspection/zjQualityProblemInfo'

export default {
  name: 'ZjQualityProblemInfo',
  data() {
    return {
      questionCategory: '房建',
      questionCategoryList: [
        { id: 1, label: '房建' },
        { id: 2, label: '市政' },
        { id: 3, label: '公路' },
        { id: 4, label: '铁路' },
        { id: 5, label: '水利' },
        { id: 6, label: '桥梁' },
        { id: 7, label: '隧道' },
        { id: 8, label: '地铁' },
        { id: 9, label: '港口航道' },
        { id: 10, label: '通用' }
      ],
      // 级联选择器配置
      cascaderProps: {
        value: 'qualityId',
        label: 'itemName',
        children: 'children',
        checkStrictly: false, // 只能选择叶子节点
        emitPath: true // 返回完整路径
      },
      // 类别树形选项
      categoryTreeOptions: [],
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 质量问题库表格数据
      zjQualityProblemInfoList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        itemName: null,
        qualityCode: null,
        commonProblem: null,
        rectificationRequirements: null,
        problemLevel: null,
        status: null,
        rectificationDeadline: null,
        questionCategory: null
      },
      // 表单参数
      form: {
        problemLevel: '一般问题',
        questionCategory: null,
        qualityStatus: 0,
        parentId: 0,
        questionCategoryPath: [] // 级联选择器的值
      },
      // 表单校验
      rules: {
        questionCategoryPath: [
          { required: true, message: '问题类别不能为空', trigger: 'change' }
        ],
        problemLevel: [
          { required: true, message: '问题等级不能为空', trigger: 'change' }
        ],
        commonProblem: [
          { required: true, message: '问题描述不能为空', trigger: 'blur' }
        ],
        rectificationDeadline: [
          { required: true, message: '整改时限(天)不能为空', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '整改时限必须为正整数', trigger: 'blur' }
        ],
        qualityStatus: [
          { required: true, message: '状态不能为空', trigger: 'change' }
        ]
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf'
      },
      selectId: null
    }
  },
  created() {
    this.getHazardHazardCategory()
    this.getCategoryTreeOptions() // 获取完整的类别树
    this.getList() // 页面初始化时默认查询数据
  },
  methods: {
    handleCommand(command) {
      if (this.ids.length <= 0) {
        this.$message({
          message: '请选择质量问题条目',
          type: 'warning'
        })
        return
      }
      // console.log(this.ids, "this.ids");
      const status = command === 'enable' ? 0 : 1
      const promises = this.ids.map((item) => {
        return updateZjQualityProblemInfo({
          qualityId: item.qualityId,
          qualityStatus: status
        })
      })
      Promise.allSettled(promises).then((res) => {
        const successCount = res.filter(
          (item) => item.status === 'fulfilled'
        ).length
        const failedResults = res.filter((item) => item.status === 'rejected')
        if (successCount > 0) {
          this.$message({
            message: `${command === 'enable' ? '启用' : '禁用'}成功`,
            type: 'success'
          })
          this.handleQuery()
        } else {
          const errorMessages = failedResults
            .map((result, index) => {
              const id = this.ids[index].serialNumber
              const errorMsg = `${command === 'enable' ? '启用' : '禁用'}失败`
              return `序号为 ${id} 的质量问题条目：${errorMsg}`
            })
            .join('\n')

          this.$message({
            message: `${errorMessages}`,
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      })
    },
    // 级联选择器变化处理
    handleCategoryChange(value) {
      if (value && value.length === 3) {
        // 三级结构：[工程类型, 问题大类, 具体问题类别]
        const questionCategory = value[0] // 第一级：工程类型（房建、市政等）
        const parentId = value[1] // 第二级：问题大类ID（作为parentId）
        const selectedId = value[2] // 第三级：具体问题类别ID
        
        // 查找选中的节点信息
        const selectedNode = this.findNodeById(this.categoryTreeOptions, selectedId)
        if (selectedNode) {
          this.form.questionCategory = selectedNode.itemName
          this.form.parentId = parentId
          this.form.questionCategory = questionCategory
        }
      } else {
        this.form.questionCategory = null
        this.form.parentId = 0
      }
    },
    // 根据ID查找节点
    findNodeById(nodes, targetId) {
      for (const node of nodes) {
        if (node.qualityId === targetId) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, targetId)
          if (found) return found
        }
      }
      return null
    },
    // 获取完整的类别树选项
    getCategoryTreeOptions() {
      // 构建三级结构：工程类型 -> 问题大类 -> 具体问题类别
      this.categoryTreeOptions = this.questionCategoryList.map(typeItem => ({
        qualityId: typeItem.label, // 使用标签作为ID
        itemName: typeItem.label,
        children: []
      }))
      
      // 为每个工程类型加载对应的问题分类
      this.loadAllTypeChildren()
    },
    // 加载所有工程类型的子级分类
    async loadAllTypeChildren() {
      for (const typeItem of this.categoryTreeOptions) {
        await this.loadTypeChildren(typeItem)
      }
    },
    // 加载每个工程类型下的问题分类（两级）
    async loadTypeChildren(typeNode) {
      try {
        // 获取该工程类型下的一级分类
        const params = {
          parentId: '0',
          questionCategory: typeNode.itemName
        }
        const res = await listZjQualityProblemCategoryFirst(params)
        if (res.code === 200 && res.rows.length > 0) {
          typeNode.children = []
          for (const firstLevel of res.rows) {
            const firstLevelNode = {
              qualityId: firstLevel.qualityId,
              itemName: firstLevel.itemName,
              children: []
            }
            
            // 加载二级分类
            await this.loadSecondLevel(firstLevelNode)
            typeNode.children.push(firstLevelNode)
          }
        }
      } catch (error) {
        console.error('加载工程类型子级失败:', error)
      }
    },
    // 加载二级分类（具体的问题类别）
    async loadSecondLevel(firstLevelNode) {
      try {
        const res = await listZjQualityProblemCategory({ parentId: firstLevelNode.qualityId })
        if (res.code === 200 && res.rows.length > 0) {
          firstLevelNode.children = res.rows.map(item => ({
            qualityId: item.qualityId,
            itemName: item.itemName
            // 第三级，不再有children
          }))
        }
      } catch (error) {
        console.error('加载二级分类失败:', error)
      }
    },
    // 构建级联选择器的路径（用于修改时回显）
    async buildCategoryPath() {
      if (this.form.parentId && this.form.parentId !== 0) {
        // 通过 parentId 反向查找完整路径
        const pathInfo = await this.findCategoryPathByParentId(this.form.parentId, this.form.qualityId)
        if (pathInfo) {
          this.form.questionCategoryPath = pathInfo.path
          // 同时更新 questionCategory 字段为工程类型
          this.form.questionCategory = pathInfo.engineeringType
        }
      } else {
        this.form.questionCategoryPath = []
      }
    },
    
    // 通过 parentId 和 qualityId 反向查找完整的级联路径
    async findCategoryPathByParentId(parentId, qualityId) {
      try {
        // 遍历所有工程类型，查找匹配的路径
        for (const typeNode of this.categoryTreeOptions) {
          if (typeNode.children && typeNode.children.length > 0) {
            // 在当前工程类型下查找 parentId 对应的一级分类
            const firstLevelNode = typeNode.children.find(child => child.qualityId === parentId)
            if (firstLevelNode) {
              // 确保二级分类已加载
              if (!firstLevelNode.children || firstLevelNode.children.length === 0) {
                await this.loadSecondLevel(firstLevelNode)
              }
              // 在二级分类中查找当前 qualityId
              if (firstLevelNode.children) {
                const secondLevelNode = firstLevelNode.children.find(child => child.qualityId === qualityId)
                if (secondLevelNode) {
                  return {
                    path: [typeNode.itemName, parentId, qualityId],
                    engineeringType: typeNode.itemName,
                    firstLevelId: parentId,
                    secondLevelId: qualityId
                  }
                }
              }
            }
          }
        }
        return null
      } catch (error) {
        console.error('查找级联路径失败:', error)
        return null
      }
    },
    getHazardHazardCategory() {
      this.treeData = []
      this.treeLoading = true
      const params = {
        parentId: '0',
        questionCategory: this.questionCategory
      }
      listZjQualityProblemCategoryFirst(params).then((res) => {
        // this.treeData = res.rows;
        if (res.code === 200) {
          const data = res.rows
          data.forEach((item) => {
            this.treeData.push({
              id: item.qualityId,
              label: item.itemName,
              isLeaf: false,
              children: null
            })
          })
          this.treeLoading = false
          // console.log(this.treeData, "treeData");
        }
      })
    },
    refreshTree() {
      this.treeData = [] // 清空现有树数据
      this.getHazardHazardCategory() // 重新加载树数据
    },
    loadNode(node, resolve) {
      if (node.level === 0) {
        return resolve([])
      }

      const currentNode = node.data
      // 若该节点已有 children，说明已请求过，直接解析现有 children
      if (currentNode.children) {
        return resolve(currentNode.children)
      }
      const parentId = currentNode.id
      listZjQualityProblemCategory({ parentId })
        .then((res) => {
          if (res.code === 200) {
            const children = res.rows.map((item) => ({
              id: item.qualityId,
              label: item.itemName,
              // 根据子节点是否存在判断是否为叶子节点
              isLeaf: !item.children || item.children.length === 0
            }))
            // 将子节点数据赋值给当前节点的 children 属性
            currentNode.children = children
            resolve(children)
          } else {
            resolve([])
          }
        })
        .catch(() => {
          resolve([])
        })
    },
    transformChildren(children) {
      console.log(children)
      if (!children || children.length === 0) return []
      return children.map((child) => ({
        label: child.itemName,
        id: child.qualityId,
        children: this.transformChildren(child.children)
      }))
    },
    handleNodeClick(nodeData, node) {
      // console.log(nodeData, node, "nodeData");
      // this.queryParams.hazardId = nodeData.id;
      if (node.isLeaf) {
        // 执行筛选操作
        this.selectId = nodeData.id
        this.handleQuery()
      }
    },
    setLabelRef(el, node) {
      if (el) {
        this.labelRefs.set(node.id || node.label, el)
      }
    },
    /** 查询质量问题库列表 */
    getList() {
      this.loading = true
      listZjQualityProblemInfo(this.queryParams).then((res) => {
        this.zjQualityProblemInfoList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        qualityId: null,
        parentId: 0,
        itemName: null,
        qualityCode: null,
        commonProblem: null,
        rectificationRequirements: null,
        problemLevel: '一般问题',
        questionCategory: null,
        qualityStatus: 0,
        status: null,
        rectificationDeadline: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        questionCategory: null,
        questionCategoryPath: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.parentId = this.selectId
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // this.ids = selection.map((item) => item.qualityId);
      this.ids = selection.map((item, index) => {
        const serialNumber =
          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +
          selection.indexOf(item) +
          1
        return {
          serialNumber,
          qualityId: item.qualityId
        }
      })
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.questionCategory = this.questionCategory
      this.open = true
      this.title = '添加质量问题库'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const qualityId = row.qualityId || this.ids
      getZjQualityProblemInfo(qualityId).then((res) => {
        this.form = res.data
        // 根据当前记录的parentId和questionCategory构建级联选择器的路径
        this.buildCategoryPath()
        this.open = true
        this.title = '修改质量问题库'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.qualityId != null) {
            updateZjQualityProblemInfo(this.form).then((res) => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addZjQualityProblemInfo(this.form).then((res) => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const qualityIds = row.qualityId || this.ids.map(item => item.qualityId)
      this.$modal
        .confirm('是否确认删除质量问题库编号为"' + qualityIds + '"的数据项？')
        .then(function () {
          return delZjQualityProblemInfo(qualityIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      // 如果有选中记录，导出选中的；否则导出全部
      if (this.ids.length > 0) {
        // 导出选中记录
        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {
          this.download(
            'inspection/zjQualityProblemInfo/export',
            {
              ids: this.ids.map(item => item.qualityId).join(',')
            },
            `zjQualityProblemInfo_selected_${new Date().getTime()}.xlsx`
          )
        })
      } else {
        // 导出全部记录（根据查询条件，但不包含分页参数）
        const exportParams = { ...this.queryParams }
        // 移除分页参数，确保导出全部数据
        delete exportParams.pageNum
        delete exportParams.pageSize

        this.download(
          'inspection/zjQualityProblemInfo/export',
          exportParams,
          `zjQualityProblemInfo_${new Date().getTime()}.xlsx`
        )
      }
    }
  }
}
</script>
<style scoped>
.title {
  font-size: 16px;
  cursor: pointer;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-tree-node.is-current>.el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}

.two-lines-ellipsis {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
</style>
