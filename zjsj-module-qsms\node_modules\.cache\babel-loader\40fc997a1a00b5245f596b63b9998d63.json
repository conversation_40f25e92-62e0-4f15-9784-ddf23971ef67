{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjProjectInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjProjectInfo\\index.vue", "mtime": 1757424290730}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zjProjectInfo", "require", "_data", "_info", "_selectPeopleTree", "_interopRequireDefault", "_default", "exports", "default", "name", "dicts", "components", "selectPeopleTree", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "zjProjectInfoList", "title", "open", "openView", "queryParams", "pageNum", "pageSize", "parentOrgId", "orgId", "belongCompany", "orgType", "code", "shortName", "syncCode", "manager", "<PERSON><PERSON><PERSON><PERSON>", "constructStatus", "constructType", "constructPurpose", "structType", "planStart", "planEnd", "actualStart", "area", "location", "longitude", "latitude", "biddingUnit", "constructUnit", "supervisingUnit", "updateDate", "deleted", "province", "city", "district", "projectCode", "id", "form", "rules", "required", "message", "trigger", "placeholder", "constructStatusDict", "companyList", "allCompanyList", "created", "getList", "getConstructStatusDict", "getCompanyList", "getAllCompanyList", "methods", "handleChange", "selectedItem", "$message", "error", "label", "_this", "querytree", "then", "res", "_this2", "_this3", "getDicts", "for<PERSON>ach", "item", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "getConstructStatus", "status", "handleOrgTreeNodeClick", "node", "console", "log", "handleQuery", "_this4", "listZjProjectInfo", "rows", "cancel", "reset", "_defineProperty2", "resetForm", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "length", "handleAdd", "handleDetail", "row", "_this5", "getZjProjectInfo", "handleUpdate", "_this6", "submitForm", "_this7", "$refs", "validate", "valid", "updateZjProjectInfo", "$modal", "msgSuccess", "addZjProjectInfo", "handleDelete", "_this8", "confirm", "delZjProjectInfo", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime"], "sources": ["src/views/inspection/zjProjectInfo/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- <el-col :span=\"4\">\r\n        <orgTree :type=\"'1'\" @nodeClick=\"handleOrgTreeNodeClick\"></orgTree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 2px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <el-form-item label=\"项目名称\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入项目名称\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!-- 所属公司 -->\r\n          <el-form-item label=\"所属公司\" prop=\"belongCompany\">\r\n            <el-select\r\n              v-model=\"queryParams.belongCompany\"\r\n              placeholder=\"请选择所属公司\"\r\n              style=\"width: 300px\"\r\n              filterable\r\n              clearable\r\n            >\r\n              <el-option\r\n                v-for=\"item in companyList\"\r\n                :key=\"item.belongCompany\"\r\n                :label=\"item.belongCompany\"\r\n                :value=\"item.belongCompany\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 项目状态 -->\r\n          <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n            <el-select\r\n              v-model=\"queryParams.constructStatus\"\r\n              placeholder=\"请选择项目状态\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.zj_construct_status\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n        <el-input\r\n          v-model=\"queryParams.shortName\"\r\n          placeholder=\"请输入项目简称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目同步编码\" prop=\"syncCode\">\r\n        <el-input\r\n          v-model=\"queryParams.syncCode\"\r\n          placeholder=\"请输入项目同步编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n          <!-- <el-form-item label=\"项目经理\" prop=\"manager\">\r\n            <el-input v-model=\"queryParams.manager\" placeholder=\"请输入项目经理\" clearable style=\"width: 300px\"\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item> -->\r\n          <!-- <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n        <el-input\r\n          v-model=\"queryParams.managerMobile\"\r\n          placeholder=\"请输入项目经理电话\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n        <el-input\r\n          v-model=\"queryParams.constructPurpose\"\r\n          placeholder=\"请输入工程用途\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.planStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择合同工期开始\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.planEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择合同工期结束\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.actualStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择实际工期开始\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n        <el-input\r\n          v-model=\"queryParams.area\"\r\n          placeholder=\"请输入建筑面积(平方米)\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程地点\" prop=\"location\">\r\n        <el-input\r\n          v-model=\"queryParams.location\"\r\n          placeholder=\"请输入工程地点\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"经度\" prop=\"longitude\">\r\n        <el-input\r\n          v-model=\"queryParams.longitude\"\r\n          placeholder=\"请输入经度\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"纬度\" prop=\"latitude\">\r\n        <el-input\r\n          v-model=\"queryParams.latitude\"\r\n          placeholder=\"请输入纬度\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.biddingUnit\"\r\n          placeholder=\"请输入中标单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.constructUnit\"\r\n          placeholder=\"请输入建设单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.supervisingUnit\"\r\n          placeholder=\"请输入监理单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.updateDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择更新日期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n        <el-input\r\n          v-model=\"queryParams.deleted\"\r\n          placeholder=\"请输入删除状态\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"省份\" prop=\"province\">\r\n        <el-input\r\n          v-model=\"queryParams.province\"\r\n          placeholder=\"请输入省份\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"市\" prop=\"city\">\r\n        <el-input\r\n          v-model=\"queryParams.city\"\r\n          placeholder=\"请输入市\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区\" prop=\"district\">\r\n        <el-input\r\n          v-model=\"queryParams.district\"\r\n          placeholder=\"请输入区\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n        <el-input\r\n          v-model=\"queryParams.projectCode\"\r\n          placeholder=\"请输入项目编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n        <el-input\r\n          v-model=\"queryParams.id\"\r\n          placeholder=\"请输入组织全路径ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:edit']\"\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              >修改</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"zjProjectInfoList\"\r\n          height=\"calc(100vh - 230px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"主键\" align=\"center\" prop=\"id\" /> -->\r\n          <!-- <el-table-column label=\"${comment}\" align=\"center\" prop=\"orgId\" />\r\n      <el-table-column label=\"${comment}\" align=\"center\" prop=\"parentOrgId\" /> -->\r\n          <!-- <el-table-column label=\"组织类型\" align=\"center\" prop=\"orgType\" /> -->\r\n          <el-table-column\r\n            label=\"项目名称\"\r\n            align=\"center\"\r\n            prop=\"name\"\r\n            width=\"200\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column\r\n            label=\"编码\"\r\n            align=\"center\"\r\n            prop=\"code\"\r\n            width=\"200\"\r\n            show-overflow-tooltip\r\n          /> -->\r\n          <!-- 所属公司 -->\r\n          <el-table-column\r\n            label=\"所属公司\"\r\n            align=\"center\"\r\n            prop=\"belongCompany\"\r\n          />\r\n\r\n          <!-- <el-table-column label=\"项目简称\" align=\"center\" prop=\"shortName\" />\r\n      <el-table-column label=\"项目同步编码\" align=\"center\" prop=\"syncCode\" /> -->\r\n          <!-- <el-table-column label=\"手机\" align=\"center\" prop=\"managerMobile\" /> -->\r\n          <el-table-column\r\n            label=\"工程类别\"\r\n            align=\"center\"\r\n            prop=\"constructType\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"项目状态\"\r\n            align=\"center\"\r\n            prop=\"constructStatus\"\r\n          >\r\n            <template slot-scope=\"{ row }\">\r\n              {{ getConstructStatus(row.constructStatus) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"项目经理\" align=\"center\" prop=\"manager\" />\r\n\r\n          <!-- <el-table-column\r\n            label=\"所属单位\"\r\n            align=\"center\"\r\n            prop=\"biddingUnit\"\r\n            width=\"180\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column> -->\r\n          <!-- <el-table-column\r\n        label=\"工程用途\"\r\n        align=\"center\"\r\n        prop=\"constructPurpose\"\r\n      /> -->\r\n          <!-- <el-table-column label=\"结构类型\" align=\"center\" prop=\"structType\" />\r\n      <el-table-column\r\n        label=\"合同工期开始\"\r\n        align=\"center\"\r\n        prop=\"planStart\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.planStart, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"合同工期结束\"\r\n        align=\"center\"\r\n        prop=\"planEnd\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.planEnd, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"实际工期开始\"\r\n        align=\"center\"\r\n        prop=\"actualStart\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.actualStart, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建筑面积(平方米)\" align=\"center\" prop=\"area\" />\r\n      <el-table-column label=\"工程地点\" align=\"center\" prop=\"location\" />\r\n      <el-table-column label=\"经度\" align=\"center\" prop=\"longitude\" />\r\n      <el-table-column label=\"纬度\" align=\"center\" prop=\"latitude\" />\r\n      <el-table-column label=\"中标单位\" align=\"center\" prop=\"biddingUnit\" />\r\n      <el-table-column label=\"建设单位\" align=\"center\" prop=\"constructUnit\" />\r\n      <el-table-column label=\"监理单位\" align=\"center\" prop=\"supervisingUnit\" />\r\n      <el-table-column\r\n        label=\"更新日期\"\r\n        align=\"center\"\r\n        prop=\"updateDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updateDate, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"删除状态\" align=\"center\" prop=\"deleted\" />\r\n      <el-table-column label=\"省份\" align=\"center\" prop=\"province\" />\r\n      <el-table-column label=\"市\" align=\"center\" prop=\"city\" />\r\n      <el-table-column label=\"区\" align=\"center\" prop=\"district\" />\r\n      <el-table-column label=\"项目编码\" align=\"center\" prop=\"projectCode\" />\r\n      <el-table-column label=\"组织全路径ID\" align=\"center\" prop=\"id\" /> -->\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"180\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:detail']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleDetail(scope.row)\"\r\n                >查看</el-button\r\n              >\r\n\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改项目信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <!-- <el-form-item label=\"${comment}\" prop=\"orgId\">\r\n          <el-input v-model=\"form.orgId\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"${comment}\" prop=\"parentOrgId\">\r\n          <el-input v-model=\"form.parentOrgId\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入项目名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n              <el-input v-model=\"form.shortName\" placeholder=\"请输入项目简称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"项目同步编码\" prop=\"syncCode\">\r\n          <el-input v-model=\"form.syncCode\" placeholder=\"请输入项目同步编码\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理\" prop=\"manager\">\r\n              <el-input v-model=\"form.manager\" placeholder=\"请输入项目经理\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n              <el-input v-model=\"form.managerMobile\" placeholder=\"请输入手机\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n              <el-select\r\n                v-model=\"form.constructStatus\"\r\n                placeholder=\"请选择项目状态\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.zj_construct_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 项目状态 -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <!-- 所属单位 -->\r\n            <el-form-item label=\"部门名称\" prop=\"belongCompany\">\r\n              <selectPeopleTree\r\n                v-model=\"form.belongCompany\"\r\n                :people-list=\"allCompanyList\"\r\n                placeholder=\"请搜索或选择部门名称\"\r\n                @change=\"handleChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程类别\" prop=\"constructType\">\r\n              <el-input\r\n                v-model=\"form.constructType\"\r\n                placeholder=\"请输入工程类别\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n              <el-input\r\n                v-model=\"form.constructPurpose\"\r\n                placeholder=\"请输入工程用途\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结构类型\" prop=\"structureType\">\r\n              <el-input\r\n                v-model=\"form.structureType\"\r\n                placeholder=\"请输入结构类型\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n              <el-date-picker\r\n                v-model=\"form.planStart\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择合同工期开始\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n              <el-date-picker\r\n                v-model=\"form.planEnd\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择合同工期结束\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStart\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择实际工期开始\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n              <el-input\r\n                v-model=\"form.area\"\r\n                placeholder=\"请输入建筑面积(平方米)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程地点\" prop=\"location\">\r\n              <el-input v-model=\"form.location\" placeholder=\"请输入工程地点\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n              <el-input\r\n                v-model=\"form.biddingUnit\"\r\n                placeholder=\"请输入中标单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n              <el-input\r\n                v-model=\"form.constructUnit\"\r\n                placeholder=\"请输入建设单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n              <el-input\r\n                v-model=\"form.supervisingUnit\"\r\n                placeholder=\"请输入监理单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.updateDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择更新日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n          <el-input v-model=\"form.deleted\" placeholder=\"请输入删除状态\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"省份\" prop=\"province\">\r\n              <el-input v-model=\"form.province\" placeholder=\"请输入省份\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"市\" prop=\"city\">\r\n              <el-input v-model=\"form.city\" placeholder=\"请输入市\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"区\" prop=\"district\">\r\n          <el-input v-model=\"form.district\" placeholder=\"请输入区\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n          <el-input v-model=\"form.projectCode\" placeholder=\"请输入项目编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n          <el-input v-model=\"form.id\" placeholder=\"请输入组织全路径ID\" />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"查看项目详情\"\r\n      :visible.sync=\"openView\"\r\n      width=\"900px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"name\">\r\n              {{ form.name || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n              {{ form.shortName || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理\" prop=\"manager\">\r\n              {{ form.manager || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n              {{ form.managerMobile || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n              {{ getConstructStatus(form.constructStatus) || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属公司\" prop=\"belongCompany\">\r\n              {{ form.belongCompany || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程类别\" prop=\"constructType\">\r\n              {{ form.constructType || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n        </el-row>\r\n\r\n        <!-- 行业类别 -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n              {{ form.constructPurpose || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结构类型\" prop=\"structureType\">\r\n              {{ form.structureType || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n              {{ form.planStart || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n              {{ form.planEnd || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n              {{ form.actualStart }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n              {{ form.area || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程地点\" prop=\"location\">\r\n              {{ form.location || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n              {{ form.biddingUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n              {{ form.constructUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n              {{ form.supervisingUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"省份\" prop=\"province\">\r\n              {{ form.province || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"市\" prop=\"city\">\r\n              {{ form.city || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--\r\n        <el-form-item label=\"经度\" prop=\"longitude\">\r\n          <el-input v-model=\"form.longitude\" placeholder=\"请输入经度\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"纬度\" prop=\"latitude\">\r\n          <el-input v-model=\"form.latitude\" placeholder=\"请输入纬度\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.updateDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择更新日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n          <el-input v-model=\"form.deleted\" placeholder=\"请输入删除状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"区\" prop=\"district\">\r\n          <el-input v-model=\"form.district\" placeholder=\"请输入区\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n          <el-input v-model=\"form.projectCode\" placeholder=\"请输入项目编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n          <el-input v-model=\"form.id\" placeholder=\"请输入组织全路径ID\" />\r\n        </el-form-item>-->\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjProjectInfo,\r\n  getZjProjectInfo,\r\n  delZjProjectInfo,\r\n  addZjProjectInfo,\r\n  updateZjProjectInfo,\r\n} from \"@/api/inspection/zjProjectInfo\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { getCompanyList, querytree } from \"@/api/system/info\";\r\n// import orgTree from \"../../components/orgTree.vue\";\r\nimport selectPeopleTree from \"@/views/components/selectPeopleTree.vue\";\r\n\r\nexport default {\r\n  name: \"ZjProjectInfo\",\r\n  dicts: [\"zj_construct_status\"],\r\n  components: {\r\n    // orgTree,\r\n    selectPeopleTree,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目信息表格数据\r\n      zjProjectInfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      openView: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parentOrgId: null,\r\n        orgId: null,\r\n        belongCompany: null,\r\n        orgType: null,\r\n        code: null,\r\n        name: null,\r\n        shortName: null,\r\n        syncCode: null,\r\n        manager: null,\r\n        managerMobile: null,\r\n        constructStatus: \"1\",\r\n        constructType: null,\r\n        constructPurpose: null,\r\n        structType: null,\r\n        planStart: null,\r\n        planEnd: null,\r\n        actualStart: null,\r\n        area: null,\r\n        location: null,\r\n        longitude: null,\r\n        latitude: null,\r\n        biddingUnit: null,\r\n        constructUnit: null,\r\n        supervisingUnit: null,\r\n        updateDate: null,\r\n        deleted: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        projectCode: null,\r\n        id: null,\r\n      },\r\n\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"项目名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        shortName: [\r\n          { required: true, message: \"项目简称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        manager: [\r\n          { required: true, message: \"项目经理不能为空\", trigger: \"blur\" },\r\n        ],\r\n        managerMobile: [\r\n          { required: true, message: \"项目经理电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        belongCompany: [\r\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        constructStatus: [\r\n          { required: true, message: \"项目状态不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      placeholder: \"请输入所属单位\",\r\n      constructStatusDict: [],\r\n      companyList: [],\r\n      allCompanyList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getConstructStatusDict();\r\n    this.getCompanyList();\r\n    this.getAllCompanyList();\r\n  },\r\n  methods: {\r\n    handleChange(selectedItem) {\r\n      if (selectedItem.id == \"654470716198912\") {\r\n        this.$message.error(\"请选择下级单位\");\r\n        return;\r\n      }\r\n      if (selectedItem) {\r\n        this.form.belongCompany = selectedItem.label;\r\n\r\n        this.form.orgId = selectedItem.id;\r\n      } else {\r\n        this.form.belongCompany = null;\r\n        this.form.orgId = null;\r\n      }\r\n    },\r\n    getAllCompanyList() {\r\n      querytree().then((res) => {\r\n        if (res.code == 200) {\r\n          this.allCompanyList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getCompanyList() {\r\n      getCompanyList().then((res) => {\r\n        if (res.code == 200) {\r\n          this.companyList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getConstructStatusDict() {\r\n      getDicts(\"zj_construct_status\").then((res) => {\r\n        res.data.forEach((item) => {\r\n          this.constructStatusDict[item.dictValue] = item.dictLabel;\r\n        });\r\n      });\r\n    },\r\n    getConstructStatus(status) {\r\n      return this.constructStatusDict[status];\r\n    },\r\n\r\n    handleOrgTreeNodeClick(node) {\r\n      // 在这里处理接收到的子组件数据\r\n      console.log(\"接收到子组件数据:\", node.id);\r\n      // 可以根据nodeData更新查询参数或其他状态\r\n      this.queryParams.parentOrgId = node.id; // 假设nodeData中有id字段\r\n      this.handleQuery(); // 触发查询\r\n    },\r\n\r\n    /** 查询项目信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjProjectInfo(this.queryParams).then((res) => {\r\n        this.zjProjectInfoList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        orgId: null,\r\n        belongCompany: null,\r\n        parentOrgId: null,\r\n        orgType: null,\r\n        code: null,\r\n        name: null,\r\n        shortName: null,\r\n        syncCode: null,\r\n        manager: null,\r\n        managerMobile: null,\r\n        constructStatus: null,\r\n        constructType: null,\r\n        constructPurpose: null,\r\n        structType: null,\r\n        planStart: null,\r\n        planEnd: null,\r\n        actualStart: null,\r\n        area: null,\r\n        location: null,\r\n        longitude: null,\r\n        latitude: null,\r\n        biddingUnit: null,\r\n        constructUnit: null,\r\n        supervisingUnit: null,\r\n        updateDate: null,\r\n        deleted: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        projectCode: null,\r\n        id: null,\r\n      };\r\n      this.belongCompany = null;\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n\r\n      this.queryParams.parentOrgId = \"\";\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加项目信息\";\r\n    },\r\n    handleDetail(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjProjectInfo(id).then((res) => {\r\n        this.form = res.data;\r\n        this.openView = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjProjectInfo(id).then((res) => {\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改项目信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjProjectInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.getCompanyList();\r\n            });\r\n          } else {\r\n            addZjProjectInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.getCompanyList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除项目信息编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjProjectInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/zjProjectInfo/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjProjectInfo_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AA80BA,IAAAA,cAAA,GAAAC,OAAA;AAOA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAEA,IAAAG,iBAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,IAAAK,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IACA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,iBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,QAAA;MAEA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,aAAA;QACAC,OAAA;QACAC,IAAA;QACAtB,IAAA;QACAuB,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,eAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,SAAA;QACAC,OAAA;QACAC,WAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,WAAA;QACAC,EAAA;MACA;MAEA;MACAC,IAAA;MACA;MACAC,KAAA;QACAjD,IAAA,GACA;UAAAkD,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,SAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA3B,OAAA,GACA;UAAAyB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA1B,aAAA,GACA;UAAAwB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhC,aAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAzB,eAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,WAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,sBAAA;IACA,KAAAC,cAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,YAAA;MACA,IAAAA,YAAA,CAAAjB,EAAA;QACA,KAAAkB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,IAAAF,YAAA;QACA,KAAAhB,IAAA,CAAA5B,aAAA,GAAA4C,YAAA,CAAAG,KAAA;QAEA,KAAAnB,IAAA,CAAA7B,KAAA,GAAA6C,YAAA,CAAAjB,EAAA;MACA;QACA,KAAAC,IAAA,CAAA5B,aAAA;QACA,KAAA4B,IAAA,CAAA7B,KAAA;MACA;IACA;IACA0C,iBAAA,WAAAA,kBAAA;MAAA,IAAAO,KAAA;MACA,IAAAC,eAAA,IAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAjD,IAAA;UACA8C,KAAA,CAAAZ,cAAA,GAAAe,GAAA,CAAAnE,IAAA;QACA;MACA;IACA;IACAwD,cAAA,WAAAA,eAAA;MAAA,IAAAY,MAAA;MACA,IAAAZ,oBAAA,IAAAU,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAjD,IAAA;UACAkD,MAAA,CAAAjB,WAAA,GAAAgB,GAAA,CAAAnE,IAAA;QACA;MACA;IACA;IACAuD,sBAAA,WAAAA,uBAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,cAAA,yBAAAJ,IAAA,WAAAC,GAAA;QACAA,GAAA,CAAAnE,IAAA,CAAAuE,OAAA,WAAAC,IAAA;UACAH,MAAA,CAAAnB,mBAAA,CAAAsB,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAAE,SAAA;QACA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,MAAA;MACA,YAAA1B,mBAAA,CAAA0B,MAAA;IACA;IAEAC,sBAAA,WAAAA,uBAAAC,IAAA;MACA;MACAC,OAAA,CAAAC,GAAA,cAAAF,IAAA,CAAAnC,EAAA;MACA;MACA,KAAAhC,WAAA,CAAAG,WAAA,GAAAgE,IAAA,CAAAnC,EAAA;MACA,KAAAsC,WAAA;IACA;IAEA,eACA3B,OAAA,WAAAA,QAAA;MAAA,IAAA4B,MAAA;MACA,KAAAjF,OAAA;MACA,IAAAkF,gCAAA,OAAAxE,WAAA,EAAAuD,IAAA,WAAAC,GAAA;QACAe,MAAA,CAAA3E,iBAAA,GAAA4D,GAAA,CAAAiB,IAAA;QACAF,MAAA,CAAA5E,KAAA,GAAA6D,GAAA,CAAA7D,KAAA;QACA4E,MAAA,CAAAjF,OAAA;MACA;IACA;IACA;IACAoF,MAAA,WAAAA,OAAA;MACA,KAAA5E,IAAA;MACA,KAAA6E,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA1C,IAAA,OAAA2C,gBAAA,CAAA5F,OAAA;QACAgD,EAAA;QACA5B,KAAA;QACAC,aAAA;QACAF,WAAA;QACAG,OAAA;QACAC,IAAA;QACAtB,IAAA;QACAuB,SAAA;QACAC,QAAA;QACAC,OAAA;QACAC,aAAA;QACAC,eAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,SAAA;QACAC,OAAA;QACAC,WAAA;QACAC,IAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,UAAA;QACAC,OAAA;QACAC,QAAA;QACAC,IAAA;QACAC,QAAA;QACAC,WAAA;MAAA,SACA,KACA;MACA,KAAA1B,aAAA;MACA,KAAAwE,SAAA;IACA;IACA,aACAP,WAAA,WAAAA,YAAA;MACA,KAAAtE,WAAA,CAAAC,OAAA;MACA,KAAA0C,OAAA;IACA;IACA,aACAmC,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MAEA,KAAA7E,WAAA,CAAAG,WAAA;MACA,KAAAmE,WAAA;IACA;IACA;IACAS,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAzF,GAAA,GAAAyF,SAAA,CAAAC,GAAA,WAAApB,IAAA;QAAA,OAAAA,IAAA,CAAA7B,EAAA;MAAA;MACA,KAAAxC,MAAA,GAAAwF,SAAA,CAAAE,MAAA;MACA,KAAAzF,QAAA,IAAAuF,SAAA,CAAAE,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAR,KAAA;MACA,KAAA7E,IAAA;MACA,KAAAD,KAAA;IACA;IACAuF,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAX,KAAA;MACA,IAAA3C,EAAA,GAAAqD,GAAA,CAAArD,EAAA,SAAAzC,GAAA;MACA,IAAAgG,+BAAA,EAAAvD,EAAA,EAAAuB,IAAA,WAAAC,GAAA;QACA8B,MAAA,CAAArD,IAAA,GAAAuB,GAAA,CAAAnE,IAAA;QACAiG,MAAA,CAAAvF,QAAA;MACA;IACA;IACA,aACAyF,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAAd,KAAA;MACA,IAAA3C,EAAA,GAAAqD,GAAA,CAAArD,EAAA,SAAAzC,GAAA;MACA,IAAAgG,+BAAA,EAAAvD,EAAA,EAAAuB,IAAA,WAAAC,GAAA;QACAiC,MAAA,CAAAxD,IAAA,GAAAuB,GAAA,CAAAnE,IAAA;QACAoG,MAAA,CAAA3F,IAAA;QACA2F,MAAA,CAAA5F,KAAA;MACA;IACA;IACA,WACA6F,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA1D,IAAA,CAAAD,EAAA;YACA,IAAA+D,kCAAA,EAAAJ,MAAA,CAAA1D,IAAA,EAAAsB,IAAA,WAAAC,GAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7F,IAAA;cACA6F,MAAA,CAAAhD,OAAA;cACAgD,MAAA,CAAA9C,cAAA;YACA;UACA;YACA,IAAAqD,+BAAA,EAAAP,MAAA,CAAA1D,IAAA,EAAAsB,IAAA,WAAAC,GAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7F,IAAA;cACA6F,MAAA,CAAAhD,OAAA;cACAgD,MAAA,CAAA9C,cAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAsD,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,MAAA;MACA,IAAA7G,GAAA,GAAA8F,GAAA,CAAArD,EAAA,SAAAzC,GAAA;MACA,KAAAyG,MAAA,CACAK,OAAA,oBAAA9G,GAAA,aACAgE,IAAA;QACA,WAAA+C,+BAAA,EAAA/G,GAAA;MACA,GACAgE,IAAA;QACA6C,MAAA,CAAAzD,OAAA;QACAyD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GACAM,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,CACA,uCAAAC,cAAA,CAAA1H,OAAA,MAEA,KAAAgB,WAAA,oBAAA2G,MAAA,CAEA,IAAAC,IAAA,GAAAC,OAAA,YACA;IACA;EACA;AACA", "ignoreList": []}]}