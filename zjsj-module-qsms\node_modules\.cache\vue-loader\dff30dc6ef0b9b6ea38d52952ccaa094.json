{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\hazard\\index.vue", "mtime": 1757425884328}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0SGF6YXJkLA0KICBnZXRIYXphcmQsDQogIGRlbEhhemFyZCwNCiAgYWRkSGF6YXJkLA0KICB1cGRhdGVIYXphcmQsDQogIHRyZWVIYXphcmQsDQogIHRyZWVIYXphcmRGaXJzdCwNCiAgZ2V0SGF6YXJkSGF6YXJkQ2F0ZWdvcnksDQp9IGZyb20gIkAvYXBpL2luc3BlY3Rpb24vaGF6YXJkIjsNCmltcG9ydCBzZWxlY3RIYXphcmRDYXRlZ29yeVRyZWUgZnJvbSAiQC92aWV3cy9jb21wb25lbnRzL3NlbGVjdEhhemFyZENhdGVnb3J5VHJlZS52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJIYXphcmQiLA0KICBjb21wb25lbnRzOiB7DQogICAgc2VsZWN0SGF6YXJkQ2F0ZWdvcnlUcmVlLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0cmVlTG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6ZqQ5oKj6Zeu6aKY5bqT6KGo5qC85pWw5o2uDQogICAgICBoYXphcmRMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBoYXphcmROYW1lOiBudWxsLA0KICAgICAgICBoYXphcmRJZDogbnVsbCwNCiAgICAgICAgcGFyZW50SWQ6IG51bGwsDQogICAgICAgIGhhemFyZENvZGU6IG51bGwsDQogICAgICAgIG9yZGVyTnVtOiBudWxsLA0KICAgICAgICB0YXJnZXQ6IG51bGwsDQogICAgICAgIGV4YW1pZDogbnVsbCwNCiAgICAgICAgY2F0ZWdvcnlUeXBlOiBudWxsLA0KICAgICAgICBpbnZlc3RpZ2F0aW9uQ29udGVudDogbnVsbCwNCiAgICAgICAgaGF6YXJkTGV2ZWw6IG51bGwsDQogICAgICAgIGhhemFyZFN0YXR1czogbnVsbCwNCiAgICAgICAgcmVjdGlmaWNhdGlvbkRlYWRsaW5lOiBudWxsLA0KICAgICAgICByZWN0aWZpY2F0aW9uUmVxdWlyZW1lbnRzOiBudWxsLA0KICAgICAgICBoYXphcmRJZGVudGlmaWNhdGlvbjogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBoYXphcmRTdGF0dXM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi54q25oCB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaW52ZXN0aWdhdGlvbkNvbnRlbnQ6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5o6S5p+l5YaF5a655LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHJlY3RpZmljYXRpb25SZXF1aXJlbWVudHM6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pW05pS56KaB5rGC5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgICAgdHJlZURhdGE6IFtdLA0KICAgICAgZGVmYXVsdFByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAiY2hpbGRyZW4iLA0KICAgICAgICBsYWJlbDogImxhYmVsIiwNCiAgICAgICAgaXNMZWFmOiAiaXNMZWFmIiwNCiAgICAgIH0sDQogICAgICBzZWxlY3RJZDogbnVsbCwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0SGF6YXJkSGF6YXJkQ2F0ZWdvcnkoKTsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUhhemFyZENhdGVnb3J5Q2hhbmdlKHNlbGVjdGVkSXRlbSkgew0KICAgICAgaWYgKHNlbGVjdGVkSXRlbSkgew0KICAgICAgICBjb25zb2xlLmxvZyhzZWxlY3RlZEl0ZW0sICJzZWxlY3RlZEl0ZW0iKTsNCiAgICAgICAgdGhpcy5mb3JtLmhhemFyZENhdGVnb3J5ID0gc2VsZWN0ZWRJdGVtLmxhYmVsIHx8IHNlbGVjdGVkSXRlbS52YWx1ZTsNCiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gc2VsZWN0ZWRJdGVtLmlkOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLmhhemFyZENhdGVnb3J5ID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g6K6+572u6ZqQ5oKj57G75Yir5Zue5pi+DQogICAgc2V0SGF6YXJkQ2F0ZWdvcnlFY2hvKCkgew0KICAgICAgY29uc29sZS5sb2coJ+W8gOWni+iuvue9rumakOaCo+exu+WIq+WbnuaYvicpOw0KICAgICAgY29uc29sZS5sb2coJ2Zvcm0uaGF6YXJkQ2F0ZWdvcnk6JywgdGhpcy5mb3JtLmhhemFyZENhdGVnb3J5KTsNCiAgICAgIGNvbnNvbGUubG9nKCd0cmVlRGF0YTonLCB0aGlzLnRyZWVEYXRhKTsNCg0KICAgICAgaWYgKCF0aGlzLmZvcm0uaGF6YXJkQ2F0ZWdvcnkpIHsNCiAgICAgICAgY29uc29sZS5sb2coJ+makOaCo+exu+WIq+WAvOS4uuepuu+8jOi3s+i/h+WbnuaYvicpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIGNvbnN0IHRyeVNldEVjaG8gPSAocmV0cnlDb3VudCA9IDApID0+IHsNCiAgICAgICAgY29uc29sZS5sb2coYOWwneivleiuvue9ruWbnuaYvu+8jOesrCR7cmV0cnlDb3VudCArIDF95qyhYCk7DQoNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGNvbXBvbmVudCA9IHRoaXMuJHJlZnMuaGF6YXJkQ2F0ZWdvcnlTZWxlY3Q7DQogICAgICAgICAgY29uc29sZS5sb2coJ+e7hOS7tuW8leeUqDonLCBjb21wb25lbnQpOw0KDQogICAgICAgICAgaWYgKGNvbXBvbmVudCkgew0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+e7hOS7tuWtmOWcqO+8jOW8gOWni+iuvue9ruWbnuaYvicpOw0KDQogICAgICAgICAgICAvLyDorr7nva7mmL7npLrlgLwNCiAgICAgICAgICAgIGNvbXBvbmVudC5kaXNwbGF5VmFsdWUgPSB0aGlzLmZvcm0uaGF6YXJkQ2F0ZWdvcnk7DQogICAgICAgICAgICBjb25zb2xlLmxvZygn6K6+572uIGRpc3BsYXlWYWx1ZTonLCBjb21wb25lbnQuZGlzcGxheVZhbHVlKTsNCg0KICAgICAgICAgICAgLy8g5qOA5p+l5qCR5pWw5o2u5piv5ZCm5bey5Yqg6L29DQogICAgICAgICAgICBpZiAodGhpcy50cmVlRGF0YSAmJiB0aGlzLnRyZWVEYXRhLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ+agkeaVsOaNruW3suWKoOi9ve+8jOiuvue9rumAieS4reeKtuaAgScpOw0KDQogICAgICAgICAgICAgIC8vIOetieW+heagkeaVsOaNruWKoOi9veWujOaIkOWQjuiuvue9rumAieS4reeKtuaAgQ0KICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoY29tcG9uZW50LnNldFRyZWVTZWxlY3Rpb24pIHsNCiAgICAgICAgICAgICAgICAgIGNvbXBvbmVudC5zZXRUcmVlU2VsZWN0aW9uKCk7DQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybignc2V0VHJlZVNlbGVjdGlvbiDmlrnms5XkuI3lrZjlnKgnKTsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0sIDIwMCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBjb25zb2xlLmxvZygn5qCR5pWw5o2u5pyq5Yqg6L2977yM562J5b6F5pWw5o2u5Yqg6L29Jyk7DQogICAgICAgICAgICAgIGlmIChyZXRyeUNvdW50IDwgMTApIHsNCiAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgICAgIHRyeVNldEVjaG8ocmV0cnlDb3VudCArIDEpOw0KICAgICAgICAgICAgICAgIH0sIDMwMCk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+makOaCo+exu+WIq+WbnuaYvuiuvue9ruWujOaIkDonLCB0aGlzLmZvcm0uaGF6YXJkQ2F0ZWdvcnkpOw0KICAgICAgICAgIH0gZWxzZSBpZiAocmV0cnlDb3VudCA8IDEwKSB7DQogICAgICAgICAgICAvLyDlpoLmnpznu4Tku7bov5jmsqHlh4blpIflpb3vvIzph43or5XmnIDlpJoxMOasoQ0KICAgICAgICAgICAgY29uc29sZS5sb2coJ+e7hOS7tuacquWHhuWkh+Wlve+8jOetieW+hemHjeivlScpOw0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHRyeVNldEVjaG8ocmV0cnlDb3VudCArIDEpOw0KICAgICAgICAgICAgfSwgMzAwKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY29uc29sZS53YXJuKCfpmpDmgqPnsbvliKvlm57mmL7orr7nva7lpLHotKXvvIznu4Tku7bmnKrlh4blpIflpb0nKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfTsNCg0KICAgICAgdHJ5U2V0RWNobygpOw0KICAgIH0sDQogICAgZXhwYW5kVG9Ob2RlKG5vZGUpIHsNCiAgICAgIGlmIChub2RlICYmIG5vZGUucGFyZW50ICYmICFub2RlLnBhcmVudC5pc1Jvb3QpIHsNCiAgICAgICAgdGhpcy5leHBhbmRUb05vZGUobm9kZS5wYXJlbnQpOw0KICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0RXhwYW5kZWRLZXkobm9kZS5wYXJlbnQuaWQsIHRydWUpOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0VmlzaWJsZUNoYW5nZSh2aXNpYmxlKSB7DQogICAgICBpZiAodmlzaWJsZSkgew0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5oYXphcmRDYXRlZ29yeSkgew0KICAgICAgICAgICAgLy8g56Gu5L+d6IqC54K55bey5Yqg6L29DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRyZWUuc2V0Q3VycmVudEtleSh0aGlzLmZvcm0uaGF6YXJkQ2F0ZWdvcnksICgpID0+IHsNCiAgICAgICAgICAgICAgY29uc3QgY3VycmVudE5vZGUgPSB0aGlzLiRyZWZzLnRyZWUuZ2V0Q3VycmVudE5vZGUoKTsNCiAgICAgICAgICAgICAgaWYgKGN1cnJlbnROb2RlKSB7DQogICAgICAgICAgICAgICAgdGhpcy5leHBhbmRUb05vZGUoY3VycmVudE5vZGUpOw0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPD0gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6ZqQ5oKj5p2h55uuIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLmlkcywgInRoaXMuaWRzIik7DQogICAgICBjb25zdCBzdGF0dXMgPSBjb21tYW5kID09PSAiZW5hYmxlIiA/ICLmraPluLgiIDogIuemgeeUqCI7DQogICAgICBjb25zdCBwcm9taXNlcyA9IHRoaXMuaWRzLm1hcCgoaXRlbSkgPT4gew0KICAgICAgICByZXR1cm4gdXBkYXRlSGF6YXJkKHsNCiAgICAgICAgICBoYXphcmRJZDogaXRlbS5oYXphcmRJZCwNCiAgICAgICAgICBoYXphcmRTdGF0dXM6IHN0YXR1cywNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICAgIFByb21pc2UuYWxsU2V0dGxlZChwcm9taXNlcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnN0IHN1Y2Nlc3NDb3VudCA9IHJlcy5maWx0ZXIoDQogICAgICAgICAgKGl0ZW0pID0+IGl0ZW0uc3RhdHVzID09PSAiZnVsZmlsbGVkIg0KICAgICAgICApLmxlbmd0aDsNCiAgICAgICAgY29uc3QgZmFpbGVkUmVzdWx0cyA9IHJlcy5maWx0ZXIoKGl0ZW0pID0+IGl0ZW0uc3RhdHVzID09PSAicmVqZWN0ZWQiKTsNCiAgICAgICAgaWYgKHN1Y2Nlc3NDb3VudCA+IDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgIG1lc3NhZ2U6IGAke2NvbW1hbmQgPT09ICJlbmFibGUiID8gIuWQr+eUqCIgOiAi5bqf5byDIn3miJDlip9gLA0KICAgICAgICAgICAgdHlwZTogInN1Y2Nlc3MiLA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zdCBlcnJvck1lc3NhZ2VzID0gZmFpbGVkUmVzdWx0cw0KICAgICAgICAgICAgLm1hcCgocmVzdWx0LCBpbmRleCkgPT4gew0KICAgICAgICAgICAgICBjb25zdCBpZCA9IHRoaXMuaWRzW2luZGV4XS5zZXJpYWxOdW1iZXI7DQogICAgICAgICAgICAgIGNvbnN0IGVycm9yTXNnID0gYCR7Y29tbWFuZCA9PT0gImVuYWJsZSIgPyAi5ZCv55SoIiA6ICLlup/lvIMifeWksei0pWA7DQogICAgICAgICAgICAgIHJldHVybiBg5bqP5Y+35Li6ICR7aWR9IOeahOmakOaCo+adoeebru+8miR7ZXJyb3JNc2d9YDsNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuam9pbigiXG4iKTsNCg0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgICAgbWVzc2FnZTogYCR7ZXJyb3JNZXNzYWdlc31gLA0KICAgICAgICAgICAgdHlwZTogImVycm9yIiwNCiAgICAgICAgICAgIGRhbmdlcm91c2x5VXNlSFRNTFN0cmluZzogdHJ1ZSwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnb1RvSGF6YXJkQ2F0ZWdvcnkoKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6ICJoYXphcmRDYXRlZ29yeSIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldEhhemFyZEhhemFyZENhdGVnb3J5KCkgew0KICAgICAgdGhpcy50cmVlTG9hZGluZyA9IHRydWU7DQogICAgICBnZXRIYXphcmRIYXphcmRDYXRlZ29yeSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIC8vIGNvbnNvbGUubG9nKHJlcywgInJlcyIpOw0KICAgICAgICAgIHRoaXMudHJlZURhdGEgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLnRyZWVMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgY29uc29sZS5sb2codGhpcy50cmVlRGF0YSwgInRoaXMudHJlZURhdGEiKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyBsb2FkTm9kZShub2RlLCByZXNvbHZlKSB7DQogICAgLy8gICBpZiAobm9kZS5sZXZlbCA9PT0gMCkgew0KICAgIC8vICAgICByZXR1cm4gcmVzb2x2ZShbXSk7DQogICAgLy8gICB9DQoNCiAgICAvLyAgIGNvbnN0IGN1cnJlbnROb2RlID0gbm9kZS5kYXRhOw0KICAgIC8vICAgLy8g6Iul6K+l6IqC54K55bey5pyJIGNoaWxkcmVu77yM6K+05piO5bey6K+35rGC6L+H77yM55u05o6l6Kej5p6Q546w5pyJIGNoaWxkcmVuDQogICAgLy8gICBpZiAoY3VycmVudE5vZGUuY2hpbGRyZW4pIHsNCiAgICAvLyAgICAgcmV0dXJuIHJlc29sdmUoY3VycmVudE5vZGUuY2hpbGRyZW4pOw0KICAgIC8vICAgfQ0KICAgIC8vICAgY29uc3QgcGFyZW50SWQgPSBjdXJyZW50Tm9kZS5pZDsNCiAgICAvLyAgIHRyZWVIYXphcmQoeyBwYXJlbnRJZCB9KQ0KICAgIC8vICAgICAudGhlbigocmVzKSA9PiB7DQogICAgLy8gICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAvLyAgICAgICAgIGNvbnN0IGNoaWxkcmVuID0gcmVzLnJvd3MubWFwKChpdGVtKSA9PiAoew0KICAgIC8vICAgICAgICAgICBpZDogaXRlbS5oYXphcmRJZCwNCiAgICAvLyAgICAgICAgICAgbGFiZWw6IGl0ZW0uaGF6YXJkTmFtZSwNCiAgICAvLyAgICAgICAgICAgLy8g5qC55o2u5a2Q6IqC54K55piv5ZCm5a2Y5Zyo5Yik5pat5piv5ZCm5Li65Y+25a2Q6IqC54K5DQogICAgLy8gICAgICAgICAgIGlzTGVhZjogIWl0ZW0uY2hpbGRyZW4gfHwgaXRlbS5jaGlsZHJlbi5sZW5ndGggPT09IDAsDQogICAgLy8gICAgICAgICB9KSk7DQogICAgLy8gICAgICAgICAvLyDlsIblrZDoioLngrnmlbDmja7otYvlgLznu5nlvZPliY3oioLngrnnmoQgY2hpbGRyZW4g5bGe5oCnDQogICAgLy8gICAgICAgICBjdXJyZW50Tm9kZS5jaGlsZHJlbiA9IGNoaWxkcmVuOw0KICAgIC8vICAgICAgICAgcmVzb2x2ZShjaGlsZHJlbik7DQogICAgLy8gICAgICAgfSBlbHNlIHsNCiAgICAvLyAgICAgICAgIHJlc29sdmUoW10pOw0KICAgIC8vICAgICAgIH0NCiAgICAvLyAgICAgfSkNCiAgICAvLyAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAvLyAgICAgICByZXNvbHZlKFtdKTsNCiAgICAvLyAgICAgfSk7DQogICAgLy8gfSwNCiAgICAvLyB0cmFuc2Zvcm1DaGlsZHJlbihjaGlsZHJlbikgew0KICAgIC8vICAgaWYgKCFjaGlsZHJlbiB8fCBjaGlsZHJlbi5sZW5ndGggPT09IDApIHJldHVybiBbXTsNCiAgICAvLyAgIHJldHVybiBjaGlsZHJlbi5tYXAoKGNoaWxkKSA9PiAoew0KICAgIC8vICAgICBsYWJlbDogY2hpbGQuaGF6YXJkTmFtZSwNCiAgICAvLyAgICAgaWQ6IGNoaWxkLmhhemFyZElkLA0KICAgIC8vICAgICBjaGlsZHJlbjogdGhpcy50cmFuc2Zvcm1DaGlsZHJlbihjaGlsZC5jaGlsZHJlbiksDQogICAgLy8gICB9KSk7DQogICAgLy8gfSwNCiAgICAvLyBoYW5kbGVOb2RlQ2xpY2sobm9kZURhdGEsIG5vZGUpIHsNCiAgICAvLyAgIGNvbnNvbGUubG9nKG5vZGUsICJub2RlRGF0YSIpOw0KICAgIC8vICAgLy8gdGhpcy5xdWVyeVBhcmFtcy5oYXphcmRJZCA9IG5vZGVEYXRhLmlkOw0KICAgIC8vICAgaWYgKG5vZGUuaXNMZWFmKSB7DQogICAgLy8gICAgIHRoaXMuc2VsZWN0SWQgPSBub2RlRGF0YS5pZDsNCiAgICAvLyAgICAgdGhpcy5mb3JtLmhhemFyZENhdGVnb3J5ID0gbm9kZURhdGEuaWQ7IC8vIOe7keWumklE6ICM6Z2ebGFiZWwNCiAgICAvLyAgICAgdGhpcy4kcmVmcy5zZWxlY3RSZWYuYmx1cigpOw0KICAgIC8vICAgfQ0KICAgIC8vIH0sDQogICAgLy8gc2V0TGFiZWxSZWYoZWwsIG5vZGUpIHsNCiAgICAvLyAgIGlmIChlbCkgew0KICAgIC8vICAgICB0aGlzLmxhYmVsUmVmcy5zZXQobm9kZS5pZCB8fCBub2RlLmxhYmVsLCBlbCk7DQogICAgLy8gICB9DQogICAgLy8gfSwNCiAgICAvKiog5p+l6K+i6ZqQ5oKj6Zeu6aKY5bqT5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0SGF6YXJkKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBjb25zdCBkYXRhID0gcmVzLnJvd3M7DQogICAgICAgIC8vIGRhdGEuc29ydCgoYSwgYikgPT4gew0KICAgICAgICAvLyAgIHJldHVybiBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpIC0gbmV3IERhdGUoYS5jcmVhdGVUaW1lKTsNCiAgICAgICAgLy8gfSk7DQogICAgICAgIHRoaXMuaGF6YXJkTGlzdCA9IGRhdGE7DQogICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBoYXphcmRJZDogbnVsbCwNCiAgICAgICAgaGF6YXJkTmFtZTogbnVsbCwNCiAgICAgICAgcGFyZW50SWQ6IG51bGwsDQogICAgICAgIGhhemFyZENvZGU6IG51bGwsDQogICAgICAgIG9yZGVyTnVtOiBudWxsLA0KICAgICAgICB0YXJnZXQ6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBleGFtaWQ6IG51bGwsDQogICAgICAgIGNhdGVnb3J5VHlwZTogbnVsbCwNCiAgICAgICAgaW52ZXN0aWdhdGlvbkNvbnRlbnQ6IG51bGwsDQogICAgICAgIGhhemFyZExldmVsOiBudWxsLA0KICAgICAgICBoYXphcmRTdGF0dXM6IG51bGwsDQogICAgICAgIHJlY3RpZmljYXRpb25EZWFkbGluZTogbnVsbCwNCiAgICAgICAgcmVjdGlmaWNhdGlvblJlcXVpcmVtZW50czogbnVsbCwNCiAgICAgICAgaGF6YXJkSWRlbnRpZmljYXRpb246IG51bGwsDQogICAgICAgIGFjY2lkZW50SGF6YXJkOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYXJlbnRJZCA9IHRoaXMuc2VsZWN0SWQ7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKChpdGVtLCBpbmRleCkgPT4gew0KICAgICAgICBjb25zdCBzZXJpYWxOdW1iZXIgPQ0KICAgICAgICAgICh0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gLSAxKSAqIHRoaXMucXVlcnlQYXJhbXMucGFnZVNpemUgKw0KICAgICAgICAgIHNlbGVjdGlvbi5pbmRleE9mKGl0ZW0pICsNCiAgICAgICAgICAxOw0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIHNlcmlhbE51bWJlciwNCiAgICAgICAgICBoYXphcmRJZDogaXRlbS5oYXphcmRJZCwNCiAgICAgICAgfTsNCiAgICAgIH0pOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOw0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoOw0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmakOaCo+mXrumimOW6kyI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaGF6YXJkSWQgPSByb3cuaGF6YXJkSWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRIYXphcmQoaGF6YXJkSWQpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXMuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnpmpDmgqPpl67popjlupMiOw0KDQogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bliLDnmoTooajljZXmlbDmja46JywgdGhpcy5mb3JtKTsNCiAgICAgICAgY29uc29sZS5sb2coJ+makOaCo+exu+WIq+WAvDonLCB0aGlzLmZvcm0uaGF6YXJkQ2F0ZWdvcnkpOw0KDQogICAgICAgIC8vIOW7tui/n+iuvue9ru+8jOehruS/neWvueivneahhuWSjOe7hOS7tumDveW3sue7j+a4suafk+WujOaIkA0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICB0aGlzLnNldEhhemFyZENhdGVnb3J5RWNobygpOw0KICAgICAgICB9LCA1MDApOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5oYXphcmRJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVIYXphcmQodGhpcy5mb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAvLyB0aGlzLmZvcm0ucGFyZW50SWQgPSB0aGlzLnNlbGVjdElkOw0KICAgICAgICAgICAgdGhpcy5mb3JtLmhhemFyZFN0YXR1cyA9ICLlkK/nlKgiOw0KICAgICAgICAgICAgLy8gaWYgKHRoaXMuZm9ybS5wYXJlbnRJZCA9PSBudWxsKSB7DQogICAgICAgICAgICAvLyAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKA0KICAgICAgICAgICAgLy8gICAgICLor7flhYjlnKjlt6bkvqfmoJHlvaLnu5PmnoTkuK3pgInmi6nlhbfkvZPnmoTpmpDmgqPnsbvliKvvvIznhLblkI7lho3mlrDlop7pmpDmgqPpl67popjlupMiDQogICAgICAgICAgICAvLyAgICk7DQogICAgICAgICAgICAvLyAgIHJldHVybjsNCiAgICAgICAgICAgIC8vIH0NCiAgICAgICAgICAgIGFkZEhhemFyZCh0aGlzLmZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBsZXQgaGF6YXJkSWRzOw0KICAgICAgaWYgKHJvdyAmJiByb3cuaGF6YXJkSWQpIHsNCiAgICAgICAgLy8g5Y2V6KGM5Yig6ZmkDQogICAgICAgIGhhemFyZElkcyA9IFtyb3cuaGF6YXJkSWRdOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aSa6KGM5Yig6Zmk77yM5LuOdGhpcy5pZHPkuK3mj5Dlj5ZoYXphcmRJZA0KICAgICAgICBoYXphcmRJZHMgPSB0aGlzLmlkcy5tYXAoKGl0ZW0pID0+IGl0ZW0uaGF6YXJkSWQpOw0KICAgICAgfQ0KDQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgNCiAgICAgICAgICAn5piv5ZCm56Gu6K6k5Yig6Zmk6ZqQ5oKj6Zeu6aKY5bqT57yW5Y+35Li6IicgKyBoYXphcmRJZHMuam9pbigiLCIpICsgJyLnmoTmlbDmja7pobnvvJ8nDQogICAgICAgICkNCiAgICAgICAgLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgIHJldHVybiBkZWxIYXphcmQoaGF6YXJkSWRzKTsNCiAgICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoDQogICAgICAgICJpbnNwZWN0aW9uL2hhemFyZC9leHBvcnQiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYGhhemFyZF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YA0KICAgICAgKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2XA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/hazard", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- 左侧树结构 -->\r\n      <!-- <el-col :span=\"3\">\r\n        <div class=\"title mb-2\" @click=\"goToHazardCategory\">隐患类别管理</div>\r\n\r\n        <el-tree\r\n                  ref=\"tree\"\r\n          v-loading=\"treeLoading\"\r\n          :data=\"treeData\"\r\n          :props=\"defaultProps\"\r\n          :load=\"loadNode\"\r\n          lazy\r\n          @node-click=\"handleNodeClick\"\r\n        >\r\n          <template #default=\"{ node, data }\">\r\n            <el-tooltip effect=\"dark\" :content=\"data.label\" placement=\"top\">\r\n              <span\r\n                :ref=\"(el) => setLabelRef(el, node)\"\r\n                class=\"el-tree-node__label\"\r\n              >\r\n                {{ node.label }}\r\n              </span>\r\n            </el-tooltip>\r\n          </template>\r\n        </el-tree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 10px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <!-- 排查内容 -->\r\n          <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n            <el-input\r\n              v-model=\"queryParams.investigationContent\"\r\n              placeholder=\"请输入排查内容\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n            <el-input\r\n              v-model=\"queryParams.hazardCategory\"\r\n              placeholder=\"请输入隐患类别\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-dropdown @command=\"handleCommand\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                type=\"success\"\r\n                plain\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                >修改状态<i class=\"el-icon-arrow-down el-icon--right\"\r\n              /></el-button>\r\n              <template #dropdown>\r\n                <el-dropdown-menu style=\"width: 100px; text-align: center\">\r\n                  <!-- 下拉选项，可根据实际需求修改 -->\r\n                  <el-dropdown-item command=\"enable\">启用</el-dropdown-item>\r\n                  <el-dropdown-item command=\"disable\">禁用</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </template>\r\n            </el-dropdown>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:hazard:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"hazardList\"\r\n          height=\"calc(100vh - 250px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"隐患明细 id\" align=\"center\" prop=\"hazardId\" /> -->\r\n          <!-- <el-table-column label=\"隐患名称\" align=\"center\" prop=\"hazardName\" /> -->\r\n          <!-- <el-table-column label=\"父菜单ID\" align=\"center\" prop=\"parentId\" /> -->\r\n          <!-- <el-table-column label=\"隐患编号\" align=\"center\" prop=\"hazardCode\" /> -->\r\n          <!-- <el-table-column label=\"显示顺序\" align=\"center\" prop=\"orderNum\" /> -->\r\n          <!-- <el-table-column label=\" 打开方式\" align=\"center\" prop=\"target\" /> -->\r\n          <!-- <el-table-column label=\"备注\" align=\"center\" prop=\"remark\" /> -->\r\n          <!-- <el-table-column label=\"检查类型\" align=\"center\" prop=\"examid\" /> -->\r\n          <!-- <el-table-column\r\n        label=\"大类类型：1：类型  2：行为\"\r\n        align=\"center\"\r\n        prop=\"categoryType\"\r\n      /> -->\r\n          <!-- 序号 -->\r\n          <el-table-column label=\"序号\" align=\"center\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              {{\r\n                (queryParams.pageNum - 1) * queryParams.pageSize +\r\n                scope.$index +\r\n                1\r\n              }}\r\n            </template>\r\n          </el-table-column>\r\n          <!-- 隐患类别 -->\r\n          <el-table-column\r\n            label=\"隐患类别\"\r\n            align=\"center\"\r\n            prop=\"hazardCategory\"\r\n            width=\"150\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"排查内容\"\r\n            align=\"center\"\r\n            prop=\"investigationContent\"\r\n            width=\"250\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column label=\"隐患级别\" align=\"center\" prop=\"hazardLevel\" /> -->\r\n          <el-table-column\r\n            label=\"整改要求\"\r\n            align=\"center\"\r\n            prop=\"rectificationRequirements\"\r\n            width=\"300\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"整改时限(天)\"\r\n            align=\"center\"\r\n            prop=\"rectificationDeadline\"\r\n            width=\"100\"\r\n          />\r\n          <!-- 事故隐患 -->\r\n          <el-table-column\r\n            label=\"事故隐患\"\r\n            align=\"center\"\r\n            prop=\"accidentHazard\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-tag\r\n                v-if=\"scope.row.accidentHazard === '1'\"\r\n                class=\"hazard-tag hazard-general\"\r\n                size=\"small\"\r\n              >\r\n                一般\r\n              </el-tag>\r\n              <el-tag\r\n                v-else-if=\"scope.row.accidentHazard === '2'\"\r\n                class=\"hazard-tag hazard-common\"\r\n                size=\"small\"\r\n              >\r\n                常见\r\n              </el-tag>\r\n              <span v-else>{{ scope.row.accidentHazard }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"隐患标识\"\r\n            align=\"center\"\r\n            prop=\"hazardIdentification\"\r\n          />\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"hazardStatus\" />\r\n\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"150\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:hazard:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改隐患问题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <!-- 隐患类别 -->\r\n        <el-form-item label=\"隐患类别\" prop=\"hazardCategory\">\r\n          <!-- <el-select\r\n            ref=\"selectRef\"\r\n            v-model=\"form.hazardCategory\"\r\n            placeholder=\"请选择隐患类别\"\r\n            style=\"width: 100%\"\r\n            clearable\r\n            :popper-append-to-body=\"false\"\r\n            @visible-change=\"handleSelectVisibleChange\"\r\n          >\r\n            <el-option\r\n              :value=\"form.hazardCategory\"\r\n              style=\"height: auto; padding: 0; border: none\"\r\n            >\r\n              <el-tree\r\n                :node-key=\"'id'\"\r\n                v-loading=\"treeLoading\"\r\n                :data=\"treeData\"\r\n                :props=\"defaultProps\"\r\n                :load=\"loadNode\"\r\n                lazy\r\n                @node-click=\"handleNodeClick\"\r\n                :expand-on-click-node=\"false\"\r\n                :highlight-current=\"true\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">{{ node.label }}</span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </el-option>\r\n          </el-select> -->\r\n          <selectHazardCategoryTree\r\n            ref=\"hazardCategorySelect\"\r\n            v-model=\"form.hazardCategory\"\r\n            :categoryList=\"treeData\"\r\n            placeholder=\"请选择隐患类别\"\r\n            @change=\"handleHazardCategoryChange\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患级别\" prop=\"hazardLevel\">\r\n          <el-select\r\n            v-model=\"form.hazardLevel\"\r\n            placeholder=\"请选择隐患级别\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一级\" value=\"1\" />\r\n            <el-option label=\"二级\" value=\"2\" />\r\n            <el-option label=\"三级\" value=\"3\" />\r\n            <el-option label=\"四级\" value=\"4\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <!-- 事故隐患 -->\r\n        <el-form-item label=\"事故隐患\" prop=\"accidentHazard\">\r\n          <!-- 一般 常见 -->\r\n          <el-select\r\n            v-model=\"form.accidentHazard\"\r\n            placeholder=\"请选择\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"一般\" value=\"1\" />\r\n            <el-option label=\"常见\" value=\"2\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"隐患标识\" prop=\"hazardIdentification\">\r\n          <el-input\r\n            v-model=\"form.hazardIdentification\"\r\n            placeholder=\"请输入隐患标识\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改时限(天)\" prop=\"rectificationDeadline\">\r\n          <el-input\r\n            v-model=\"form.rectificationDeadline\"\r\n            type=\"Number\"\r\n            placeholder=\"请输入整改时限(天)\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"hazardStatus\">\r\n          <el-select\r\n            v-model=\"form.hazardStatus\"\r\n            placeholder=\"请选择状态\"\r\n            style=\"width: 100%\"\r\n          >\r\n            <el-option label=\"启用\" value=\"启用\" />\r\n            <el-option label=\"禁用\" value=\"禁用\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排查内容\" prop=\"investigationContent\">\r\n          <el-input\r\n            v-model=\"form.investigationContent\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"整改要求\" prop=\"rectificationRequirements\">\r\n          <el-input\r\n            v-model=\"form.rectificationRequirements\"\r\n            :maxlength=\"255\"\r\n            rows=\"5\"\r\n            type=\"textarea\"\r\n            placeholder=\"请输入内容\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listHazard,\r\n  getHazard,\r\n  delHazard,\r\n  addHazard,\r\n  updateHazard,\r\n  treeHazard,\r\n  treeHazardFirst,\r\n  getHazardHazardCategory,\r\n} from \"@/api/inspection/hazard\";\r\nimport selectHazardCategoryTree from \"@/views/components/selectHazardCategoryTree.vue\";\r\n\r\nexport default {\r\n  name: \"Hazard\",\r\n  components: {\r\n    selectHazardCategoryTree,\r\n  },\r\n  data() {\r\n    return {\r\n      treeLoading: true,\r\n      // 遮罩层\r\n      loading: false,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 隐患问题库表格数据\r\n      hazardList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        hazardName: null,\r\n        hazardId: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        hazardStatus: [\r\n          { required: true, message: \"状态不能为空\", trigger: \"change\" },\r\n        ],\r\n        investigationContent: [\r\n          { required: true, message: \"排查内容不能为空\", trigger: \"blur\" },\r\n        ],\r\n        rectificationRequirements: [\r\n          { required: true, message: \"整改要求不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      treeData: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n        isLeaf: \"isLeaf\",\r\n      },\r\n      selectId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getHazardHazardCategory();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    handleHazardCategoryChange(selectedItem) {\r\n      if (selectedItem) {\r\n        console.log(selectedItem, \"selectedItem\");\r\n        this.form.hazardCategory = selectedItem.label || selectedItem.value;\r\n        this.form.parentId = selectedItem.id;\r\n      } else {\r\n        this.form.hazardCategory = null;\r\n        this.form.parentId = null;\r\n      }\r\n    },\r\n\r\n    // 设置隐患类别回显\r\n    setHazardCategoryEcho() {\r\n      console.log('开始设置隐患类别回显');\r\n      console.log('form.hazardCategory:', this.form.hazardCategory);\r\n      console.log('treeData:', this.treeData);\r\n\r\n      if (!this.form.hazardCategory) {\r\n        console.log('隐患类别值为空，跳过回显');\r\n        return;\r\n      }\r\n\r\n      const trySetEcho = (retryCount = 0) => {\r\n        console.log(`尝试设置回显，第${retryCount + 1}次`);\r\n\r\n        this.$nextTick(() => {\r\n          const component = this.$refs.hazardCategorySelect;\r\n          console.log('组件引用:', component);\r\n\r\n          if (component) {\r\n            console.log('组件存在，开始设置回显');\r\n\r\n            // 设置显示值\r\n            component.displayValue = this.form.hazardCategory;\r\n            console.log('设置 displayValue:', component.displayValue);\r\n\r\n            // 检查树数据是否已加载\r\n            if (this.treeData && this.treeData.length > 0) {\r\n              console.log('树数据已加载，设置选中状态');\r\n\r\n              // 等待树数据加载完成后设置选中状态\r\n              setTimeout(() => {\r\n                if (component.setTreeSelection) {\r\n                  component.setTreeSelection();\r\n                } else {\r\n                  console.warn('setTreeSelection 方法不存在');\r\n                }\r\n              }, 200);\r\n            } else {\r\n              console.log('树数据未加载，等待数据加载');\r\n              if (retryCount < 10) {\r\n                setTimeout(() => {\r\n                  trySetEcho(retryCount + 1);\r\n                }, 300);\r\n              }\r\n            }\r\n\r\n            console.log('隐患类别回显设置完成:', this.form.hazardCategory);\r\n          } else if (retryCount < 10) {\r\n            // 如果组件还没准备好，重试最多10次\r\n            console.log('组件未准备好，等待重试');\r\n            setTimeout(() => {\r\n              trySetEcho(retryCount + 1);\r\n            }, 300);\r\n          } else {\r\n            console.warn('隐患类别回显设置失败，组件未准备好');\r\n          }\r\n        });\r\n      };\r\n\r\n      trySetEcho();\r\n    },\r\n    expandToNode(node) {\r\n      if (node && node.parent && !node.parent.isRoot) {\r\n        this.expandToNode(node.parent);\r\n        this.$refs.tree.setExpandedKey(node.parent.id, true);\r\n      }\r\n    },\r\n    handleSelectVisibleChange(visible) {\r\n      if (visible) {\r\n        this.$nextTick(() => {\r\n          if (this.form.hazardCategory) {\r\n            // 确保节点已加载\r\n            this.$refs.tree.setCurrentKey(this.form.hazardCategory, () => {\r\n              const currentNode = this.$refs.tree.getCurrentNode();\r\n              if (currentNode) {\r\n                this.expandToNode(currentNode);\r\n              }\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n\r\n    handleCommand(command) {\r\n      if (this.ids.length <= 0) {\r\n        this.$message({\r\n          message: \"请选择隐患条目\",\r\n          type: \"warning\",\r\n        });\r\n        return;\r\n      }\r\n      // console.log(this.ids, \"this.ids\");\r\n      const status = command === \"enable\" ? \"正常\" : \"禁用\";\r\n      const promises = this.ids.map((item) => {\r\n        return updateHazard({\r\n          hazardId: item.hazardId,\r\n          hazardStatus: status,\r\n        });\r\n      });\r\n      Promise.allSettled(promises).then((res) => {\r\n        const successCount = res.filter(\r\n          (item) => item.status === \"fulfilled\"\r\n        ).length;\r\n        const failedResults = res.filter((item) => item.status === \"rejected\");\r\n        if (successCount > 0) {\r\n          this.$message({\r\n            message: `${command === \"enable\" ? \"启用\" : \"废弃\"}成功`,\r\n            type: \"success\",\r\n          });\r\n          this.handleQuery();\r\n        } else {\r\n          const errorMessages = failedResults\r\n            .map((result, index) => {\r\n              const id = this.ids[index].serialNumber;\r\n              const errorMsg = `${command === \"enable\" ? \"启用\" : \"废弃\"}失败`;\r\n              return `序号为 ${id} 的隐患条目：${errorMsg}`;\r\n            })\r\n            .join(\"\\n\");\r\n\r\n          this.$message({\r\n            message: `${errorMessages}`,\r\n            type: \"error\",\r\n            dangerouslyUseHTMLString: true,\r\n          });\r\n        }\r\n      });\r\n    },\r\n    goToHazardCategory() {\r\n      this.$router.push({\r\n        path: \"hazardCategory\",\r\n      });\r\n    },\r\n    getHazardHazardCategory() {\r\n      this.treeLoading = true;\r\n      getHazardHazardCategory().then((res) => {\r\n        if (res.code === 200) {\r\n          // console.log(res, \"res\");\r\n          this.treeData = res.data;\r\n          this.treeLoading = false;\r\n          console.log(this.treeData, \"this.treeData\");\r\n        }\r\n      });\r\n    },\r\n    // loadNode(node, resolve) {\r\n    //   if (node.level === 0) {\r\n    //     return resolve([]);\r\n    //   }\r\n\r\n    //   const currentNode = node.data;\r\n    //   // 若该节点已有 children，说明已请求过，直接解析现有 children\r\n    //   if (currentNode.children) {\r\n    //     return resolve(currentNode.children);\r\n    //   }\r\n    //   const parentId = currentNode.id;\r\n    //   treeHazard({ parentId })\r\n    //     .then((res) => {\r\n    //       if (res.code === 200) {\r\n    //         const children = res.rows.map((item) => ({\r\n    //           id: item.hazardId,\r\n    //           label: item.hazardName,\r\n    //           // 根据子节点是否存在判断是否为叶子节点\r\n    //           isLeaf: !item.children || item.children.length === 0,\r\n    //         }));\r\n    //         // 将子节点数据赋值给当前节点的 children 属性\r\n    //         currentNode.children = children;\r\n    //         resolve(children);\r\n    //       } else {\r\n    //         resolve([]);\r\n    //       }\r\n    //     })\r\n    //     .catch(() => {\r\n    //       resolve([]);\r\n    //     });\r\n    // },\r\n    // transformChildren(children) {\r\n    //   if (!children || children.length === 0) return [];\r\n    //   return children.map((child) => ({\r\n    //     label: child.hazardName,\r\n    //     id: child.hazardId,\r\n    //     children: this.transformChildren(child.children),\r\n    //   }));\r\n    // },\r\n    // handleNodeClick(nodeData, node) {\r\n    //   console.log(node, \"nodeData\");\r\n    //   // this.queryParams.hazardId = nodeData.id;\r\n    //   if (node.isLeaf) {\r\n    //     this.selectId = nodeData.id;\r\n    //     this.form.hazardCategory = nodeData.id; // 绑定ID而非label\r\n    //     this.$refs.selectRef.blur();\r\n    //   }\r\n    // },\r\n    // setLabelRef(el, node) {\r\n    //   if (el) {\r\n    //     this.labelRefs.set(node.id || node.label, el);\r\n    //   }\r\n    // },\r\n    /** 查询隐患问题库列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listHazard(this.queryParams).then((res) => {\r\n        const data = res.rows;\r\n        // data.sort((a, b) => {\r\n        //   return new Date(b.createTime) - new Date(a.createTime);\r\n        // });\r\n        this.hazardList = data;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        hazardId: null,\r\n        hazardName: null,\r\n        parentId: null,\r\n        hazardCode: null,\r\n        orderNum: null,\r\n        target: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        examid: null,\r\n        categoryType: null,\r\n        investigationContent: null,\r\n        hazardLevel: null,\r\n        hazardStatus: null,\r\n        rectificationDeadline: null,\r\n        rectificationRequirements: null,\r\n        hazardIdentification: null,\r\n        accidentHazard: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.parentId = this.selectId;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item, index) => {\r\n        const serialNumber =\r\n          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +\r\n          selection.indexOf(item) +\r\n          1;\r\n        return {\r\n          serialNumber,\r\n          hazardId: item.hazardId,\r\n        };\r\n      });\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加隐患问题库\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const hazardId = row.hazardId || this.ids;\r\n      getHazard(hazardId).then((res) => {\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改隐患问题库\";\r\n\r\n        console.log('获取到的表单数据:', this.form);\r\n        console.log('隐患类别值:', this.form.hazardCategory);\r\n\r\n        // 延迟设置，确保对话框和组件都已经渲染完成\r\n        setTimeout(() => {\r\n          this.setHazardCategoryEcho();\r\n        }, 500);\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.hazardId != null) {\r\n            updateHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            // this.form.parentId = this.selectId;\r\n            this.form.hazardStatus = \"启用\";\r\n            // if (this.form.parentId == null) {\r\n            //   this.$modal.msgError(\r\n            //     \"请先在左侧树形结构中选择具体的隐患类别，然后再新增隐患问题库\"\r\n            //   );\r\n            //   return;\r\n            // }\r\n            addHazard(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      let hazardIds;\r\n      if (row && row.hazardId) {\r\n        // 单行删除\r\n        hazardIds = [row.hazardId];\r\n      } else {\r\n        // 多行删除，从this.ids中提取hazardId\r\n        hazardIds = this.ids.map((item) => item.hazardId);\r\n      }\r\n\r\n      this.$modal\r\n        .confirm(\r\n          '是否确认删除隐患问题库编号为\"' + hazardIds.join(\",\") + '\"的数据项？'\r\n        )\r\n        .then(function () {\r\n          return delHazard(hazardIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/hazard/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `hazard_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.title {\r\n  font-size: 16px;\r\n  cursor: pointer;\r\n}\r\n\r\n::v-deep .el-tree-node__label {\r\n  display: inline-block;\r\n  max-width: 180px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n::v-deep .el-tree-node.is-current > .el-tree-node__content {\r\n  background-color: #f0f7ff;\r\n  color: #409eff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 事故隐患标签样式 */\r\n.hazard-tag {\r\n  color: white !important;\r\n  border: none !important;\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n  min-height: 20px;\r\n}\r\n\r\n/* 一般隐患 - 黄色背景 */\r\n.hazard-general {\r\n  background-color: #f39c12 !important;\r\n}\r\n\r\n/* 常见隐患 - 红色背景 */\r\n.hazard-common {\r\n  background-color: #e74c3c !important;\r\n}\r\n</style>\r\n"]}]}