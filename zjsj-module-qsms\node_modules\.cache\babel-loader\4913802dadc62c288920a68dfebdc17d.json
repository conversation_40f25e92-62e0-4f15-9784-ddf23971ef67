{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue", "mtime": 1757425570923}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\babel.config.js", "mtime": 1756724494465}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "value", "type", "String", "Number", "default", "placeholder", "categoryList", "Array", "disabled", "Boolean", "data", "displayValue", "treeProps", "label", "children", "computed", "treeData", "processTreeData", "watch", "immediate", "handler", "newVal", "_this", "$nextTick", "setTreeSelection", "_this2", "mounted", "_this3", "methods", "handleVisibleChange", "isVisible", "_this4", "$refs", "hazardTree", "selectedNode", "findNodeById", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "expandToNode", "handleNodeClick", "node", "console", "log", "length", "expanded", "selectNode", "_this5", "$emit", "id", "closeDropdown", "handleInput", "handleSelectChange", "handleClear", "hazardSelectRef", "blur", "_this6", "isArray", "map", "item", "_objectSpread2", "hazardId", "hazardName", "nodes", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "found", "err", "e", "f", "targetNode", "_this7", "expandParents", "parent", "findParentNode", "setExpandedKey", "childId", "arguments", "undefined", "_iterator2", "_step2", "findNodeByLabel", "_iterator3", "_step3"], "sources": ["src/views/components/selectHazardCategoryTree.vue"], "sourcesContent": ["<template>\n  <el-select\n    ref=\"hazardSelectRef\"\n    :value=\"displayValue\"\n    :placeholder=\"placeholder\"\n    :disabled=\"disabled\"\n    clearable\n    filterable\n    style=\"width: 100%\"\n    class=\"hazard-category-select\"\n    popper-class=\"hazard-category-dropdown\"\n    @input=\"handleInput\"\n    @change=\"handleSelectChange\"\n    @visible-change=\"handleVisibleChange\"\n    @clear=\"handleClear\"\n  >\n    <el-option :value=\"displayValue\" style=\"height: auto; padding: 0\">\n      <el-tree\n        ref=\"hazardTree\"\n        :data=\"treeData\"\n        :props=\"treeProps\"\n        :expand-on-click-node=\"false\"\n        node-key=\"id\"\n        highlight-current\n        style=\"padding: 5px 0\"\n        @node-click=\"handleNodeClick\"\n      >\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\n          <span class=\"tree-label\">{{ node.label }}</span>\n        </span>\n      </el-tree>\n    </el-option>\n  </el-select>\n</template>\n\n<script>\nexport default {\n  name: 'SelectHazardCategoryTree',\n  props: {\n    value: {\n      type: [String, Number],\n      default: '',\n    },\n    placeholder: {\n      type: String,\n      default: '请选择隐患类别',\n    },\n    categoryList: {\n      type: Array,\n      default: () => [],\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    return {\n      displayValue: '',\n      treeProps: {\n        label: 'label',\n        children: 'children',\n      },\n    }\n  },\n\n  computed: {\n    treeData() {\n      return this.processTreeData(this.categoryList)\n    },\n  },\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        this.displayValue = newVal || ''\n        // 当值改变时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n    categoryList: {\n      immediate: true,\n      handler() {\n        // 当分类列表变化时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n  },\n\n  mounted() {\n    // 组件挂载后初始化设置\n    this.$nextTick(() => {\n      this.setTreeSelection()\n    })\n  },\n\n  methods: {\n    handleVisibleChange(isVisible) {\n      if (isVisible) {\n        // 下拉框打开时，设置当前选中的节点\n        this.$nextTick(() => {\n          if (this.value && this.$refs.hazardTree) {\n            const selectedNode = this.findNodeById(this.treeData, this.value)\n            if (selectedNode) {\n              this.$refs.hazardTree.setCurrentKey(this.value)\n              this.expandToNode(selectedNode)\n            }\n          }\n        })\n      }\n    },\n\n    handleNodeClick(data, node) {\n      console.log('点击隐患类别节点', data, node)\n      \n      // 如果有子节点，切换展开状态\n      if (data.children && data.children.length > 0) {\n        node.expanded = !node.expanded\n        return\n      }\n      \n      // 叶子节点，触发选择\n      this.selectNode(data)\n    },\n\n    selectNode(data) {\n      this.displayValue = data.label\n      this.$emit('input', data.label)\n      \n      // 触发 change 事件，传递完整的节点信息\n      this.$emit('change', {\n        id: data.id,\n        label: data.label,\n        value: data.label,\n      })\n      \n      // 更新树的高亮选择\n      this.$refs.hazardTree.setCurrentKey(data.id)\n      \n      // 关闭下拉框\n      this.$nextTick(() => {\n        this.closeDropdown()\n      })\n    },\n\n    handleInput(value) {\n      this.displayValue = value\n      this.$emit('input', value)\n    },\n\n    handleSelectChange(value) {\n      if (!value) {\n        this.$emit('change', null)\n      }\n    },\n\n    handleClear() {\n      this.displayValue = ''\n      this.$emit('input', '')\n      this.$emit('change', null)\n    },\n\n    closeDropdown() {\n      this.$refs.hazardSelectRef.blur()\n    },\n\n    // 处理树形数据\n    processTreeData(data) {\n      if (!data || !Array.isArray(data)) return []\n      \n      return data.map((item) => ({\n        ...item,\n        id: item.id || item.hazardId,\n        label: item.label || item.hazardName || item.name,\n        children: item.children ? this.processTreeData(item.children) : [],\n      }))\n    },\n\n    // 根据ID查找节点\n    findNodeById(nodes, id) {\n      for (const node of nodes) {\n        if (node.id === id) return node\n        if (node.children && node.children.length) {\n          const found = this.findNodeById(node.children, id)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 展开到指定节点\n    expandToNode(targetNode) {\n      if (!targetNode || !this.$refs.hazardTree) return\n      \n      // 递归展开父节点\n      const expandParents = (node) => {\n        const parent = this.findParentNode(this.treeData, node.id)\n        if (parent) {\n          this.$refs.hazardTree.setExpandedKey(parent.id, true)\n          expandParents(parent)\n        }\n      }\n      \n      expandParents(targetNode)\n    },\n\n    // 查找父节点\n    findParentNode(nodes, childId, parent = null) {\n      for (const node of nodes) {\n        if (node.id === childId) {\n          return parent\n        }\n        if (node.children && node.children.length) {\n          const found = this.findParentNode(node.children, childId, node)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 设置树的选中状态\n    setTreeSelection() {\n      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {\n        return\n      }\n\n      // 根据displayValue查找对应的节点\n      const selectedNode = this.findNodeByLabel(this.treeData, this.value)\n      if (selectedNode) {\n        this.$refs.hazardTree.setCurrentKey(selectedNode.id)\n        this.expandToNode(selectedNode)\n      }\n    },\n\n    // 根据标签查找节点\n    findNodeByLabel(nodes, label) {\n      for (const node of nodes) {\n        if (node.label === label) {\n          return node\n        }\n        if (node.children && node.children.length) {\n          const found = this.findNodeByLabel(node.children, label)\n          if (found) return found\n        }\n      }\n      return null\n    },\n  },\n}\n</script>\n\n<style scoped>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.tree-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n::v-deep .hazard-category-dropdown {\n  max-height: 400px;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content {\n  height: auto;\n  padding: 4px 0;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content:hover {\n  background-color: #f5f7fa;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #f0f7ff;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAoCA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,WAAA;MACAJ,IAAA,EAAAC,MAAA;MACAE,OAAA;IACA;IACAE,YAAA;MACAL,IAAA,EAAAM,KAAA;MACAH,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;IACAI,QAAA;MACAP,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;EACA;EAEAM,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,SAAA;QACAC,KAAA;QACAC,QAAA;MACA;IACA;EACA;EAEAC,QAAA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,eAAA,MAAAX,YAAA;IACA;EACA;EAEAY,KAAA;IACAlB,KAAA;MACAmB,SAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,KAAA;QACA,KAAAX,YAAA,GAAAU,MAAA;QACA;QACA,KAAAE,SAAA;UACAD,KAAA,CAAAE,gBAAA;QACA;MACA;IACA;IACAlB,YAAA;MACAa,SAAA;MACAC,OAAA,WAAAA,QAAA;QAAA,IAAAK,MAAA;QACA;QACA,KAAAF,SAAA;UACAE,MAAA,CAAAD,gBAAA;QACA;MACA;IACA;EACA;EAEAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA;IACA,KAAAJ,SAAA;MACAI,MAAA,CAAAH,gBAAA;IACA;EACA;EAEAI,OAAA;IACAC,mBAAA,WAAAA,oBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,SAAA;QACA;QACA,KAAAP,SAAA;UACA,IAAAQ,MAAA,CAAA/B,KAAA,IAAA+B,MAAA,CAAAC,KAAA,CAAAC,UAAA;YACA,IAAAC,YAAA,GAAAH,MAAA,CAAAI,YAAA,CAAAJ,MAAA,CAAAf,QAAA,EAAAe,MAAA,CAAA/B,KAAA;YACA,IAAAkC,YAAA;cACAH,MAAA,CAAAC,KAAA,CAAAC,UAAA,CAAAG,aAAA,CAAAL,MAAA,CAAA/B,KAAA;cACA+B,MAAA,CAAAM,YAAA,CAAAH,YAAA;YACA;UACA;QACA;MACA;IACA;IAEAI,eAAA,WAAAA,gBAAA5B,IAAA,EAAA6B,IAAA;MACAC,OAAA,CAAAC,GAAA,aAAA/B,IAAA,EAAA6B,IAAA;;MAEA;MACA,IAAA7B,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAAI,QAAA,CAAA4B,MAAA;QACAH,IAAA,CAAAI,QAAA,IAAAJ,IAAA,CAAAI,QAAA;QACA;MACA;;MAEA;MACA,KAAAC,UAAA,CAAAlC,IAAA;IACA;IAEAkC,UAAA,WAAAA,WAAAlC,IAAA;MAAA,IAAAmC,MAAA;MACA,KAAAlC,YAAA,GAAAD,IAAA,CAAAG,KAAA;MACA,KAAAiC,KAAA,UAAApC,IAAA,CAAAG,KAAA;;MAEA;MACA,KAAAiC,KAAA;QACAC,EAAA,EAAArC,IAAA,CAAAqC,EAAA;QACAlC,KAAA,EAAAH,IAAA,CAAAG,KAAA;QACAb,KAAA,EAAAU,IAAA,CAAAG;MACA;;MAEA;MACA,KAAAmB,KAAA,CAAAC,UAAA,CAAAG,aAAA,CAAA1B,IAAA,CAAAqC,EAAA;;MAEA;MACA,KAAAxB,SAAA;QACAsB,MAAA,CAAAG,aAAA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAAjD,KAAA;MACA,KAAAW,YAAA,GAAAX,KAAA;MACA,KAAA8C,KAAA,UAAA9C,KAAA;IACA;IAEAkD,kBAAA,WAAAA,mBAAAlD,KAAA;MACA,KAAAA,KAAA;QACA,KAAA8C,KAAA;MACA;IACA;IAEAK,WAAA,WAAAA,YAAA;MACA,KAAAxC,YAAA;MACA,KAAAmC,KAAA;MACA,KAAAA,KAAA;IACA;IAEAE,aAAA,WAAAA,cAAA;MACA,KAAAhB,KAAA,CAAAoB,eAAA,CAAAC,IAAA;IACA;IAEA;IACApC,eAAA,WAAAA,gBAAAP,IAAA;MAAA,IAAA4C,MAAA;MACA,KAAA5C,IAAA,KAAAH,KAAA,CAAAgD,OAAA,CAAA7C,IAAA;MAEA,OAAAA,IAAA,CAAA8C,GAAA,WAAAC,IAAA;QAAA,WAAAC,cAAA,CAAAtD,OAAA,MAAAsD,cAAA,CAAAtD,OAAA,MACAqD,IAAA;UACAV,EAAA,EAAAU,IAAA,CAAAV,EAAA,IAAAU,IAAA,CAAAE,QAAA;UACA9C,KAAA,EAAA4C,IAAA,CAAA5C,KAAA,IAAA4C,IAAA,CAAAG,UAAA,IAAAH,IAAA,CAAA3D,IAAA;UACAgB,QAAA,EAAA2C,IAAA,CAAA3C,QAAA,GAAAwC,MAAA,CAAArC,eAAA,CAAAwC,IAAA,CAAA3C,QAAA;QAAA;MAAA,CACA;IACA;IAEA;IACAqB,YAAA,WAAAA,aAAA0B,KAAA,EAAAd,EAAA;MAAA,IAAAe,SAAA,OAAAC,2BAAA,CAAA3D,OAAA,EACAyD,KAAA;QAAAG,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA5B,IAAA,GAAAyB,KAAA,CAAAhE,KAAA;UACA,IAAAuC,IAAA,CAAAQ,EAAA,KAAAA,EAAA,SAAAR,IAAA;UACA,IAAAA,IAAA,CAAAzB,QAAA,IAAAyB,IAAA,CAAAzB,QAAA,CAAA4B,MAAA;YACA,IAAA0B,KAAA,QAAAjC,YAAA,CAAAI,IAAA,CAAAzB,QAAA,EAAAiC,EAAA;YACA,IAAAqB,KAAA,SAAAA,KAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;MAAA;QAAAP,SAAA,CAAAS,CAAA;MAAA;MACA;IACA;IAEA;IACAlC,YAAA,WAAAA,aAAAmC,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAD,UAAA,UAAAxC,KAAA,CAAAC,UAAA;;MAEA;MACA,IAAAyC,cAAA,YAAAA,cAAAnC,IAAA;QACA,IAAAoC,MAAA,GAAAF,MAAA,CAAAG,cAAA,CAAAH,MAAA,CAAAzD,QAAA,EAAAuB,IAAA,CAAAQ,EAAA;QACA,IAAA4B,MAAA;UACAF,MAAA,CAAAzC,KAAA,CAAAC,UAAA,CAAA4C,cAAA,CAAAF,MAAA,CAAA5B,EAAA;UACA2B,cAAA,CAAAC,MAAA;QACA;MACA;MAEAD,cAAA,CAAAF,UAAA;IACA;IAEA;IACAI,cAAA,WAAAA,eAAAf,KAAA,EAAAiB,OAAA;MAAA,IAAAH,MAAA,GAAAI,SAAA,CAAArC,MAAA,QAAAqC,SAAA,QAAAC,SAAA,GAAAD,SAAA;MAAA,IAAAE,UAAA,OAAAlB,2BAAA,CAAA3D,OAAA,EACAyD,KAAA;QAAAqB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAhB,CAAA,MAAAiB,MAAA,GAAAD,UAAA,CAAAf,CAAA,IAAAC,IAAA;UAAA,IAAA5B,IAAA,GAAA2C,MAAA,CAAAlF,KAAA;UACA,IAAAuC,IAAA,CAAAQ,EAAA,KAAA+B,OAAA;YACA,OAAAH,MAAA;UACA;UACA,IAAApC,IAAA,CAAAzB,QAAA,IAAAyB,IAAA,CAAAzB,QAAA,CAAA4B,MAAA;YACA,IAAA0B,KAAA,QAAAQ,cAAA,CAAArC,IAAA,CAAAzB,QAAA,EAAAgE,OAAA,EAAAvC,IAAA;YACA,IAAA6B,KAAA,SAAAA,KAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAY,UAAA,CAAAX,CAAA,CAAAD,GAAA;MAAA;QAAAY,UAAA,CAAAV,CAAA;MAAA;MACA;IACA;IAEA;IACA/C,gBAAA,WAAAA,iBAAA;MACA,UAAAxB,KAAA,UAAAgC,KAAA,CAAAC,UAAA,UAAAjB,QAAA,CAAA0B,MAAA;QACA;MACA;;MAEA;MACA,IAAAR,YAAA,QAAAiD,eAAA,MAAAnE,QAAA,OAAAhB,KAAA;MACA,IAAAkC,YAAA;QACA,KAAAF,KAAA,CAAAC,UAAA,CAAAG,aAAA,CAAAF,YAAA,CAAAa,EAAA;QACA,KAAAV,YAAA,CAAAH,YAAA;MACA;IACA;IAEA;IACAiD,eAAA,WAAAA,gBAAAtB,KAAA,EAAAhD,KAAA;MAAA,IAAAuE,UAAA,OAAArB,2BAAA,CAAA3D,OAAA,EACAyD,KAAA;QAAAwB,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAnB,CAAA,MAAAoB,MAAA,GAAAD,UAAA,CAAAlB,CAAA,IAAAC,IAAA;UAAA,IAAA5B,IAAA,GAAA8C,MAAA,CAAArF,KAAA;UACA,IAAAuC,IAAA,CAAA1B,KAAA,KAAAA,KAAA;YACA,OAAA0B,IAAA;UACA;UACA,IAAAA,IAAA,CAAAzB,QAAA,IAAAyB,IAAA,CAAAzB,QAAA,CAAA4B,MAAA;YACA,IAAA0B,KAAA,QAAAe,eAAA,CAAA5C,IAAA,CAAAzB,QAAA,EAAAD,KAAA;YACA,IAAAuD,KAAA,SAAAA,KAAA;UACA;QACA;MAAA,SAAAC,GAAA;QAAAe,UAAA,CAAAd,CAAA,CAAAD,GAAA;MAAA;QAAAe,UAAA,CAAAb,CAAA;MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}