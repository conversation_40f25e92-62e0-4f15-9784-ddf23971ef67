{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjProjectInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\zjProjectInfo\\index.vue", "mtime": 1757424290730}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0WmpQcm9qZWN0SW5mbywNCiAgZ2V0WmpQcm9qZWN0SW5mbywNCiAgZGVsWmpQcm9qZWN0SW5mbywNCiAgYWRkWmpQcm9qZWN0SW5mbywNCiAgdXBkYXRlWmpQcm9qZWN0SW5mbywNCn0gZnJvbSAiQC9hcGkvaW5zcGVjdGlvbi96alByb2plY3RJbmZvIjsNCmltcG9ydCB7IGdldERpY3RzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7DQppbXBvcnQgeyBnZXRDb21wYW55TGlzdCwgcXVlcnl0cmVlIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2luZm8iOw0KLy8gaW1wb3J0IG9yZ1RyZWUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy9vcmdUcmVlLnZ1ZSI7DQppbXBvcnQgc2VsZWN0UGVvcGxlVHJlZSBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvc2VsZWN0UGVvcGxlVHJlZS52dWUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJaalByb2plY3RJbmZvIiwNCiAgZGljdHM6IFsiempfY29uc3RydWN0X3N0YXR1cyJdLA0KICBjb21wb25lbnRzOiB7DQogICAgLy8gb3JnVHJlZSwNCiAgICBzZWxlY3RQZW9wbGVUcmVlLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6aG555uu5L+h5oGv6KGo5qC85pWw5o2uDQogICAgICB6alByb2plY3RJbmZvTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICBvcGVuVmlldzogZmFsc2UsDQoNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICBwYXJlbnRPcmdJZDogbnVsbCwNCiAgICAgICAgb3JnSWQ6IG51bGwsDQogICAgICAgIGJlbG9uZ0NvbXBhbnk6IG51bGwsDQogICAgICAgIG9yZ1R5cGU6IG51bGwsDQogICAgICAgIGNvZGU6IG51bGwsDQogICAgICAgIG5hbWU6IG51bGwsDQogICAgICAgIHNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgc3luY0NvZGU6IG51bGwsDQogICAgICAgIG1hbmFnZXI6IG51bGwsDQogICAgICAgIG1hbmFnZXJNb2JpbGU6IG51bGwsDQogICAgICAgIGNvbnN0cnVjdFN0YXR1czogIjEiLA0KICAgICAgICBjb25zdHJ1Y3RUeXBlOiBudWxsLA0KICAgICAgICBjb25zdHJ1Y3RQdXJwb3NlOiBudWxsLA0KICAgICAgICBzdHJ1Y3RUeXBlOiBudWxsLA0KICAgICAgICBwbGFuU3RhcnQ6IG51bGwsDQogICAgICAgIHBsYW5FbmQ6IG51bGwsDQogICAgICAgIGFjdHVhbFN0YXJ0OiBudWxsLA0KICAgICAgICBhcmVhOiBudWxsLA0KICAgICAgICBsb2NhdGlvbjogbnVsbCwNCiAgICAgICAgbG9uZ2l0dWRlOiBudWxsLA0KICAgICAgICBsYXRpdHVkZTogbnVsbCwNCiAgICAgICAgYmlkZGluZ1VuaXQ6IG51bGwsDQogICAgICAgIGNvbnN0cnVjdFVuaXQ6IG51bGwsDQogICAgICAgIHN1cGVydmlzaW5nVW5pdDogbnVsbCwNCiAgICAgICAgdXBkYXRlRGF0ZTogbnVsbCwNCiAgICAgICAgZGVsZXRlZDogbnVsbCwNCiAgICAgICAgcHJvdmluY2U6IG51bGwsDQogICAgICAgIGNpdHk6IG51bGwsDQogICAgICAgIGRpc3RyaWN0OiBudWxsLA0KICAgICAgICBwcm9qZWN0Q29kZTogbnVsbCwNCiAgICAgICAgaWQ6IG51bGwsDQogICAgICB9LA0KDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBuYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumhueebruWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBzaG9ydE5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aG555uu566A56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIG1hbmFnZXI6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aG555uu57uP55CG5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIG1hbmFnZXJNb2JpbGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6aG555uu57uP55CG55S16K+d5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGJlbG9uZ0NvbXBhbnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6YOo6Zeo5ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnN0cnVjdFN0YXR1czogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLpobnnm67nirbmgIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICBwbGFjZWhvbGRlcjogIuivt+i+k+WFpeaJgOWxnuWNleS9jSIsDQogICAgICBjb25zdHJ1Y3RTdGF0dXNEaWN0OiBbXSwNCiAgICAgIGNvbXBhbnlMaXN0OiBbXSwNCiAgICAgIGFsbENvbXBhbnlMaXN0OiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0Q29uc3RydWN0U3RhdHVzRGljdCgpOw0KICAgIHRoaXMuZ2V0Q29tcGFueUxpc3QoKTsNCiAgICB0aGlzLmdldEFsbENvbXBhbnlMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVDaGFuZ2Uoc2VsZWN0ZWRJdGVtKSB7DQogICAgICBpZiAoc2VsZWN0ZWRJdGVtLmlkID09ICI2NTQ0NzA3MTYxOTg5MTIiKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+mAieaLqeS4i+e6p+WNleS9jSIpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAoc2VsZWN0ZWRJdGVtKSB7DQogICAgICAgIHRoaXMuZm9ybS5iZWxvbmdDb21wYW55ID0gc2VsZWN0ZWRJdGVtLmxhYmVsOw0KDQogICAgICAgIHRoaXMuZm9ybS5vcmdJZCA9IHNlbGVjdGVkSXRlbS5pZDsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZm9ybS5iZWxvbmdDb21wYW55ID0gbnVsbDsNCiAgICAgICAgdGhpcy5mb3JtLm9yZ0lkID0gbnVsbDsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldEFsbENvbXBhbnlMaXN0KCkgew0KICAgICAgcXVlcnl0cmVlKCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmFsbENvbXBhbnlMaXN0ID0gcmVzLmRhdGE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0Q29tcGFueUxpc3QoKSB7DQogICAgICBnZXRDb21wYW55TGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5jb21wYW55TGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldENvbnN0cnVjdFN0YXR1c0RpY3QoKSB7DQogICAgICBnZXREaWN0cygiempfY29uc3RydWN0X3N0YXR1cyIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICByZXMuZGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgdGhpcy5jb25zdHJ1Y3RTdGF0dXNEaWN0W2l0ZW0uZGljdFZhbHVlXSA9IGl0ZW0uZGljdExhYmVsOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0Q29uc3RydWN0U3RhdHVzKHN0YXR1cykgew0KICAgICAgcmV0dXJuIHRoaXMuY29uc3RydWN0U3RhdHVzRGljdFtzdGF0dXNdOw0KICAgIH0sDQoNCiAgICBoYW5kbGVPcmdUcmVlTm9kZUNsaWNrKG5vZGUpIHsNCiAgICAgIC8vIOWcqOi/memHjOWkhOeQhuaOpeaUtuWIsOeahOWtkOe7hOS7tuaVsOaNrg0KICAgICAgY29uc29sZS5sb2coIuaOpeaUtuWIsOWtkOe7hOS7tuaVsOaNrjoiLCBub2RlLmlkKTsNCiAgICAgIC8vIOWPr+S7peagueaNrm5vZGVEYXRh5pu05paw5p+l6K+i5Y+C5pWw5oiW5YW25LuW54q25oCBDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhcmVudE9yZ0lkID0gbm9kZS5pZDsgLy8g5YGH6K6+bm9kZURhdGHkuK3mnIlpZOWtl+autQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOyAvLyDop6blj5Hmn6Xor6INCiAgICB9LA0KDQogICAgLyoqIOafpeivoumhueebruS/oeaBr+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFpqUHJvamVjdEluZm8odGhpcy5xdWVyeVBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuempQcm9qZWN0SW5mb0xpc3QgPSByZXMucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBvcmdJZDogbnVsbCwNCiAgICAgICAgYmVsb25nQ29tcGFueTogbnVsbCwNCiAgICAgICAgcGFyZW50T3JnSWQ6IG51bGwsDQogICAgICAgIG9yZ1R5cGU6IG51bGwsDQogICAgICAgIGNvZGU6IG51bGwsDQogICAgICAgIG5hbWU6IG51bGwsDQogICAgICAgIHNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgc3luY0NvZGU6IG51bGwsDQogICAgICAgIG1hbmFnZXI6IG51bGwsDQogICAgICAgIG1hbmFnZXJNb2JpbGU6IG51bGwsDQogICAgICAgIGNvbnN0cnVjdFN0YXR1czogbnVsbCwNCiAgICAgICAgY29uc3RydWN0VHlwZTogbnVsbCwNCiAgICAgICAgY29uc3RydWN0UHVycG9zZTogbnVsbCwNCiAgICAgICAgc3RydWN0VHlwZTogbnVsbCwNCiAgICAgICAgcGxhblN0YXJ0OiBudWxsLA0KICAgICAgICBwbGFuRW5kOiBudWxsLA0KICAgICAgICBhY3R1YWxTdGFydDogbnVsbCwNCiAgICAgICAgYXJlYTogbnVsbCwNCiAgICAgICAgbG9jYXRpb246IG51bGwsDQogICAgICAgIGxvbmdpdHVkZTogbnVsbCwNCiAgICAgICAgbGF0aXR1ZGU6IG51bGwsDQogICAgICAgIGJpZGRpbmdVbml0OiBudWxsLA0KICAgICAgICBjb25zdHJ1Y3RVbml0OiBudWxsLA0KICAgICAgICBzdXBlcnZpc2luZ1VuaXQ6IG51bGwsDQogICAgICAgIHVwZGF0ZURhdGU6IG51bGwsDQogICAgICAgIGRlbGV0ZWQ6IG51bGwsDQogICAgICAgIHByb3ZpbmNlOiBudWxsLA0KICAgICAgICBjaXR5OiBudWxsLA0KICAgICAgICBkaXN0cmljdDogbnVsbCwNCiAgICAgICAgcHJvamVjdENvZGU6IG51bGwsDQogICAgICAgIGlkOiBudWxsLA0KICAgICAgfTsNCiAgICAgIHRoaXMuYmVsb25nQ29tcGFueSA9IG51bGw7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQoNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyZW50T3JnSWQgPSAiIjsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcCgoaXRlbSkgPT4gaXRlbS5pZCk7DQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6aG555uu5L+h5oGvIjsNCiAgICB9LA0KICAgIGhhbmRsZURldGFpbChyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0WmpQcm9qZWN0SW5mbyhpZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlcy5kYXRhOw0KICAgICAgICB0aGlzLm9wZW5WaWV3ID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IGlkID0gcm93LmlkIHx8IHRoaXMuaWRzOw0KICAgICAgZ2V0WmpQcm9qZWN0SW5mbyhpZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlcy5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUuemhueebruS/oeaBryI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVpqUHJvamVjdEluZm8odGhpcy5mb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRDb21wYW55TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFpqUHJvamVjdEluZm8odGhpcy5mb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRDb21wYW55TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpZHMgPSByb3cuaWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRtb2RhbA0KICAgICAgICAuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6aG555uu5L+h5oGv57yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsWmpQcm9qZWN0SW5mbyhpZHMpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgNCiAgICAgICAgImluc3BlY3Rpb24vempQcm9qZWN0SW5mby9leHBvcnQiLA0KICAgICAgICB7DQogICAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywNCiAgICAgICAgfSwNCiAgICAgICAgYHpqUHJvamVjdEluZm9fJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGANCiAgICAgICk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA80BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/zjProjectInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <!-- <el-col :span=\"4\">\r\n        <orgTree :type=\"'1'\" @nodeClick=\"handleOrgTreeNodeClick\"></orgTree>\r\n      </el-col> -->\r\n      <el-col :span=\"24\" style=\"margin-left: 2px\">\r\n        <el-form\r\n          v-show=\"showSearch\"\r\n          ref=\"queryForm\"\r\n          :model=\"queryParams\"\r\n          size=\"small\"\r\n          :inline=\"true\"\r\n          label-width=\"68px\"\r\n        >\r\n          <el-form-item label=\"项目名称\" prop=\"name\">\r\n            <el-input\r\n              v-model=\"queryParams.name\"\r\n              placeholder=\"请输入项目名称\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!-- 所属公司 -->\r\n          <el-form-item label=\"所属公司\" prop=\"belongCompany\">\r\n            <el-select\r\n              v-model=\"queryParams.belongCompany\"\r\n              placeholder=\"请选择所属公司\"\r\n              style=\"width: 300px\"\r\n              filterable\r\n              clearable\r\n            >\r\n              <el-option\r\n                v-for=\"item in companyList\"\r\n                :key=\"item.belongCompany\"\r\n                :label=\"item.belongCompany\"\r\n                :value=\"item.belongCompany\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 项目状态 -->\r\n          <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n            <el-select\r\n              v-model=\"queryParams.constructStatus\"\r\n              placeholder=\"请选择项目状态\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.zj_construct_status\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n        <el-input\r\n          v-model=\"queryParams.shortName\"\r\n          placeholder=\"请输入项目简称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目同步编码\" prop=\"syncCode\">\r\n        <el-input\r\n          v-model=\"queryParams.syncCode\"\r\n          placeholder=\"请输入项目同步编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n          <!-- <el-form-item label=\"项目经理\" prop=\"manager\">\r\n            <el-input v-model=\"queryParams.manager\" placeholder=\"请输入项目经理\" clearable style=\"width: 300px\"\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item> -->\r\n          <!-- <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n        <el-input\r\n          v-model=\"queryParams.managerMobile\"\r\n          placeholder=\"请输入项目经理电话\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n        <el-input\r\n          v-model=\"queryParams.constructPurpose\"\r\n          placeholder=\"请输入工程用途\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.planStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择合同工期开始\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.planEnd\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择合同工期结束\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.actualStart\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择实际工期开始\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n        <el-input\r\n          v-model=\"queryParams.area\"\r\n          placeholder=\"请输入建筑面积(平方米)\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"工程地点\" prop=\"location\">\r\n        <el-input\r\n          v-model=\"queryParams.location\"\r\n          placeholder=\"请输入工程地点\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"经度\" prop=\"longitude\">\r\n        <el-input\r\n          v-model=\"queryParams.longitude\"\r\n          placeholder=\"请输入经度\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"纬度\" prop=\"latitude\">\r\n        <el-input\r\n          v-model=\"queryParams.latitude\"\r\n          placeholder=\"请输入纬度\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.biddingUnit\"\r\n          placeholder=\"请输入中标单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.constructUnit\"\r\n          placeholder=\"请输入建设单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n        <el-input\r\n          v-model=\"queryParams.supervisingUnit\"\r\n          placeholder=\"请输入监理单位\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.updateDate\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择更新日期\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n        <el-input\r\n          v-model=\"queryParams.deleted\"\r\n          placeholder=\"请输入删除状态\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"省份\" prop=\"province\">\r\n        <el-input\r\n          v-model=\"queryParams.province\"\r\n          placeholder=\"请输入省份\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"市\" prop=\"city\">\r\n        <el-input\r\n          v-model=\"queryParams.city\"\r\n          placeholder=\"请输入市\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区\" prop=\"district\">\r\n        <el-input\r\n          v-model=\"queryParams.district\"\r\n          placeholder=\"请输入区\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n        <el-input\r\n          v-model=\"queryParams.projectCode\"\r\n          placeholder=\"请输入项目编码\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n        <el-input\r\n          v-model=\"queryParams.id\"\r\n          placeholder=\"请输入组织全路径ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n          <el-form-item>\r\n            <el-button\r\n              type=\"primary\"\r\n              icon=\"el-icon-search\"\r\n              size=\"mini\"\r\n              @click=\"handleQuery\"\r\n              >搜索</el-button\r\n            >\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n              >重置</el-button\r\n            >\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:add']\"\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              >新增</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:edit']\"\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              >修改</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:remove']\"\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              >删除</el-button\r\n            >\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['inspection:zjProjectInfo:export']\"\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              >导出</el-button\r\n            >\r\n          </el-col>\r\n          <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-loading=\"loading\"\r\n          :data=\"zjProjectInfoList\"\r\n          height=\"calc(100vh - 230px)\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n          <!-- <el-table-column label=\"主键\" align=\"center\" prop=\"id\" /> -->\r\n          <!-- <el-table-column label=\"${comment}\" align=\"center\" prop=\"orgId\" />\r\n      <el-table-column label=\"${comment}\" align=\"center\" prop=\"parentOrgId\" /> -->\r\n          <!-- <el-table-column label=\"组织类型\" align=\"center\" prop=\"orgType\" /> -->\r\n          <el-table-column\r\n            label=\"项目名称\"\r\n            align=\"center\"\r\n            prop=\"name\"\r\n            width=\"200\"\r\n            show-overflow-tooltip\r\n          />\r\n          <!-- <el-table-column\r\n            label=\"编码\"\r\n            align=\"center\"\r\n            prop=\"code\"\r\n            width=\"200\"\r\n            show-overflow-tooltip\r\n          /> -->\r\n          <!-- 所属公司 -->\r\n          <el-table-column\r\n            label=\"所属公司\"\r\n            align=\"center\"\r\n            prop=\"belongCompany\"\r\n          />\r\n\r\n          <!-- <el-table-column label=\"项目简称\" align=\"center\" prop=\"shortName\" />\r\n      <el-table-column label=\"项目同步编码\" align=\"center\" prop=\"syncCode\" /> -->\r\n          <!-- <el-table-column label=\"手机\" align=\"center\" prop=\"managerMobile\" /> -->\r\n          <el-table-column\r\n            label=\"工程类别\"\r\n            align=\"center\"\r\n            prop=\"constructType\"\r\n            show-overflow-tooltip\r\n          />\r\n          <el-table-column\r\n            label=\"项目状态\"\r\n            align=\"center\"\r\n            prop=\"constructStatus\"\r\n          >\r\n            <template slot-scope=\"{ row }\">\r\n              {{ getConstructStatus(row.constructStatus) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"项目经理\" align=\"center\" prop=\"manager\" />\r\n\r\n          <!-- <el-table-column\r\n            label=\"所属单位\"\r\n            align=\"center\"\r\n            prop=\"biddingUnit\"\r\n            width=\"180\"\r\n            show-overflow-tooltip\r\n          >\r\n          </el-table-column> -->\r\n          <!-- <el-table-column\r\n        label=\"工程用途\"\r\n        align=\"center\"\r\n        prop=\"constructPurpose\"\r\n      /> -->\r\n          <!-- <el-table-column label=\"结构类型\" align=\"center\" prop=\"structType\" />\r\n      <el-table-column\r\n        label=\"合同工期开始\"\r\n        align=\"center\"\r\n        prop=\"planStart\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.planStart, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"合同工期结束\"\r\n        align=\"center\"\r\n        prop=\"planEnd\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.planEnd, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"实际工期开始\"\r\n        align=\"center\"\r\n        prop=\"actualStart\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.actualStart, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"建筑面积(平方米)\" align=\"center\" prop=\"area\" />\r\n      <el-table-column label=\"工程地点\" align=\"center\" prop=\"location\" />\r\n      <el-table-column label=\"经度\" align=\"center\" prop=\"longitude\" />\r\n      <el-table-column label=\"纬度\" align=\"center\" prop=\"latitude\" />\r\n      <el-table-column label=\"中标单位\" align=\"center\" prop=\"biddingUnit\" />\r\n      <el-table-column label=\"建设单位\" align=\"center\" prop=\"constructUnit\" />\r\n      <el-table-column label=\"监理单位\" align=\"center\" prop=\"supervisingUnit\" />\r\n      <el-table-column\r\n        label=\"更新日期\"\r\n        align=\"center\"\r\n        prop=\"updateDate\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.updateDate, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"删除状态\" align=\"center\" prop=\"deleted\" />\r\n      <el-table-column label=\"省份\" align=\"center\" prop=\"province\" />\r\n      <el-table-column label=\"市\" align=\"center\" prop=\"city\" />\r\n      <el-table-column label=\"区\" align=\"center\" prop=\"district\" />\r\n      <el-table-column label=\"项目编码\" align=\"center\" prop=\"projectCode\" />\r\n      <el-table-column label=\"组织全路径ID\" align=\"center\" prop=\"id\" /> -->\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            class-name=\"small-padding fixed-width\"\r\n            fixed=\"right\"\r\n            width=\"180\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:detail']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-view\"\r\n                @click=\"handleDetail(scope.row)\"\r\n                >查看</el-button\r\n              >\r\n\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:edit']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                >修改</el-button\r\n              >\r\n              <el-button\r\n                v-hasPermi=\"['inspection:zjProjectInfo:remove']\"\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                >删除</el-button\r\n              >\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total > 0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改项目信息对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"900px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <!-- <el-form-item label=\"${comment}\" prop=\"orgId\">\r\n          <el-input v-model=\"form.orgId\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"${comment}\" prop=\"parentOrgId\">\r\n          <el-input v-model=\"form.parentOrgId\" placeholder=\"请输入${comment}\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入项目名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n              <el-input v-model=\"form.shortName\" placeholder=\"请输入项目简称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"项目同步编码\" prop=\"syncCode\">\r\n          <el-input v-model=\"form.syncCode\" placeholder=\"请输入项目同步编码\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理\" prop=\"manager\">\r\n              <el-input v-model=\"form.manager\" placeholder=\"请输入项目经理\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n              <el-input v-model=\"form.managerMobile\" placeholder=\"请输入手机\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n              <el-select\r\n                v-model=\"form.constructStatus\"\r\n                placeholder=\"请选择项目状态\"\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in dict.type.zj_construct_status\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!-- 项目状态 -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <!-- 所属单位 -->\r\n            <el-form-item label=\"部门名称\" prop=\"belongCompany\">\r\n              <selectPeopleTree\r\n                v-model=\"form.belongCompany\"\r\n                :people-list=\"allCompanyList\"\r\n                placeholder=\"请搜索或选择部门名称\"\r\n                @change=\"handleChange\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程类别\" prop=\"constructType\">\r\n              <el-input\r\n                v-model=\"form.constructType\"\r\n                placeholder=\"请输入工程类别\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n              <el-input\r\n                v-model=\"form.constructPurpose\"\r\n                placeholder=\"请输入工程用途\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结构类型\" prop=\"structureType\">\r\n              <el-input\r\n                v-model=\"form.structureType\"\r\n                placeholder=\"请输入结构类型\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n              <el-date-picker\r\n                v-model=\"form.planStart\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择合同工期开始\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n              <el-date-picker\r\n                v-model=\"form.planEnd\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择合同工期结束\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n              <el-date-picker\r\n                v-model=\"form.actualStart\"\r\n                clearable\r\n                type=\"datetime\"\r\n                value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                placeholder=\"请选择实际工期开始\"\r\n                style=\"width: 100%\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n              <el-input\r\n                v-model=\"form.area\"\r\n                placeholder=\"请输入建筑面积(平方米)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程地点\" prop=\"location\">\r\n              <el-input v-model=\"form.location\" placeholder=\"请输入工程地点\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n              <el-input\r\n                v-model=\"form.biddingUnit\"\r\n                placeholder=\"请输入中标单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n              <el-input\r\n                v-model=\"form.constructUnit\"\r\n                placeholder=\"请输入建设单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n              <el-input\r\n                v-model=\"form.supervisingUnit\"\r\n                placeholder=\"请输入监理单位\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.updateDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择更新日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n          <el-input v-model=\"form.deleted\" placeholder=\"请输入删除状态\" />\r\n        </el-form-item> -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"省份\" prop=\"province\">\r\n              <el-input v-model=\"form.province\" placeholder=\"请输入省份\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"市\" prop=\"city\">\r\n              <el-input v-model=\"form.city\" placeholder=\"请输入市\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"区\" prop=\"district\">\r\n          <el-input v-model=\"form.district\" placeholder=\"请输入区\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n          <el-input v-model=\"form.projectCode\" placeholder=\"请输入项目编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n          <el-input v-model=\"form.id\" placeholder=\"请输入组织全路径ID\" />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"查看项目详情\"\r\n      :visible.sync=\"openView\"\r\n      width=\"900px\"\r\n      append-to-body\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"name\">\r\n              {{ form.name || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目简称\" prop=\"shortName\">\r\n              {{ form.shortName || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理\" prop=\"manager\">\r\n              {{ form.manager || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目经理电话\" prop=\"managerMobile\">\r\n              {{ form.managerMobile || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"项目状态\" prop=\"constructStatus\">\r\n              {{ getConstructStatus(form.constructStatus) || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属公司\" prop=\"belongCompany\">\r\n              {{ form.belongCompany || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程类别\" prop=\"constructType\">\r\n              {{ form.constructType || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n        </el-row>\r\n\r\n        <!-- 行业类别 -->\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程用途\" prop=\"constructPurpose\">\r\n              {{ form.constructPurpose || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"结构类型\" prop=\"structureType\">\r\n              {{ form.structureType || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期开始\" prop=\"planStart\">\r\n              {{ form.planStart || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合同工期结束\" prop=\"planEnd\">\r\n              {{ form.planEnd || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"实际工期开始\" prop=\"actualStart\">\r\n              {{ form.actualStart }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建筑面积(平方米)\" prop=\"area\">\r\n              {{ form.area || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"工程地点\" prop=\"location\">\r\n              {{ form.location || \"-\" }}\r\n            </el-form-item></el-col\r\n          >\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"中标单位\" prop=\"biddingUnit\">\r\n              {{ form.biddingUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"建设单位\" prop=\"constructUnit\">\r\n              {{ form.constructUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"监理单位\" prop=\"supervisingUnit\">\r\n              {{ form.supervisingUnit || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"省份\" prop=\"province\">\r\n              {{ form.province || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"市\" prop=\"city\">\r\n              {{ form.city || \"-\" }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--\r\n        <el-form-item label=\"经度\" prop=\"longitude\">\r\n          <el-input v-model=\"form.longitude\" placeholder=\"请输入经度\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"纬度\" prop=\"latitude\">\r\n          <el-input v-model=\"form.latitude\" placeholder=\"请输入纬度\" />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"更新日期\" prop=\"updateDate\">\r\n          <el-date-picker\r\n            clearable\r\n            v-model=\"form.updateDate\"\r\n            type=\"date\"\r\n            value-format=\"yyyy-MM-dd\"\r\n            placeholder=\"请选择更新日期\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"删除状态\" prop=\"deleted\">\r\n          <el-input v-model=\"form.deleted\" placeholder=\"请输入删除状态\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"区\" prop=\"district\">\r\n          <el-input v-model=\"form.district\" placeholder=\"请输入区\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"项目编码\" prop=\"projectCode\">\r\n          <el-input v-model=\"form.projectCode\" placeholder=\"请输入项目编码\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"组织全路径ID\" prop=\"id\">\r\n          <el-input v-model=\"form.id\" placeholder=\"请输入组织全路径ID\" />\r\n        </el-form-item>-->\r\n      </el-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjProjectInfo,\r\n  getZjProjectInfo,\r\n  delZjProjectInfo,\r\n  addZjProjectInfo,\r\n  updateZjProjectInfo,\r\n} from \"@/api/inspection/zjProjectInfo\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { getCompanyList, querytree } from \"@/api/system/info\";\r\n// import orgTree from \"../../components/orgTree.vue\";\r\nimport selectPeopleTree from \"@/views/components/selectPeopleTree.vue\";\r\n\r\nexport default {\r\n  name: \"ZjProjectInfo\",\r\n  dicts: [\"zj_construct_status\"],\r\n  components: {\r\n    // orgTree,\r\n    selectPeopleTree,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 项目信息表格数据\r\n      zjProjectInfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      openView: false,\r\n\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        parentOrgId: null,\r\n        orgId: null,\r\n        belongCompany: null,\r\n        orgType: null,\r\n        code: null,\r\n        name: null,\r\n        shortName: null,\r\n        syncCode: null,\r\n        manager: null,\r\n        managerMobile: null,\r\n        constructStatus: \"1\",\r\n        constructType: null,\r\n        constructPurpose: null,\r\n        structType: null,\r\n        planStart: null,\r\n        planEnd: null,\r\n        actualStart: null,\r\n        area: null,\r\n        location: null,\r\n        longitude: null,\r\n        latitude: null,\r\n        biddingUnit: null,\r\n        constructUnit: null,\r\n        supervisingUnit: null,\r\n        updateDate: null,\r\n        deleted: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        projectCode: null,\r\n        id: null,\r\n      },\r\n\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"项目名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        shortName: [\r\n          { required: true, message: \"项目简称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        manager: [\r\n          { required: true, message: \"项目经理不能为空\", trigger: \"blur\" },\r\n        ],\r\n        managerMobile: [\r\n          { required: true, message: \"项目经理电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        belongCompany: [\r\n          { required: true, message: \"部门名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        constructStatus: [\r\n          { required: true, message: \"项目状态不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      placeholder: \"请输入所属单位\",\r\n      constructStatusDict: [],\r\n      companyList: [],\r\n      allCompanyList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getConstructStatusDict();\r\n    this.getCompanyList();\r\n    this.getAllCompanyList();\r\n  },\r\n  methods: {\r\n    handleChange(selectedItem) {\r\n      if (selectedItem.id == \"654470716198912\") {\r\n        this.$message.error(\"请选择下级单位\");\r\n        return;\r\n      }\r\n      if (selectedItem) {\r\n        this.form.belongCompany = selectedItem.label;\r\n\r\n        this.form.orgId = selectedItem.id;\r\n      } else {\r\n        this.form.belongCompany = null;\r\n        this.form.orgId = null;\r\n      }\r\n    },\r\n    getAllCompanyList() {\r\n      querytree().then((res) => {\r\n        if (res.code == 200) {\r\n          this.allCompanyList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getCompanyList() {\r\n      getCompanyList().then((res) => {\r\n        if (res.code == 200) {\r\n          this.companyList = res.data;\r\n        }\r\n      });\r\n    },\r\n    getConstructStatusDict() {\r\n      getDicts(\"zj_construct_status\").then((res) => {\r\n        res.data.forEach((item) => {\r\n          this.constructStatusDict[item.dictValue] = item.dictLabel;\r\n        });\r\n      });\r\n    },\r\n    getConstructStatus(status) {\r\n      return this.constructStatusDict[status];\r\n    },\r\n\r\n    handleOrgTreeNodeClick(node) {\r\n      // 在这里处理接收到的子组件数据\r\n      console.log(\"接收到子组件数据:\", node.id);\r\n      // 可以根据nodeData更新查询参数或其他状态\r\n      this.queryParams.parentOrgId = node.id; // 假设nodeData中有id字段\r\n      this.handleQuery(); // 触发查询\r\n    },\r\n\r\n    /** 查询项目信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjProjectInfo(this.queryParams).then((res) => {\r\n        this.zjProjectInfoList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        orgId: null,\r\n        belongCompany: null,\r\n        parentOrgId: null,\r\n        orgType: null,\r\n        code: null,\r\n        name: null,\r\n        shortName: null,\r\n        syncCode: null,\r\n        manager: null,\r\n        managerMobile: null,\r\n        constructStatus: null,\r\n        constructType: null,\r\n        constructPurpose: null,\r\n        structType: null,\r\n        planStart: null,\r\n        planEnd: null,\r\n        actualStart: null,\r\n        area: null,\r\n        location: null,\r\n        longitude: null,\r\n        latitude: null,\r\n        biddingUnit: null,\r\n        constructUnit: null,\r\n        supervisingUnit: null,\r\n        updateDate: null,\r\n        deleted: null,\r\n        province: null,\r\n        city: null,\r\n        district: null,\r\n        projectCode: null,\r\n        id: null,\r\n      };\r\n      this.belongCompany = null;\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n\r\n      this.queryParams.parentOrgId = \"\";\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加项目信息\";\r\n    },\r\n    handleDetail(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjProjectInfo(id).then((res) => {\r\n        this.form = res.data;\r\n        this.openView = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjProjectInfo(id).then((res) => {\r\n        this.form = res.data;\r\n        this.open = true;\r\n        this.title = \"修改项目信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjProjectInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.getCompanyList();\r\n            });\r\n          } else {\r\n            addZjProjectInfo(this.form).then((res) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n              this.getCompanyList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除项目信息编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjProjectInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/zjProjectInfo/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjProjectInfo_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n  },\r\n};\r\n</script>\r\n"]}]}