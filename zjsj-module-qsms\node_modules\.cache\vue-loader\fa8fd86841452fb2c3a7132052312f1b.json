{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue?vue&type=style&index=0&id=0fa16cf3&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\inspection\\problemLedger\\components\\zjQualityInspectionInfo\\index.vue", "mtime": 1757425168080}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1757382153709}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1757382157092}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1757382154814}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1757382152798}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZm9udC0xMiB7DQogIGZvbnQtc2l6ZTogMTJweDsNCn0NCg0KLmNpcmNsZSB7DQogIHdpZHRoOiA4cHg7DQogIGhlaWdodDogOHB4Ow0KICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogIGJvcmRlci1yYWRpdXM6IDUwJTsNCiAgbWFyZ2luLXJpZ2h0OiAycHg7DQp9DQoNCi5iZy1vcmFuZ2Ugew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZhNTAwOw0KfQ0KDQouYmctYmx1ZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICMwMDdiZmY7DQp9DQoNCi5iZy1ncmVlbiB7DQogIGJhY2tncm91bmQtY29sb3I6ICMyOGE3NDU7DQp9DQoNCi8qIOivpuaDheW8ueeql+agt+W8jyAqLw0KOmRlZXAoLmRldGFpbC1kaWFsb2cpIHsNCiAgLmVsLWRpYWxvZ19faGVhZGVyIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmNWY1Ow0KICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTZlNmU2Ow0KICAgIHBhZGRpbmc6IDE1cHggMjBweDsNCiAgfQ0KDQogIC5lbC1kaWFsb2dfX3RpdGxlIHsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogIzMzMzsNCiAgfQ0KDQogIC5lbC1kaWFsb2dfX2JvZHkgew0KICAgIHBhZGRpbmc6IDA7DQogICAgbWF4LWhlaWdodDogNjB2aDsNCiAgICBvdmVyZmxvdy15OiBhdXRvOw0KICAgIHBhZGRpbmc6IDBweCAyMHB4ICFpbXBvcnRhbnQ7DQogIH0NCg0KICA6OnYtZGVlcCguZWwtZGlhbG9nX19ib2R5KSB7DQogICAgcGFkZGluZzogMHB4IDIwcHggIWltcG9ydGFudDsNCiAgfQ0KfQ0KDQouZGV0YWlsLWNvbnRlbnQgew0KICBwYWRkaW5nOiAwIDIwcHg7DQoNCiAgLmluZGVwZW5kZW50LXNlY3Rpb24gew0KICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQoNCiAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICB9DQogIH0NCg0KICAuaW5kZXBlbmRlbnQtdGl0bGUgew0KICAgIG1hcmdpbjogMCAwIDIwcHggMDsNCiAgICBwYWRkaW5nOiAwOw0KICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICBmb250LXdlaWdodDogNjAwOw0KICAgIGNvbG9yOiAjMzMzOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBnYXA6IDIwcHg7DQoNCiAgICAmOmFmdGVyIHsNCiAgICAgIGNvbnRlbnQ6ICIiOw0KICAgICAgZmxleDogMTsNCiAgICAgIGhlaWdodDogMnB4Ow0KICAgICAgYm9yZGVyLXRvcDogMnB4IGRhc2hlZCAjNDA5ZWZmOw0KICAgIH0NCiAgfQ0KDQogIC8vIOW3puWPs+S4pOWIl+W4g+WxgA0KICAucmVjb3JkLWNvbHVtbnMgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZ2FwOiA0MHB4Ow0KICB9DQoNCiAgLmxlZnQtY29sdW1uLA0KICAucmlnaHQtY29sdW1uIHsNCiAgICBmbGV4OiAxOw0KICB9DQoNCiAgLmZpZWxkLXJvdyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsNCiAgICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KDQogICAgJi5mdWxsLXdpZHRoIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgIH0NCg0KICAgICY6bGFzdC1jaGlsZCB7DQogICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgIH0NCg0KICAgICYuaGlnaGxpZ2h0ZWQtZmllbGQgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ViZWNmMDsNCiAgICAgIHBhZGRpbmc6IDhweCAwOw0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICAgIG1hcmdpbi1sZWZ0OiAtMjBweDsNCiAgICAgIG1hcmdpbi1yaWdodDogLTIwcHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDIwcHg7DQogICAgICBwYWRkaW5nLXJpZ2h0OiAyMHB4Ow0KICAgIH0NCiAgfQ0KDQogIC8vIOmdnumrmOS6ruWtl+auteS4jumrmOS6ruWtl+auteS5i+mXtOeahOmXtOi3nQ0KICAuZmllbGQtcm93Om5vdCguaGlnaGxpZ2h0ZWQtZmllbGQpICsgLmZpZWxkLXJvdy5oaWdobGlnaHRlZC1maWVsZCwNCiAgLmZpZWxkLXJvdy5oaWdobGlnaHRlZC1maWVsZCArIC5maWVsZC1yb3c6bm90KC5oaWdobGlnaHRlZC1maWVsZCkgew0KICAgIG1hcmdpbi10b3A6IDE2cHg7DQogIH0NCg0KICAuZmllbGQtbGFiZWwgew0KICAgIG1pbi13aWR0aDogNzBweDsNCiAgICBmb250LXdlaWdodDogNDAwOw0KICAgIGNvbG9yOiAjNjY2Ow0KICAgIG1hcmdpbi1yaWdodDogMTBweDsNCiAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBsaW5lLWhlaWdodDogMS41Ow0KICB9DQoNCiAgLmZpZWxkLXZhbHVlIHsNCiAgICBjb2xvcjogIzMzMzsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgbGluZS1oZWlnaHQ6IDEuNTsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgZmxleDogMTsNCg0KICAgICYuc3RhdHVzLXRhZyB7DQogICAgICBwYWRkaW5nOiAycHggOHB4Ow0KICAgICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICB9DQogIH0NCg0KICAvLyDmlbTmlLnorrDlvZXpg6jliIbmoLflvI8NCiAgLnJlY3RpZmljYXRpb24taW5mbyB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBnYXA6IDA7DQogICAgbWFyZ2luLWJvdHRvbTogMTZweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZWJlY2YwOw0KICAgIG1hcmdpbi1sZWZ0OiAtMjBweDsNCiAgICBtYXJnaW4tcmlnaHQ6IC0yMHB4Ow0KICAgIHBhZGRpbmc6IDhweCAyMHB4Ow0KDQogICAgLmZpZWxkLXJvdyB7DQogICAgICBtYXJnaW4tYm90dG9tOiAwOw0KICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgICAgIGZsZXg6IDE7DQogICAgICBtYXJnaW4tbGVmdDogMDsNCiAgICAgIG1hcmdpbi1yaWdodDogMDsNCiAgICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50Ow0KDQogICAgICAmOmZpcnN0LWNoaWxkIHsNCiAgICAgICAgcGFkZGluZy1sZWZ0OiAwOw0KICAgICAgfQ0KDQogICAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgICBwYWRkaW5nLXJpZ2h0OiAwOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC8vIOeKtuaAgeagh+etvuminOiJsg0KICAuc3RhdHVzLW5vLW5lZWQgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmMGY5ZmY7DQogICAgY29sb3I6ICMwMzY5YTE7DQogICAgYm9yZGVyOiAxcHggc29saWQgIzdkZDNmYzsNCiAgfQ0KDQogIC5zdGF0dXMtcGVuZGluZyB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7DQogICAgY29sb3I6ICNkOTc3MDY7DQogICAgYm9yZGVyOiBub25lOw0KICB9DQoNCiAgLnN0YXR1cy1yZWN0aWZpZWQgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNkY2ZjZTc7DQogICAgY29sb3I6ICMxNmEzNGE7DQogICAgYm9yZGVyOiAxcHggc29saWQgIzRhZGU4MDsNCiAgfQ0KDQogIC5zdGF0dXMtcXVhbGlmaWVkIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZGNmY2U3Ow0KICAgIGNvbG9yOiAjMTZhMzRhOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICM0YWRlODA7DQogIH0NCg0KICAuc3RhdHVzLXVucXVhbGlmaWVkIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmVlMmUyOw0KICAgIGNvbG9yOiAjZGMyNjI2Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNmODcxNzE7DQogIH0NCg0KICAuc3RhdHVzLXZlcmlmeSB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2VkZTlmZTsNCiAgICBjb2xvcjogIzdjM2FlZDsNCiAgICBib3JkZXI6IDFweCBzb2xpZCAjYTc4YmZhOw0KICB9DQoNCiAgLnN0YXR1cy11bmtub3duIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjNmNGY2Ow0KICAgIGNvbG9yOiAjNmI3MjgwOw0KICAgIGJvcmRlcjogMXB4IHNvbGlkICNkMWQ1ZGI7DQogIH0NCg0KICAvLyDnirbmgIHooYzmoLflvI8NCiAgLnN0YXR1cy1yb3cgew0KICAgIG1hcmdpbi10b3A6IDhweDsNCg0KICAgIGxhYmVsIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsNCiAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KDQogICAgICBpbnB1dFt0eXBlPSJjaGVja2JveCJdIHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g54Wn54mH55u45YWz5qC35byPDQogIC5waG90by1zZWN0aW9uIHsNCiAgICBwYWRkaW5nLXRvcDogMTBweDsNCiAgICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjBmMDsNCiAgfQ0KDQogIC5waG90by1jb250YWluZXIgew0KICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgZ2FwOiAxMHB4Ow0KICB9DQoNCiAgLnBob3RvLWl0ZW0sDQogIC5waG90by1wbGFjZWhvbGRlciB7DQogICAgYm9yZGVyOiAxcHggc29saWQgI2U2ZTZlNjsNCiAgICBib3JkZXItcmFkaXVzOiA2cHg7DQogICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7DQogICAgYmFja2dyb3VuZDogI2ZhZmFmYTsNCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjAyKTsNCiAgICAgIGJvcmRlci1jb2xvcjogIzQwOWVmZjsNCiAgICAgIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDY0LCAxNTgsIDI1NSwgMC4yKTsNCiAgICB9DQoNCiAgICBpbWcgew0KICAgICAgd2lkdGg6IDEyMHB4Ow0KICAgICAgaGVpZ2h0OiA5MHB4Ow0KICAgICAgb2JqZWN0LWZpdDogY292ZXI7DQogICAgICBkaXNwbGF5OiBibG9jazsNCiAgICB9DQogIH0NCg0KICAucGhvdG8tcGxhY2Vob2xkZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOw0KICAgIGJvcmRlcjogMXB4IGRhc2hlZCAjZDFkNWRiOw0KDQogICAgJjpob3ZlciB7DQogICAgICB0cmFuc2Zvcm06IG5vbmU7DQogICAgICBib3JkZXItY29sb3I6ICNkMWQ1ZGI7DQogICAgICBib3gtc2hhZG93OiBub25lOw0KICAgIH0NCg0KICAgIGltZyB7DQogICAgICBvcGFjaXR5OiAwLjM7DQogICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsNCiAgICB9DQogIH0NCg0KICAubm8tcGhvdG8gew0KICAgIGNvbG9yOiAjOTk5Ow0KICAgIGZvbnQtc3R5bGU6IGl0YWxpYzsNCiAgICBmb250LXNpemU6IDEycHg7DQogICAgbWFyZ2luLXRvcDogOHB4Ow0KICB9DQoNCiAgLy8g5ZON5bqU5byP6K6+6K6hDQogIEBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgew0KICAgIC5yZWNvcmQtY29sdW1ucyB7DQogICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgZ2FwOiAyMHB4Ow0KICAgIH0NCg0KICAgIC5yZWN0aWZpY2F0aW9uLWluZm8gew0KICAgICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsNCiAgICAgIGdhcDogMTBweDsNCiAgICB9DQogIH0NCn0NCg0KLy8g5Zu+54mH6aKE6KeI5by556qX5qC35byPDQo6ZGVlcCguaW1hZ2UtcHJldmlldy1kaWFsb2cpIHsNCiAgLmVsLW1lc3NhZ2UtYm94X19jb250ZW50IHsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIH0NCg0KICAuZWwtbWVzc2FnZS1ib3hfX21lc3NhZ2Ugew0KICAgIG1hcmdpbjogMDsNCiAgfQ0KfQ0KDQovLyDlhazlj7jnrZvpgInkuIvmi4nmoYbmoLflvI8NCi50cmVlLXNlbGVjdC13cmFwcGVyIHsNCiAgcGFkZGluZzogOHB4Ow0KICBtaW4taGVpZ2h0OiAyMDBweDsNCiAgbWF4LWhlaWdodDogMzAwcHg7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0KDQo6ZGVlcCgub3JnLXRyZWUtc2VsZWN0LWRyb3Bkb3duKSB7DQogIC5lbC1zZWxlY3QtZHJvcGRvd25fX2l0ZW0gew0KICAgIGhlaWdodDogYXV0bzsNCiAgICBtYXgtaGVpZ2h0OiAzMDBweDsNCiAgICBwYWRkaW5nOiAwOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgb3ZlcmZsb3cteTogYXV0bzsNCiAgfQ0KDQogIC5lbC10cmVlLW5vZGVfX2NvbnRlbnQgew0KICAgIGhlaWdodDogMzJweDsNCiAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogIH0NCg0KICAuZWwtdHJlZS1ub2RlX19sYWJlbCB7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs4CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/inspection/problemLedger/components/zjQualityInspectionInfo", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form\r\n      v-show=\"showSearch\"\r\n      ref=\"queryForm\"\r\n      :model=\"queryParams\"\r\n      size=\"small\"\r\n      :inline=\"true\"\r\n      label-width=\"68px\"\r\n    >\r\n      <!-- <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n        <el-input\r\n          v-model=\"queryParams.projectId\"\r\n          placeholder=\"请输入项目ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n        <el-input\r\n          v-model=\"queryParams.creatorId\"\r\n          placeholder=\"请输入检查人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"所属公司\" prop=\"unitName\">\r\n        <el-select\r\n          ref=\"queryCompanySelect\"\r\n          v-model=\"queryParams.unitName\"\r\n          placeholder=\"请选择所属公司\"\r\n          clearable\r\n          style=\"width: 180px\"\r\n          popper-class=\"org-tree-select-dropdown\"\r\n        >\r\n          <el-option\r\n            :value=\"queryParams.unitName\"\r\n            :label=\"queryParams.unitName\"\r\n            style=\"height: auto; padding: 0; border: none\"\r\n          >\r\n            <div class=\"tree-select-wrapper\">\r\n              <el-tree\r\n                v-loading=\"companyTreeLoading\"\r\n                :data=\"companyTreeData\"\r\n                :props=\"companyTreeProps\"\r\n                highlight-current\r\n                @node-click=\"handleQueryCompanyNodeClick\"\r\n              >\r\n                <template #default=\"{ node, data }\">\r\n                  <el-tooltip\r\n                    effect=\"dark\"\r\n                    :content=\"data.label\"\r\n                    placement=\"top\"\r\n                  >\r\n                    <span class=\"el-tree-node__label\">\r\n                      {{ node.label }}\r\n                    </span>\r\n                  </el-tooltip>\r\n                </template>\r\n              </el-tree>\r\n            </div>\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"常规id\" prop=\"routineId\">\r\n        <el-input\r\n          v-model=\"queryParams.routineId\"\r\n          placeholder=\"请输入常规id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n        <el-input\r\n          v-model=\"queryParams.projectName\"\r\n          placeholder=\"请输入项目名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"记录id\" prop=\"recordId\">\r\n        <el-input\r\n          v-model=\"queryParams.recordId\"\r\n          placeholder=\"请输入记录id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionId\"\r\n          placeholder=\"请输入责任区域ID\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullId\"\r\n          placeholder=\"请输入责任区域全id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionName\"\r\n          placeholder=\"请输入区域名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.regionFullName\"\r\n          placeholder=\"请输入区域全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeId\"\r\n          placeholder=\"请输入隐患类别id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"关系\" prop=\"relation\">\r\n        <el-input\r\n          v-model=\"queryParams.relation\"\r\n          placeholder=\"请输入关系\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"隐患类别\" prop=\"dangerTypeName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeName\"\r\n          placeholder=\"请输入隐患类别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerTypeFullName\"\r\n          placeholder=\"请输入隐患类别全称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n        <el-input\r\n          v-model=\"queryParams.autoRecg\"\r\n          placeholder=\"请输入auto_recg\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n       <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n        <el-input\r\n          v-model=\"queryParams.dangerDesc\"\r\n          placeholder=\"请输入备注\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时间\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item>-->\r\n      <!-- <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n        <el-date-picker\r\n          clearable\r\n          v-model=\"queryParams.changeLimitTime\"\r\n          type=\"date\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          placeholder=\"请选择整改时限(天)\"\r\n        >\r\n        </el-date-picker>\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"级别\" prop=\"level\">\r\n        <el-input\r\n          v-model=\"queryParams.level\"\r\n          placeholder=\"请输入级别\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n        <el-input\r\n          v-model=\"queryParams.levelName\"\r\n          placeholder=\"请输入级别名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n     <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n        <el-input\r\n          v-model=\"queryParams.changeId\"\r\n          placeholder=\"请输入整改人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n        <el-input\r\n          v-model=\"queryParams.participationIds\"\r\n          placeholder=\"请输入参与人id\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- <el-form-item label=\"参与人\" prop=\"participationNames\">\r\n        <el-input\r\n          v-model=\"queryParams.participationNames\"\r\n          placeholder=\"请输入参与人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item> -->\r\n      <!-- 检查结果 -->\r\n      <el-form-item label=\"检查结果\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"请选择检查结果\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in checkResultOptions\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.value\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button\r\n          type=\"primary\"\r\n          icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n          @click=\"handleQuery\"\r\n          >搜索</el-button\r\n        >\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\"\r\n          >重置</el-button\r\n        >\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button v-hasPermi=\"['inspection:zjQualityInspectionInfo:add']\" type=\"primary\" plain icon=\"el-icon-plus\"\r\n          size=\"mini\" @click=\"handleAdd\">新增</el-button>\r\n      </el-col> -->\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n          >修改</el-button\r\n        >\r\n      </el-col> -->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          >删除</el-button\r\n        >\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['inspection:zjQualityInspectionInfo:export']\"\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          >导出</el-button\r\n        >\r\n      </el-col>\r\n      <right-toolbar :show-search.sync=\"showSearch\" @queryTable=\"getList\" />\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-loading=\"loading\"\r\n      :data=\"zjQualityInspectionInfoList\"\r\n      height=\"calc(100vh - 250px)\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"left\" />\r\n      <el-table-column label=\"序号\" width=\"55\" align=\"left\">\r\n        <template slot-scope=\"scope\">\r\n          {{\r\n            (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1\r\n          }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 检查结果/截止时限 -->\r\n      <el-table-column label=\"检查结果/截止时限\" align=\"left\" width=\"200\">\r\n        <template slot-scope=\"{ row }\">\r\n          <div class=\"font-12\">\r\n            <span class=\"circle\" :class=\"getStatusClass(row.status)\" />\r\n            {{ getCheckResultText(row.status) }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            复查时限:{{\r\n              row.changeLimitTime ? row.changeLimitTime.slice(0, 10) : \"\"\r\n            }}\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 所属公司 -->\r\n      <el-table-column\r\n        label=\"所属公司\"\r\n        align=\"left\"\r\n        prop=\"ownerOrgStr\"\r\n        width=\"160\"\r\n        show-overflow-tooltip\r\n      />\r\n\r\n      <el-table-column\r\n        label=\"项目名称\"\r\n        align=\"left\"\r\n        prop=\"projectName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      />\r\n      <el-table-column\r\n        label=\"检查人/检查时间\"\r\n        align=\"left\"\r\n        prop=\"creatorName\"\r\n        width=\"140\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <div class=\"font-12\">\r\n            {{ scope.row.creatorName }}\r\n          </div>\r\n          <div class=\"font-12\">\r\n            {{ scope.row.createTime }}\r\n          </div>\r\n          <!-- {{ scope.row.creatorName + \"/\" + scope.row.createTime }} -->\r\n        </template>\r\n      </el-table-column>\r\n      <!-- 质量问题信息 -->\r\n      <el-table-column\r\n        label=\"质量问题信息\"\r\n        align=\"left\"\r\n        width=\"200\"\r\n        show-overflow-tooltip\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- levelName dangerTypeFullName dangerDesc  -->\r\n          <el-tag type=\"primary\"> {{ scope.row.levelName }}</el-tag>\r\n\r\n          {{ scope.row.dangerTypeFullName + \",\" + scope.row.dangerDesc }}\r\n        </template>\r\n      </el-table-column>\r\n      <!-- <el-table-column label=\"状态\" align=\"left\" prop=\"projectStatus\" /> -->\r\n      <!-- <el-table-column\r\n        label=\"区域名称\"\r\n        align=\"left\"\r\n        prop=\"regionName\"\r\n        width=\"300\"\r\n        show-overflow-tooltip\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"隐患类别名称\"\r\n        align=\"left\"\r\n        width=\"120\"\r\n        prop=\"dangerTypeName\"\r\n      /> -->\r\n      <!-- <el-table-column\r\n        label=\"整改时间\"\r\n        align=\"left\"\r\n        prop=\"changeTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column> -->\r\n\r\n      <el-table-column label=\"整改人\" align=\"left\" prop=\"changeName\" />\r\n      <!-- <el-table-column label=\"参与人\" align=\"left\" prop=\"participationNames\" /> -->\r\n      <!-- 复查人 -->\r\n      <el-table-column label=\"复查人\" align=\"left\" prop=\"reviewName\" />\r\n      <!-- 核验人 -->\r\n      <el-table-column label=\"核验人\" align=\"left\" prop=\"verifyManName\" />\r\n      <!-- 例行检查 -->\r\n      <el-table-column label=\"例行检查\" align=\"left\" prop=\"routineCheckNames\" />\r\n      <!-- 说明 -->\r\n      <el-table-column\r\n        label=\"说明\"\r\n        align=\"left\"\r\n        prop=\"remark\"\r\n        show-overflow-tooltip\r\n      />\r\n      <!-- <el-table-column label=\"项目ID\" align=\"left\" prop=\"projectId\" />\r\n      <el-table-column label=\"检查人id\" align=\"left\" prop=\"creatorId\" />\r\n\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"remark\" />\r\n      <el-table-column label=\"常规id\" align=\"left\" prop=\"routineId\" />\r\n      <el-table-column label=\"记录id\" align=\"left\" prop=\"recordId\" />\r\n      <el-table-column label=\"责任区域ID\" align=\"left\" prop=\"regionId\" />\r\n      <el-table-column\r\n        label=\"责任区域全id\"\r\n        align=\"left\"\r\n        prop=\"regionFullId\"\r\n      />\r\n      <el-table-column label=\"区域全称\" align=\"left\" prop=\"regionFullName\" />\r\n      <el-table-column label=\"关系\" align=\"left\" prop=\"relation\" />\r\n      <el-table-column label=\"隐患类别id\" align=\"left\" prop=\"dangerTypeId\" />\r\n\r\n      <el-table-column\r\n        label=\"隐患类别全称\"\r\n        align=\"left\"\r\n        prop=\"dangerTypeFullName\"\r\n      />\r\n      <el-table-column label=\"auto_recg\" align=\"left\" prop=\"autoRecg\" />\r\n      <el-table-column\r\n        label=\"隐患类目内容\"\r\n        align=\"left\"\r\n        prop=\"dangerItemContent\"\r\n      />\r\n      <el-table-column label=\"备注\" align=\"left\" prop=\"dangerDesc\" />\r\n\r\n      <el-table-column\r\n        label=\"整改时限(天)\"\r\n        align=\"left\"\r\n        prop=\"changeLimitTime\"\r\n        width=\"180\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.changeLimitTime, \"{y}-{m}-{d}\") }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"级别\" align=\"left\" prop=\"level\" />\r\n      <el-table-column label=\"级别名称\" align=\"left\" prop=\"levelName\" />\r\n      <el-table-column label=\"状态\" align=\"left\" prop=\"status\" />\r\n      <el-table-column label=\"整改人id\" align=\"left\" prop=\"changeId\" />\r\n      <el-table-column\r\n        label=\"参与人id\"\r\n        align=\"left\"\r\n        prop=\"participationIds\"\r\n      /> -->\r\n\r\n      <el-table-column\r\n        label=\"操作\"\r\n        align=\"left\"\r\n        class-name=\"small-padding fixed-width\"\r\n        fixed=\"right\"\r\n        width=\"150\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <!-- 查看详情 -->\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-view\"\r\n            @click=\"handleDetail(scope.row)\"\r\n            >详情</el-button\r\n          >\r\n          <!-- <el-button size=\"mini\" type=\"text\">打印</el-button> -->\r\n\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:edit']\"\r\n            >修改</el-button\r\n          > -->\r\n          <!-- <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['inspection:zjQualityInspectionInfo:remove']\"\r\n            >删除</el-button\r\n          > -->\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total > 0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 详情弹窗 -->\r\n    <el-dialog\r\n      title=\"查看详情\"\r\n      :visible.sync=\"detailDialog\"\r\n      width=\"1000px\"\r\n      append-to-body\r\n      custom-class=\"detail-dialog\"\r\n    >\r\n      <div v-if=\"detailData\" class=\"detail-content\">\r\n        <!-- 问题记录部分 -->\r\n        <div class=\"independent-section\">\r\n          <h3 class=\"independent-title\">问题记录</h3>\r\n          <!-- 左右两列布局 -->\r\n          <div class=\"record-columns\">\r\n            <!-- 左列 -->\r\n            <div class=\"left-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查部位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.regionName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题描述:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerItemContent || detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题分类:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerTypeFullName ||\r\n                  detailData.dangerTypeName ||\r\n                  \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">补充说明:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.dangerDesc || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">问题等级:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.levelName || detailData.level || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改要求:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.rectificationRequirements || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">轴线位置:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.axisPosition || \"8号18-17/D\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">分包单位:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.contractorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 右列 -->\r\n            <div class=\"right-column\">\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row highlighted-field\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime || \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">紧急程度:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.urgencyLevel || \"一般\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.creatorName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">检查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.createTime\r\n                    ? detailData.createTime.slice(0, 10)\r\n                    : \"2025-08-25\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">整改时限:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.changeLimitTime\r\n                    ? detailData.changeLimitTime.slice(0, 10)\r\n                    : \"2025-08-27\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewName || \"-\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">复查时间:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.reviewTime\r\n                    ? detailData.reviewTime.slice(0, 10)\r\n                    : \"2025-08-30\"\r\n                }}</span>\r\n              </div>\r\n\r\n              <div class=\"field-row\">\r\n                <span class=\"field-label\">通知人:</span>\r\n                <span class=\"field-value\">{{\r\n                  detailData.notifierName || \"-\"\r\n                }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 现状整改部分 -->\r\n          <div class=\"field-row\">\r\n            <span class=\"field-label\">现状整改:</span>\r\n            <div class=\"status-row\">\r\n              <label\r\n                ><input\r\n                  type=\"checkbox\"\r\n                  :checked=\"detailData.status !== '1'\"\r\n                  disabled\r\n                />\r\n                未完成</label\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.hiddenDangerPictures\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.hiddenDangerPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"问题照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 整改记录部分 - 待整改状态(status=1)时不显示 -->\r\n        <div v-if=\"detailData.status !== '1'\" class=\"independent-section\">\r\n          <h3 class=\"independent-title\">整改记录</h3>\r\n          <!-- 整改信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getRectificationStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">整改时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.changeTime\r\n                  ? detailData.changeTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 整改说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">整改说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.rectificationDesc || \"已整改\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 相关照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div\r\n              v-if=\"detailData.rectificationPictures\"\r\n              class=\"photo-container\"\r\n            >\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.rectificationPictures\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"整改照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 复查记录部分 - 待整改(status=1)和待复查(status=2)状态时不显示 -->\r\n        <div\r\n          v-if=\"detailData.status !== '1' && detailData.status !== '2'\"\r\n          class=\"independent-section\"\r\n        >\r\n          <h3 class=\"independent-title\">复查记录</h3>\r\n          <!-- 复查信息行 -->\r\n          <div class=\"rectification-info\">\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查状态:</span>\r\n              <span class=\"field-value\">{{\r\n                getReviewStatusText(detailData.status)\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查人:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewName || \"-\"\r\n              }}</span>\r\n            </div>\r\n\r\n            <div class=\"field-row highlighted-field\">\r\n              <span class=\"field-label\">复查时间:</span>\r\n              <span class=\"field-value\">{{\r\n                detailData.reviewTime\r\n                  ? detailData.reviewTime.slice(0, 16)\r\n                  : \"2025-08-27\"\r\n              }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 复查说明 -->\r\n          <div class=\"field-row full-width\">\r\n            <span class=\"field-label\">复查说明:</span>\r\n            <span class=\"field-value\">{{\r\n              detailData.reviewComments || \"-\"\r\n            }}</span>\r\n          </div>\r\n\r\n          <!-- 复查照片 -->\r\n          <div class=\"photo-section\">\r\n            <div class=\"field-row\">\r\n              <span class=\"field-label\">相关附件:</span>\r\n            </div>\r\n            <div v-if=\"detailData.reviewPicUrl\" class=\"photo-container\">\r\n              <div\r\n                v-for=\"(imageUrl, index) in getAllImageUrls(\r\n                  detailData.reviewPicUrl\r\n                )\"\r\n                :key=\"index\"\r\n                class=\"photo-item\"\r\n              >\r\n                <img\r\n                  :src=\"imageUrl\"\r\n                  alt=\"复查照片\"\r\n                  @click=\"previewImage(imageUrl)\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 添加或修改质量检查台账对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"主键ID\" prop=\"id\">\r\n              <el-input v-model=\"form.id\" placeholder=\"请输入主键ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目ID\" prop=\"projectId\">\r\n              <el-input v-model=\"form.projectId\" placeholder=\"请输入项目ID\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人id\" prop=\"creatorId\">\r\n              <el-input v-model=\"form.creatorId\" placeholder=\"请输入检查人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"检查人姓名\" prop=\"creatorName\">\r\n              <el-input\r\n                v-model=\"form.creatorName\"\r\n                placeholder=\"请输入检查人姓名\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"remark\">\r\n              <el-input v-model=\"form.remark\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"常规id\" prop=\"routineId\">\r\n              <el-input v-model=\"form.routineId\" placeholder=\"请输入常规id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"项目名称\" prop=\"projectName\">\r\n              <el-input\r\n                v-model=\"form.projectName\"\r\n                placeholder=\"请输入项目名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"记录id\" prop=\"recordId\">\r\n              <el-input v-model=\"form.recordId\" placeholder=\"请输入记录id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域ID\" prop=\"regionId\">\r\n              <el-input\r\n                v-model=\"form.regionId\"\r\n                placeholder=\"请输入责任区域ID\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"责任区域全id\" prop=\"regionFullId\">\r\n              <el-input\r\n                v-model=\"form.regionFullId\"\r\n                placeholder=\"请输入责任区域全id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称\" prop=\"regionName\">\r\n              <el-input\r\n                v-model=\"form.regionName\"\r\n                placeholder=\"请输入区域名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域全称\" prop=\"regionFullName\">\r\n              <el-input\r\n                v-model=\"form.regionFullName\"\r\n                placeholder=\"请输入区域全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别id\" prop=\"dangerTypeId\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeId\"\r\n                placeholder=\"请输入隐患类别id\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"关系\" prop=\"relation\">\r\n              <el-input v-model=\"form.relation\" placeholder=\"请输入关系\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别名称\" prop=\"dangerTypeName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeName\"\r\n                placeholder=\"请输入隐患类别名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"隐患类别全称\" prop=\"dangerTypeFullName\">\r\n              <el-input\r\n                v-model=\"form.dangerTypeFullName\"\r\n                placeholder=\"请输入隐患类别全称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"auto_recg\" prop=\"autoRecg\">\r\n              <el-input v-model=\"form.autoRecg\" placeholder=\"请输入auto_recg\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"备注\" prop=\"dangerDesc\">\r\n              <el-input v-model=\"form.dangerDesc\" placeholder=\"请输入备注\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"隐患类目内容\">\r\n              <editor v-model=\"form.dangerItemContent\" :min-height=\"192\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时间\" prop=\"changeTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时间\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改时限(天)\" prop=\"changeLimitTime\">\r\n              <el-date-picker\r\n                v-model=\"form.changeLimitTime\"\r\n                clearable\r\n                type=\"date\"\r\n                value-format=\"yyyy-MM-dd\"\r\n                placeholder=\"请选择整改时限(天)\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别\" prop=\"level\">\r\n              <el-input v-model=\"form.level\" placeholder=\"请输入级别\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"级别名称\" prop=\"levelName\">\r\n              <el-input v-model=\"form.levelName\" placeholder=\"请输入级别名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人id\" prop=\"changeId\">\r\n              <el-input v-model=\"form.changeId\" placeholder=\"请输入整改人id\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"整改人名称\" prop=\"changeName\">\r\n              <el-input\r\n                v-model=\"form.changeName\"\r\n                placeholder=\"请输入整改人名称\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <!-- <el-form-item label=\"参与人id\" prop=\"participationIds\">\r\n          <el-input\r\n            v-model=\"form.participationIds\"\r\n            placeholder=\"请输入参与人id\"\r\n          />\r\n        </el-form-item> -->\r\n        <!-- <el-form-item label=\"参与人姓名\" prop=\"participationNames\">\r\n          <el-input\r\n            v-model=\"form.participationNames\"\r\n            placeholder=\"请输入参与人姓名\"\r\n          />\r\n        </el-form-item> -->\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listZjQualityInspectionInfo,\r\n  getZjQualityInspectionInfo,\r\n  delZjQualityInspectionInfo,\r\n  addZjQualityInspectionInfo,\r\n  updateZjQualityInspectionInfo,\r\n} from \"@/api/inspection/zjQualityInspectionInfo\";\r\nimport { getEnterpriseInfo } from \"@/api/system/info\";\r\n\r\nexport default {\r\n  name: \"ZjQualityInspectionInfo\",\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 质量检查台账表格数据\r\n      zjQualityInspectionInfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        projectId: null,\r\n        creatorId: null,\r\n        unitName: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        id: [{ required: true, message: \"主键ID不能为空\", trigger: \"blur\" }],\r\n      },\r\n      // 详情弹窗\r\n      detailDialog: false,\r\n      // 详情数据\r\n      detailData: {},\r\n      // 公司树相关数据\r\n      companyTreeLoading: false,\r\n      companyTreeData: [],\r\n      companyTreeProps: {\r\n        children: \"children\",\r\n        label: \"label\",\r\n      },\r\n      // 检查结果选择项\r\n      checkResultOptions: [\r\n        {\r\n          label: \"无需整改\",\r\n          value: 0,\r\n        },\r\n        {\r\n          label: \"待整改\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"已整改\",\r\n          value: 2,\r\n        },\r\n        {\r\n          label: \"已合格\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"不合格\",\r\n          value: 4,\r\n        },\r\n        {\r\n          label: \"待核验\",\r\n          value: 7,\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getCompanyTreeData();\r\n  },\r\n  methods: {\r\n    getStatusClass(status) {\r\n      const statusMap = {\r\n        0: \"bg-blue\", // 无需整改\r\n        1: \"bg-orange\", // 待整改\r\n        2: \"bg-green\", // 已整改\r\n        3: \"bg-green\", // 已合格\r\n        4: \"bg-orange\", // 不合格\r\n        7: \"bg-blue\", // 待核验\r\n      };\r\n      return statusMap[status] || \"bg-blue\";\r\n    },\r\n    // 获取检查结果文字\r\n    getCheckResultText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    /** 查询质量检查台账列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listZjQualityInspectionInfo(this.queryParams).then((response) => {\r\n        this.zjQualityInspectionInfoList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        projectId: null,\r\n        createTime: null,\r\n        creatorId: null,\r\n        updateTime: null,\r\n        remark: null,\r\n        routineId: null,\r\n        projectName: null,\r\n        projectStatus: null,\r\n        recordId: null,\r\n        regionId: null,\r\n        regionFullId: null,\r\n        regionName: null,\r\n        regionFullName: null,\r\n        dangerTypeId: null,\r\n        relation: null,\r\n        dangerTypeName: null,\r\n        dangerTypeFullName: null,\r\n        autoRecg: null,\r\n        dangerItemContent: null,\r\n        dangerDesc: null,\r\n        changeTime: null,\r\n        changeLimitTime: null,\r\n        level: null,\r\n        levelName: null,\r\n        status: null,\r\n        changeId: null,\r\n        participationIds: null,\r\n        participationNames: null,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map((item) => item.id);\r\n      this.single = selection.length !== 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加质量检查台账\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const id = row.id || this.ids;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改质量检查台账\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addZjQualityInspectionInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除质量检查台账编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delZjQualityInspectionInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\r\n        \"inspection/zjQualityInspectionInfo/export\",\r\n        {\r\n          ...this.queryParams,\r\n        },\r\n        `zjQualityInspectionInfo_${new Date().getTime()}.xlsx`\r\n      );\r\n    },\r\n    // 查看详情\r\n    handleDetail(row) {\r\n      const id = row.id;\r\n      getZjQualityInspectionInfo(id).then((response) => {\r\n        this.detailData = response.data;\r\n        this.detailDialog = true;\r\n      });\r\n    },\r\n    // 获取整改状态文字\r\n    getRectificationStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需整改\",\r\n        1: \"待整改\",\r\n        2: \"已整改\",\r\n        3: \"已合格\",\r\n        4: \"不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未知状态\";\r\n    },\r\n    // 获取复查状态文字\r\n    getReviewStatusText(status) {\r\n      const statusMap = {\r\n        0: \"无需复查\",\r\n        1: \"待复查\",\r\n        2: \"待复查\",\r\n        3: \"复查合格\",\r\n        4: \"复查不合格\",\r\n        7: \"待核验\",\r\n      };\r\n      return statusMap[status] || \"未复查\";\r\n    },\r\n    // 解析图片URL列表\r\n    parseImageUrls(imageUrl, dataSource) {\r\n      if (!imageUrl) return [];\r\n\r\n      // 当dataSource为2时，处理后端拼接好的URL\r\n      if (dataSource === 2 || dataSource === \"2\") {\r\n        // 移除开头的@符号，然后按逗号分割\r\n        const urlStr = imageUrl.startsWith(\"@\")\r\n          ? imageUrl.substring(1)\r\n          : imageUrl;\r\n        return urlStr\r\n          .split(\",\")\r\n          .filter((url) => url.trim())\r\n          .map((url) => url.trim());\r\n      }\r\n\r\n      // 默认情况：前端拼接\r\n      if (imageUrl.startsWith(\"/\")) {\r\n        return [`${process.env.VUE_APP_BASE_API}${imageUrl}`];\r\n      }\r\n      return [imageUrl];\r\n    },\r\n    // 获取图片URL（兼容原有逻辑）\r\n    getImageUrl(imageUrl) {\r\n      const urls = this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n      return urls.length > 0 ? urls[0] : \"\";\r\n    },\r\n    // 获取所有图片URL\r\n    getAllImageUrls(imageUrl) {\r\n      return this.parseImageUrls(imageUrl, this.detailData?.dataSource);\r\n    },\r\n    // 预览图片\r\n    previewImage(imageUrl) {\r\n      if (!imageUrl) return;\r\n      // 使用element-ui的图片预览功能\r\n      this.$alert(\r\n        `<img src=\"${imageUrl}\" style=\"width: 100%; max-width: 500px;\" alt=\"预览图片\">`,\r\n        \"图片预览\",\r\n        {\r\n          dangerouslyUseHTMLString: true,\r\n          customClass: \"image-preview-dialog\",\r\n          showConfirmButton: false,\r\n          showCancelButton: true,\r\n          cancelButtonText: \"关闭\",\r\n        }\r\n      );\r\n    },\r\n    // 处理公司节点点击\r\n    handleQueryCompanyNodeClick(data) {\r\n      this.queryParams.unitName = data.label;\r\n      this.$refs.queryCompanySelect.blur();\r\n    },\r\n    // 获取公司树数据\r\n    getCompanyTreeData() {\r\n      this.companyTreeLoading = true;\r\n      getEnterpriseInfo()\r\n        .then((res) => {\r\n          const deptList = res.data;\r\n          this.companyTreeData = [];\r\n          deptList.forEach((item) => {\r\n            this.companyTreeData.push({\r\n              label: item.label,\r\n              id: item.id,\r\n              children: item.children,\r\n            });\r\n          });\r\n          this.companyTreeLoading = false;\r\n        })\r\n        .catch((err) => {\r\n          this.companyTreeLoading = false;\r\n          console.error(err);\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.font-12 {\r\n  font-size: 12px;\r\n}\r\n\r\n.circle {\r\n  width: 8px;\r\n  height: 8px;\r\n  display: inline-block;\r\n  border-radius: 50%;\r\n  margin-right: 2px;\r\n}\r\n\r\n.bg-orange {\r\n  background-color: #ffa500;\r\n}\r\n\r\n.bg-blue {\r\n  background-color: #007bff;\r\n}\r\n\r\n.bg-green {\r\n  background-color: #28a745;\r\n}\r\n\r\n/* 详情弹窗样式 */\r\n:deep(.detail-dialog) {\r\n  .el-dialog__header {\r\n    background-color: #f5f5f5;\r\n    border-bottom: 1px solid #e6e6e6;\r\n    padding: 15px 20px;\r\n  }\r\n\r\n  .el-dialog__title {\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n  }\r\n\r\n  .el-dialog__body {\r\n    padding: 0;\r\n    max-height: 60vh;\r\n    overflow-y: auto;\r\n    padding: 0px 20px !important;\r\n  }\r\n\r\n  ::v-deep(.el-dialog__body) {\r\n    padding: 0px 20px !important;\r\n  }\r\n}\r\n\r\n.detail-content {\r\n  padding: 0 20px;\r\n\r\n  .independent-section {\r\n    margin-bottom: 10px;\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .independent-title {\r\n    margin: 0 0 20px 0;\r\n    padding: 0;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #333;\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 20px;\r\n\r\n    &:after {\r\n      content: \"\";\r\n      flex: 1;\r\n      height: 2px;\r\n      border-top: 2px dashed #409eff;\r\n    }\r\n  }\r\n\r\n  // 左右两列布局\r\n  .record-columns {\r\n    display: flex;\r\n    gap: 40px;\r\n  }\r\n\r\n  .left-column,\r\n  .right-column {\r\n    flex: 1;\r\n  }\r\n\r\n  .field-row {\r\n    display: flex;\r\n    align-items: flex-start;\r\n    margin-bottom: 16px;\r\n\r\n    &.full-width {\r\n      width: 100%;\r\n    }\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    &.highlighted-field {\r\n      background-color: #ebecf0;\r\n      padding: 8px 0;\r\n      margin-bottom: 0;\r\n      margin-left: -20px;\r\n      margin-right: -20px;\r\n      padding-left: 20px;\r\n      padding-right: 20px;\r\n    }\r\n  }\r\n\r\n  // 非高亮字段与高亮字段之间的间距\r\n  .field-row:not(.highlighted-field) + .field-row.highlighted-field,\r\n  .field-row.highlighted-field + .field-row:not(.highlighted-field) {\r\n    margin-top: 16px;\r\n  }\r\n\r\n  .field-label {\r\n    min-width: 70px;\r\n    font-weight: 400;\r\n    color: #666;\r\n    margin-right: 10px;\r\n    white-space: nowrap;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n  }\r\n\r\n  .field-value {\r\n    color: #333;\r\n    word-break: break-all;\r\n    line-height: 1.5;\r\n    font-size: 14px;\r\n    flex: 1;\r\n\r\n    &.status-tag {\r\n      padding: 2px 8px;\r\n      border-radius: 3px;\r\n      font-size: 12px;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  // 整改记录部分样式\r\n  .rectification-info {\r\n    display: flex;\r\n    gap: 0;\r\n    margin-bottom: 16px;\r\n    background-color: #ebecf0;\r\n    margin-left: -20px;\r\n    margin-right: -20px;\r\n    padding: 8px 20px;\r\n\r\n    .field-row {\r\n      margin-bottom: 0;\r\n      white-space: nowrap;\r\n      flex: 1;\r\n      margin-left: 0;\r\n      margin-right: 0;\r\n      padding: 0 20px;\r\n      background-color: transparent;\r\n\r\n      &:first-child {\r\n        padding-left: 0;\r\n      }\r\n\r\n      &:last-child {\r\n        padding-right: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 状态标签颜色\r\n  .status-no-need {\r\n    background-color: #f0f9ff;\r\n    color: #0369a1;\r\n    border: 1px solid #7dd3fc;\r\n  }\r\n\r\n  .status-pending {\r\n    background-color: transparent;\r\n    color: #d97706;\r\n    border: none;\r\n  }\r\n\r\n  .status-rectified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-qualified {\r\n    background-color: #dcfce7;\r\n    color: #16a34a;\r\n    border: 1px solid #4ade80;\r\n  }\r\n\r\n  .status-unqualified {\r\n    background-color: #fee2e2;\r\n    color: #dc2626;\r\n    border: 1px solid #f87171;\r\n  }\r\n\r\n  .status-verify {\r\n    background-color: #ede9fe;\r\n    color: #7c3aed;\r\n    border: 1px solid #a78bfa;\r\n  }\r\n\r\n  .status-unknown {\r\n    background-color: #f3f4f6;\r\n    color: #6b7280;\r\n    border: 1px solid #d1d5db;\r\n  }\r\n\r\n  // 状态行样式\r\n  .status-row {\r\n    margin-top: 8px;\r\n\r\n    label {\r\n      margin-right: 15px;\r\n      color: #666;\r\n      font-size: 14px;\r\n\r\n      input[type=\"checkbox\"] {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 照片相关样式\r\n  .photo-section {\r\n    padding-top: 10px;\r\n    border-top: 1px solid #f0f0f0;\r\n  }\r\n\r\n  .photo-container {\r\n    margin-top: 10px;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n  }\r\n\r\n  .photo-item,\r\n  .photo-placeholder {\r\n    border: 1px solid #e6e6e6;\r\n    border-radius: 6px;\r\n    overflow: hidden;\r\n    cursor: pointer;\r\n    transition: all 0.2s;\r\n    background: #fafafa;\r\n\r\n    &:hover {\r\n      transform: scale(1.02);\r\n      border-color: #409eff;\r\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\r\n    }\r\n\r\n    img {\r\n      width: 120px;\r\n      height: 90px;\r\n      object-fit: cover;\r\n      display: block;\r\n    }\r\n  }\r\n\r\n  .photo-placeholder {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #f8f9fa;\r\n    border: 1px dashed #d1d5db;\r\n\r\n    &:hover {\r\n      transform: none;\r\n      border-color: #d1d5db;\r\n      box-shadow: none;\r\n    }\r\n\r\n    img {\r\n      opacity: 0.3;\r\n      background: transparent;\r\n    }\r\n  }\r\n\r\n  .no-photo {\r\n    color: #999;\r\n    font-style: italic;\r\n    font-size: 12px;\r\n    margin-top: 8px;\r\n  }\r\n\r\n  // 响应式设计\r\n  @media (max-width: 768px) {\r\n    .record-columns {\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    .rectification-info {\r\n      flex-direction: column;\r\n      gap: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n// 图片预览弹窗样式\r\n:deep(.image-preview-dialog) {\r\n  .el-message-box__content {\r\n    text-align: center;\r\n  }\r\n\r\n  .el-message-box__message {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// 公司筛选下拉框样式\r\n.tree-select-wrapper {\r\n  padding: 8px;\r\n  min-height: 200px;\r\n  max-height: 300px;\r\n  overflow: auto;\r\n}\r\n\r\n:deep(.org-tree-select-dropdown) {\r\n  .el-select-dropdown__item {\r\n    height: auto;\r\n    max-height: 300px;\r\n    padding: 0;\r\n    overflow: hidden;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .el-tree-node__content {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .el-tree-node__label {\r\n    font-size: 14px;\r\n  }\r\n}\r\n</style>\r\n"]}]}