<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="70px">
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable style="width: 300px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="超危工程" prop="dangerousProjectName">
        <el-input v-model="queryParams.dangerousProjectName" placeholder="请输入超危工程名称" clearable style="width: 300px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['inspection:zjExtremelyDangerousProject:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-hasPermi="['inspection:zjExtremelyDangerousProject:edit']" type="success" plain icon="el-icon-edit"
          size="mini" :disabled="single || hasReviewedItem" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['inspection:zjExtremelyDangerousProject:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:zjExtremelyDangerousProject:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="zjExtremelyDangerousProjectList" height="calc(100vh - 250px)"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分子公司" align="center" prop="subsidiaryCompanies" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="250" show-overflow-tooltip>
        <template slot-scope="{ row }">{{ row.projectName }}</template>
      </el-table-column>
      <el-table-column label="超危工程" align="center" prop="dangerousProjectName" min-width="200" show-overflow-tooltip />
      <el-table-column label="施工开始时间" align="center" prop="constructionStartTime" width="160">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.constructionStartTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="施工结束时间" align="center" prop="constructionEndTime" width="160">
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.constructionEndTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="方案附件" align="center" prop="schemeAttachment" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-button v-if="scope.row.schemeAttachment" size="mini" type="text"
            @click="handleViewAttachment(scope.row.schemeAttachment)">查看</el-button>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="reviewStatus" width="120">
        <template slot-scope="scope">
          <el-tag :type="getReviewStatusType(scope.row.reviewStatus)" size="mini">
            {{ getReviewStatusText(scope.row.reviewStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button v-hasPermi="['inspection:zjExtremelyDangerousProject:edit']" size="mini" type="text" icon="el-icon-edit"
            :disabled="scope.row.reviewStatus === '1'" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button v-hasPermi="['inspection:zjExtremelyDangerousProject:remove']" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)">删除</el-button>
          <el-button v-hasPermi="['inspection:zjExtremelyDangerousProject:edit']" size="mini" type="text" icon="el-icon-check"
            :disabled="scope.row.reviewStatus !== '0'" @click="handleReview(scope.row)">审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改超危大工程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="650px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="分子公司" prop="subsidiaryCompanies">
          <el-input v-model="form.subsidiaryCompanies" placeholder="请输入分子公司" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="超危工程" prop="dangerousProjectName">
          <el-input v-model="form.dangerousProjectName" placeholder="请输入超危工程名称" style="width: 100%" clearable />
        </el-form-item>
        <el-form-item label="施工开始时间" prop="constructionStartTime">
          <el-date-picker v-model="form.constructionStartTime" style="width: 100%" clearable type="date"
            value-format="yyyy-MM-dd" placeholder="请选择施工开始时间" />
        </el-form-item>
        <el-form-item label="施工结束时间" prop="constructionEndTime">
          <el-date-picker v-model="form.constructionEndTime" style="width: 100%" clearable type="date"
            value-format="yyyy-MM-dd" placeholder="请选择施工结束时间" />
        </el-form-item>
        <el-form-item label="超危工程方案附件" prop="schemeAttachment">
          <file-upload v-model="form.schemeAttachment" :file-type="fileType" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 审核对话框 -->
    <el-dialog :title="reviewTitle" :visible.sync="reviewOpen" width="400px" append-to-body>
      <el-form ref="reviewForm" :model="reviewForm" :rules="reviewRules" label-width="80px">
        <el-form-item label="审核状态" prop="reviewStatus">
          <el-select v-model="reviewForm.reviewStatus" placeholder="请选择审核状态" style="width: 100%">
            <el-option label="审核通过" value="1" />
            <el-option label="审核不通过" value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReview">确 定</el-button>
        <el-button @click="cancelReview">取 消</el-button>
      </div>
    </el-dialog>
    <AttachmentDialog v-model="attachmentDialogVisible" :attachment-list="attachmentList" />
  </div>
</template>

<script>
import {
  listZjExtremelyDangerousProject,
  getZjExtremelyDangerousProject,
  delZjExtremelyDangerousProject,
  addZjExtremelyDangerousProject,
  updateZjExtremelyDangerousProject
} from '@/api/inspection/zjExtremelyDangerousProject'
import AttachmentDialog from '@/views/components/attchmentDialog.vue'

export default {
  name: 'ZjExtremelyDangerousProject',
  components: {
    AttachmentDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中记录包含审核通过的项目
      hasReviewedItem: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 超危大工程表格数据
      zjExtremelyDangerousProjectList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        subsidiaryCompanies: null,
        projectName: null,
        dangerousProjectName: null,
        projectType: null,
        constructionStartTime: null,
        constructionEndTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subsidiaryCompanies: [
          { required: true, message: '请输入分子公司', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        dangerousProjectName: [
          { required: true, message: '请输入超危工程名称', trigger: 'blur' }
        ]
      },
      // 附件相关
      attachmentDialogVisible: false,
      attachmentList: [],
      fileType: ['png', 'jpg', 'jpeg', 'doc', 'docx', 'xls', 'pdf'],
      // 审核相关
      reviewOpen: false,
      reviewTitle: '',
      reviewForm: {},
      reviewRules: {
        reviewStatus: [
          { required: true, message: '请选择审核状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询超危大工程列表 */
    getList() {
      this.loading = true;
      listZjExtremelyDangerousProject(this.queryParams).then(response => {
        this.zjExtremelyDangerousProjectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        subsidiaryCompanies: null,
        projectName: null,
        dangerousProjectName: null,
        projectType: null,
        constructionStartTime: null,
        constructionEndTime: null,
        typeId: null,
        typeName: null,
        schemeAttachment: null
      }
      this.resetForm('form')
    },
    /** 查看附件 */
    handleViewAttachment(value) {
      this.attachmentDialogVisible = true
      this.attachmentList = value.split(',')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
      // 检查选中的记录是否包含审核通过的项目
      this.hasReviewedItem = selection.some(item => item.reviewStatus === '1')
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加超危工程";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getZjExtremelyDangerousProject(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '修改超危大工程'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateZjExtremelyDangerousProject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addZjExtremelyDangerousProject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除超危大工程编号为"' + ids + '"的数据项？').then(function() {
        return delZjExtremelyDangerousProject(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      // 如果有选中记录，导出选中的；否则导出全部
      if (this.ids.length > 0) {
        // 导出选中记录
        this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条记录？').then(() => {
          this.download(
            'inspection/zjExtremelyDangerousProject/export',
            {
              ids: this.ids.join(',')
            },
            `zjExtremelyDangerousProject_selected_${new Date().getTime()}.xlsx`
          )
        })
      } else {
        // 导出全部记录（根据查询条件，但不包含分页参数）
        const exportParams = { ...this.queryParams }
        // 移除分页参数，确保导出全部数据
        delete exportParams.pageNum
        delete exportParams.pageSize

        this.download(
          'inspection/zjExtremelyDangerousProject/export',
          exportParams,
          `zjExtremelyDangerousProject_${new Date().getTime()}.xlsx`
        )
      }
    },
    /** 获取审核状态文字 */
    getReviewStatusText(status) {
      const statusMap = {
        '0': '未审核',
        '1': '审核通过',
        '2': '审核不通过'
      }
      return statusMap[status] || '未审核'
    },
    /** 获取审核状态标签类型 */
    getReviewStatusType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'success',
        '2': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 审核按钮操作 */
    handleReview(row) {
      this.reviewForm = {
        id: row.id,
        reviewStatus: '1' // 默认选择审核通过
      }
      this.reviewOpen = true
      this.reviewTitle = '审核超危大工程'
    },
    /** 提交审核 */
    submitReview() {
      this.$refs['reviewForm'].validate((valid) => {
        if (valid) {
          // 调用修改接口更新审核状态
          updateZjExtremelyDangerousProject(this.reviewForm).then((response) => {
            this.$modal.msgSuccess('审核成功')
            this.reviewOpen = false
            this.getList()
          })
        }
      })
    },
    /** 取消审核 */
    cancelReview() {
      this.reviewOpen = false
      this.reviewForm = {}
    }
  }
}
</script>
