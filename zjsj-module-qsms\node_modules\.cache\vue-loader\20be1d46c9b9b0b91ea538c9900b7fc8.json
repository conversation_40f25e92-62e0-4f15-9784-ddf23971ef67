{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue", "mtime": 1757425926929}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectHazardCategoryTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "selectHazardCategoryTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-select\n    ref=\"hazardSelectRef\"\n    :value=\"displayValue\"\n    :placeholder=\"placeholder\"\n    :disabled=\"disabled\"\n    clearable\n    filterable\n    style=\"width: 100%\"\n    class=\"hazard-category-select\"\n    popper-class=\"hazard-category-dropdown\"\n    @input=\"handleInput\"\n    @change=\"handleSelectChange\"\n    @visible-change=\"handleVisibleChange\"\n    @clear=\"handleClear\"\n  >\n    <el-option :value=\"displayValue\" style=\"height: auto; padding: 0\">\n      <el-tree\n        ref=\"hazardTree\"\n        :data=\"treeData\"\n        :props=\"treeProps\"\n        :expand-on-click-node=\"false\"\n        node-key=\"id\"\n        highlight-current\n        style=\"padding: 5px 0\"\n        @node-click=\"handleNodeClick\"\n      >\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\n          <span class=\"tree-label\">{{ node.label }}</span>\n        </span>\n      </el-tree>\n    </el-option>\n  </el-select>\n</template>\n\n<script>\nexport default {\n  name: 'SelectHazardCategoryTree',\n  props: {\n    value: {\n      type: [String, Number],\n      default: '',\n    },\n    placeholder: {\n      type: String,\n      default: '请选择隐患类别',\n    },\n    categoryList: {\n      type: Array,\n      default: () => [],\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    return {\n      displayValue: '',\n      treeProps: {\n        label: 'label',\n        children: 'children',\n      },\n    }\n  },\n\n  computed: {\n    treeData() {\n      return this.processTreeData(this.categoryList)\n    },\n  },\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        console.log('value 变化:', newVal)\n        this.displayValue = newVal || ''\n        // 当值改变时，延迟设置树的选中状态\n        setTimeout(() => {\n          this.setTreeSelection()\n        }, 100)\n      },\n    },\n    categoryList: {\n      immediate: true,\n      handler(newData) {\n        console.log('categoryList 变化:', newData)\n        // 当分类列表变化时，延迟设置树的选中状态\n        if (newData && newData.length > 0 && this.displayValue) {\n          setTimeout(() => {\n            this.setTreeSelection()\n          }, 200)\n        }\n      },\n    },\n  },\n\n  mounted() {\n    // 组件挂载后初始化设置\n    this.$nextTick(() => {\n      this.setTreeSelection()\n    })\n  },\n\n  methods: {\n    handleVisibleChange(isVisible) {\n      if (isVisible) {\n        // 下拉框打开时，设置当前选中的节点\n        this.$nextTick(() => {\n          if (this.value && this.$refs.hazardTree) {\n            const selectedNode = this.findNodeById(this.treeData, this.value)\n            if (selectedNode) {\n              this.$refs.hazardTree.setCurrentKey(this.value)\n              this.expandToNode(selectedNode)\n            }\n          }\n        })\n      }\n    },\n\n    handleNodeClick(data, node) {\n      console.log('点击隐患类别节点', data, node)\n      \n      // 如果有子节点，切换展开状态\n      if (data.children && data.children.length > 0) {\n        node.expanded = !node.expanded\n        return\n      }\n      \n      // 叶子节点，触发选择\n      this.selectNode(data)\n    },\n\n    selectNode(data) {\n      this.displayValue = data.label\n      this.$emit('input', data.label)\n      \n      // 触发 change 事件，传递完整的节点信息\n      this.$emit('change', {\n        id: data.id,\n        label: data.label,\n        value: data.label,\n      })\n      \n      // 更新树的高亮选择\n      this.$refs.hazardTree.setCurrentKey(data.id)\n      \n      // 关闭下拉框\n      this.$nextTick(() => {\n        this.closeDropdown()\n      })\n    },\n\n    handleInput(value) {\n      this.displayValue = value\n      this.$emit('input', value)\n    },\n\n    handleSelectChange(value) {\n      if (!value) {\n        this.$emit('change', null)\n      }\n    },\n\n    handleClear() {\n      this.displayValue = ''\n      this.$emit('input', '')\n      this.$emit('change', null)\n    },\n\n    closeDropdown() {\n      this.$refs.hazardSelectRef.blur()\n    },\n\n    // 处理树形数据\n    processTreeData(data) {\n      if (!data || !Array.isArray(data)) return []\n      \n      return data.map((item) => ({\n        ...item,\n        id: item.id || item.hazardId,\n        label: item.label || item.hazardName || item.name,\n        children: item.children ? this.processTreeData(item.children) : [],\n      }))\n    },\n\n    // 根据ID查找节点\n    findNodeById(nodes, id) {\n      for (const node of nodes) {\n        if (node.id === id) return node\n        if (node.children && node.children.length) {\n          const found = this.findNodeById(node.children, id)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 展开到指定节点\n    expandToNode(targetNode) {\n      if (!targetNode || !this.$refs.hazardTree) return\n      \n      // 递归展开父节点\n      const expandParents = (node) => {\n        const parent = this.findParentNode(this.treeData, node.id)\n        if (parent) {\n          this.$refs.hazardTree.setExpandedKey(parent.id, true)\n          expandParents(parent)\n        }\n      }\n      \n      expandParents(targetNode)\n    },\n\n    // 查找父节点\n    findParentNode(nodes, childId, parent = null) {\n      for (const node of nodes) {\n        if (node.id === childId) {\n          return parent\n        }\n        if (node.children && node.children.length) {\n          const found = this.findParentNode(node.children, childId, node)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 根据标签路径查找节点（用于回显）\n    findNodeByLabelPath(labelPath) {\n      console.log('查找节点路径:', labelPath)\n\n      if (!labelPath || !this.treeData.length) {\n        console.log('路径为空或树数据为空')\n        return null\n      }\n\n      // 分割路径，例如 \"安全管理-安全生产责任制\" -> [\"安全管理\", \"安全生产责任制\"]\n      const pathParts = labelPath.split('-')\n      console.log('路径分割结果:', pathParts)\n\n      let currentNodes = this.treeData\n      let targetNode = null\n\n      for (let i = 0; i < pathParts.length; i++) {\n        const part = pathParts[i].trim()\n        console.log(`查找第${i+1}级: \"${part}\"`)\n        console.log('当前可选节点:', currentNodes.map(n => n.label))\n\n        targetNode = currentNodes.find(node => node.label === part)\n\n        if (!targetNode) {\n          console.log(`未找到匹配的节点: \"${part}\"`)\n          return null\n        }\n\n        console.log(`找到节点: \"${part}\"`, targetNode)\n\n        if (i < pathParts.length - 1) {\n          // 不是最后一级，继续查找子节点\n          currentNodes = targetNode.children || []\n          console.log(`进入下一级，子节点数量: ${currentNodes.length}`)\n        }\n      }\n\n      console.log('最终找到的目标节点:', targetNode)\n      return targetNode\n    },\n\n    // 设置树形选择回显\n    setTreeSelection() {\n      console.log('=== setTreeSelection 开始 ===')\n      console.log('displayValue:', this.displayValue)\n      console.log('treeData 长度:', this.treeData?.length)\n      console.log('hazardTree ref:', this.$refs.hazardTree)\n\n      if (!this.displayValue) {\n        console.log('displayValue 为空，退出')\n        return\n      }\n\n      if (!this.$refs.hazardTree) {\n        console.log('hazardTree ref 不存在，退出')\n        return\n      }\n\n      if (!this.treeData || this.treeData.length === 0) {\n        console.log('treeData 为空，退出')\n        return\n      }\n\n      this.$nextTick(() => {\n        const targetNode = this.findNodeByLabelPath(this.displayValue)\n\n        if (targetNode) {\n          console.log('找到目标节点:', targetNode)\n\n          // 设置当前选中的节点\n          this.$refs.hazardTree.setCurrentKey(targetNode.id)\n\n          // 展开到目标节点\n          this.expandToNode(targetNode)\n\n          console.log('回显设置成功')\n        } else {\n          console.warn('未找到匹配的节点:', this.displayValue)\n          console.log('树数据结构:', JSON.stringify(this.treeData, null, 2))\n        }\n      })\n\n      console.log('=== setTreeSelection 结束 ===')\n    },\n\n    // 设置树的选中状态\n    setTreeSelection() {\n      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {\n        return\n      }\n\n      // 根据displayValue查找对应的节点\n      const selectedNode = this.findNodeByLabel(this.treeData, this.value)\n      if (selectedNode) {\n        this.$refs.hazardTree.setCurrentKey(selectedNode.id)\n        this.expandToNode(selectedNode)\n      }\n    },\n\n    // 根据标签查找节点\n    findNodeByLabel(nodes, label) {\n      for (const node of nodes) {\n        if (node.label === label) {\n          return node\n        }\n        if (node.children && node.children.length) {\n          const found = this.findNodeByLabel(node.children, label)\n          if (found) return found\n        }\n      }\n      return null\n    },\n  },\n}\n</script>\n\n<style scoped>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.tree-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n::v-deep .hazard-category-dropdown {\n  max-height: 400px;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content {\n  height: auto;\n  padding: 4px 0;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content:hover {\n  background-color: #f5f7fa;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #f0f7ff;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"]}]}