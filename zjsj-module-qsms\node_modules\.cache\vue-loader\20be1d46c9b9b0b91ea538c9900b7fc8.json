{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectHazardCategoryTree.vue", "mtime": 1757425570923}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectHazardCategoryTree.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "selectHazardCategoryTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-select\n    ref=\"hazardSelectRef\"\n    :value=\"displayValue\"\n    :placeholder=\"placeholder\"\n    :disabled=\"disabled\"\n    clearable\n    filterable\n    style=\"width: 100%\"\n    class=\"hazard-category-select\"\n    popper-class=\"hazard-category-dropdown\"\n    @input=\"handleInput\"\n    @change=\"handleSelectChange\"\n    @visible-change=\"handleVisibleChange\"\n    @clear=\"handleClear\"\n  >\n    <el-option :value=\"displayValue\" style=\"height: auto; padding: 0\">\n      <el-tree\n        ref=\"hazardTree\"\n        :data=\"treeData\"\n        :props=\"treeProps\"\n        :expand-on-click-node=\"false\"\n        node-key=\"id\"\n        highlight-current\n        style=\"padding: 5px 0\"\n        @node-click=\"handleNodeClick\"\n      >\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\n          <span class=\"tree-label\">{{ node.label }}</span>\n        </span>\n      </el-tree>\n    </el-option>\n  </el-select>\n</template>\n\n<script>\nexport default {\n  name: 'SelectHazardCategoryTree',\n  props: {\n    value: {\n      type: [String, Number],\n      default: '',\n    },\n    placeholder: {\n      type: String,\n      default: '请选择隐患类别',\n    },\n    categoryList: {\n      type: Array,\n      default: () => [],\n    },\n    disabled: {\n      type: Boolean,\n      default: false,\n    },\n  },\n\n  data() {\n    return {\n      displayValue: '',\n      treeProps: {\n        label: 'label',\n        children: 'children',\n      },\n    }\n  },\n\n  computed: {\n    treeData() {\n      return this.processTreeData(this.categoryList)\n    },\n  },\n\n  watch: {\n    value: {\n      immediate: true,\n      handler(newVal) {\n        this.displayValue = newVal || ''\n        // 当值改变时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n    categoryList: {\n      immediate: true,\n      handler() {\n        // 当分类列表变化时，重新设置树的选中状态\n        this.$nextTick(() => {\n          this.setTreeSelection()\n        })\n      },\n    },\n  },\n\n  mounted() {\n    // 组件挂载后初始化设置\n    this.$nextTick(() => {\n      this.setTreeSelection()\n    })\n  },\n\n  methods: {\n    handleVisibleChange(isVisible) {\n      if (isVisible) {\n        // 下拉框打开时，设置当前选中的节点\n        this.$nextTick(() => {\n          if (this.value && this.$refs.hazardTree) {\n            const selectedNode = this.findNodeById(this.treeData, this.value)\n            if (selectedNode) {\n              this.$refs.hazardTree.setCurrentKey(this.value)\n              this.expandToNode(selectedNode)\n            }\n          }\n        })\n      }\n    },\n\n    handleNodeClick(data, node) {\n      console.log('点击隐患类别节点', data, node)\n      \n      // 如果有子节点，切换展开状态\n      if (data.children && data.children.length > 0) {\n        node.expanded = !node.expanded\n        return\n      }\n      \n      // 叶子节点，触发选择\n      this.selectNode(data)\n    },\n\n    selectNode(data) {\n      this.displayValue = data.label\n      this.$emit('input', data.label)\n      \n      // 触发 change 事件，传递完整的节点信息\n      this.$emit('change', {\n        id: data.id,\n        label: data.label,\n        value: data.label,\n      })\n      \n      // 更新树的高亮选择\n      this.$refs.hazardTree.setCurrentKey(data.id)\n      \n      // 关闭下拉框\n      this.$nextTick(() => {\n        this.closeDropdown()\n      })\n    },\n\n    handleInput(value) {\n      this.displayValue = value\n      this.$emit('input', value)\n    },\n\n    handleSelectChange(value) {\n      if (!value) {\n        this.$emit('change', null)\n      }\n    },\n\n    handleClear() {\n      this.displayValue = ''\n      this.$emit('input', '')\n      this.$emit('change', null)\n    },\n\n    closeDropdown() {\n      this.$refs.hazardSelectRef.blur()\n    },\n\n    // 处理树形数据\n    processTreeData(data) {\n      if (!data || !Array.isArray(data)) return []\n      \n      return data.map((item) => ({\n        ...item,\n        id: item.id || item.hazardId,\n        label: item.label || item.hazardName || item.name,\n        children: item.children ? this.processTreeData(item.children) : [],\n      }))\n    },\n\n    // 根据ID查找节点\n    findNodeById(nodes, id) {\n      for (const node of nodes) {\n        if (node.id === id) return node\n        if (node.children && node.children.length) {\n          const found = this.findNodeById(node.children, id)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 展开到指定节点\n    expandToNode(targetNode) {\n      if (!targetNode || !this.$refs.hazardTree) return\n      \n      // 递归展开父节点\n      const expandParents = (node) => {\n        const parent = this.findParentNode(this.treeData, node.id)\n        if (parent) {\n          this.$refs.hazardTree.setExpandedKey(parent.id, true)\n          expandParents(parent)\n        }\n      }\n      \n      expandParents(targetNode)\n    },\n\n    // 查找父节点\n    findParentNode(nodes, childId, parent = null) {\n      for (const node of nodes) {\n        if (node.id === childId) {\n          return parent\n        }\n        if (node.children && node.children.length) {\n          const found = this.findParentNode(node.children, childId, node)\n          if (found) return found\n        }\n      }\n      return null\n    },\n\n    // 设置树的选中状态\n    setTreeSelection() {\n      if (!this.value || !this.$refs.hazardTree || !this.treeData.length) {\n        return\n      }\n\n      // 根据displayValue查找对应的节点\n      const selectedNode = this.findNodeByLabel(this.treeData, this.value)\n      if (selectedNode) {\n        this.$refs.hazardTree.setCurrentKey(selectedNode.id)\n        this.expandToNode(selectedNode)\n      }\n    },\n\n    // 根据标签查找节点\n    findNodeByLabel(nodes, label) {\n      for (const node of nodes) {\n        if (node.label === label) {\n          return node\n        }\n        if (node.children && node.children.length) {\n          const found = this.findNodeByLabel(node.children, label)\n          if (found) return found\n        }\n      }\n      return null\n    },\n  },\n}\n</script>\n\n<style scoped>\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.tree-label {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n::v-deep .hazard-category-dropdown {\n  max-height: 400px;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content {\n  height: auto;\n  padding: 4px 0;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node__content:hover {\n  background-color: #f5f7fa;\n}\n\n::v-deep .hazard-category-dropdown .el-tree-node.is-current > .el-tree-node__content {\n  background-color: #f0f7ff;\n  color: #409eff;\n  font-weight: bold;\n}\n</style>\n"]}]}