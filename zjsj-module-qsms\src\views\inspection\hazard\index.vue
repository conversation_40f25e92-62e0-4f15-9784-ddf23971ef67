<template>
  <div class="app-container">
    <el-row>
      <!-- 左侧树结构 -->
      <!-- <el-col :span="3">
        <div class="title mb-2" @click="goToHazardCategory">隐患类别管理</div>

        <el-tree
                  ref="tree"
          v-loading="treeLoading"
          :data="treeData"
          :props="defaultProps"
          :load="loadNode"
          lazy
          @node-click="handleNodeClick"
        >
          <template #default="{ node, data }">
            <el-tooltip effect="dark" :content="data.label" placement="top">
              <span
                :ref="(el) => setLabelRef(el, node)"
                class="el-tree-node__label"
              >
                {{ node.label }}
              </span>
            </el-tooltip>
          </template>
        </el-tree>
      </el-col> -->
      <el-col :span="24" style="margin-left: 10px">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          size="small"
          :inline="true"
          label-width="68px"
        >
          <!-- 排查内容 -->
          <el-form-item label="排查内容" prop="investigationContent">
            <el-input
              v-model="queryParams.investigationContent"
              placeholder="请输入排查内容"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="隐患类别" prop="hazardCategory">
            <el-input
              v-model="queryParams.hazardCategory"
              placeholder="请输入隐患类别"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:hazard:add']"
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-dropdown @command="handleCommand">
              <el-button
                v-hasPermi="['inspection:hazard:edit']"
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                >修改状态<i class="el-icon-arrow-down el-icon--right"
              /></el-button>
              <template #dropdown>
                <el-dropdown-menu style="width: 100px; text-align: center">
                  <!-- 下拉选项，可根据实际需求修改 -->
                  <el-dropdown-item command="enable">启用</el-dropdown-item>
                  <el-dropdown-item command="disable">禁用</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:hazard:remove']"
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['inspection:hazard:export']"
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >导出</el-button
            >
          </el-col>
          <right-toolbar :show-search.sync="showSearch" @queryTable="getList" />
        </el-row>

        <el-table
          v-loading="loading"
          :data="hazardList"
          height="calc(100vh - 250px)"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <!-- <el-table-column label="隐患明细 id" align="center" prop="hazardId" /> -->
          <!-- <el-table-column label="隐患名称" align="center" prop="hazardName" /> -->
          <!-- <el-table-column label="父菜单ID" align="center" prop="parentId" /> -->
          <!-- <el-table-column label="隐患编号" align="center" prop="hazardCode" /> -->
          <!-- <el-table-column label="显示顺序" align="center" prop="orderNum" /> -->
          <!-- <el-table-column label=" 打开方式" align="center" prop="target" /> -->
          <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
          <!-- <el-table-column label="检查类型" align="center" prop="examid" /> -->
          <!-- <el-table-column
        label="大类类型：1：类型  2：行为"
        align="center"
        prop="categoryType"
      /> -->
          <!-- 序号 -->
          <el-table-column label="序号" align="center" width="60">
            <template slot-scope="scope">
              {{
                (queryParams.pageNum - 1) * queryParams.pageSize +
                scope.$index +
                1
              }}
            </template>
          </el-table-column>
          <!-- 隐患类别 -->
          <el-table-column
            label="隐患类别"
            align="center"
            prop="hazardCategory"
            width="150"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            label="排查内容"
            align="center"
            prop="investigationContent"
            width="250"
            show-overflow-tooltip
          />
          <!-- <el-table-column label="隐患级别" align="center" prop="hazardLevel" /> -->
          <el-table-column
            label="整改要求"
            align="center"
            prop="rectificationRequirements"
            width="300"
            show-overflow-tooltip
          />
          <el-table-column
            label="整改时限(天)"
            align="center"
            prop="rectificationDeadline"
            width="100"
          />
          <!-- 事故隐患 -->
          <el-table-column
            label="事故隐患"
            align="center"
            prop="accidentHazard"
          >
            <template slot-scope="scope">
              <el-tag
                v-if="scope.row.accidentHazard === '1'"
                class="hazard-tag hazard-general"
                size="small"
              >
                一般
              </el-tag>
              <el-tag
                v-else-if="scope.row.accidentHazard === '2'"
                class="hazard-tag hazard-common"
                size="small"
              >
                常见
              </el-tag>
              <span v-else>{{ scope.row.accidentHazard }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="隐患标识"
            align="center"
            prop="hazardIdentification"
          />
          <el-table-column label="状态" align="center" prop="hazardStatus" />

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
            width="150"
          >
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['inspection:hazard:edit']"
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                >修改</el-button
              >
              <el-button
                v-hasPermi="['inspection:hazard:remove']"
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改隐患问题库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <!-- 隐患类别 -->
        <el-form-item label="隐患类别" prop="hazardCategory">
          <!-- <el-select
            ref="selectRef"
            v-model="form.hazardCategory"
            placeholder="请选择隐患类别"
            style="width: 100%"
            clearable
            :popper-append-to-body="false"
            @visible-change="handleSelectVisibleChange"
          >
            <el-option
              :value="form.hazardCategory"
              style="height: auto; padding: 0; border: none"
            >
              <el-tree
                :node-key="'id'"
                v-loading="treeLoading"
                :data="treeData"
                :props="defaultProps"
                :load="loadNode"
                lazy
                @node-click="handleNodeClick"
                :expand-on-click-node="false"
                :highlight-current="true"
              >
                <template #default="{ node, data }">
                  <el-tooltip
                    effect="dark"
                    :content="data.label"
                    placement="top"
                  >
                    <span class="el-tree-node__label">{{ node.label }}</span>
                  </el-tooltip>
                </template>
              </el-tree>
            </el-option>
          </el-select> -->
          <selectHazardCategoryTree
            ref="hazardCategorySelect"
            v-model="form.hazardCategory"
            :categoryList="treeData"
            placeholder="请选择隐患类别"
            @change="handleHazardCategoryChange"
          />
        </el-form-item>
        <el-form-item label="隐患级别" prop="hazardLevel">
          <el-select
            v-model="form.hazardLevel"
            placeholder="请选择隐患级别"
            style="width: 100%"
          >
            <el-option label="一级" value="1" />
            <el-option label="二级" value="2" />
            <el-option label="三级" value="3" />
            <el-option label="四级" value="4" />
          </el-select>
        </el-form-item>
        <!-- 事故隐患 -->
        <el-form-item label="事故隐患" prop="accidentHazard">
          <!-- 一般 常见 -->
          <el-select
            v-model="form.accidentHazard"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option label="一般" value="1" />
            <el-option label="常见" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="隐患标识" prop="hazardIdentification">
          <el-input
            v-model="form.hazardIdentification"
            placeholder="请输入隐患标识"
          />
        </el-form-item>
        <el-form-item label="整改时限(天)" prop="rectificationDeadline">
          <el-input
            v-model="form.rectificationDeadline"
            type="Number"
            placeholder="请输入整改时限(天)"
          />
        </el-form-item>
        <el-form-item label="状态" prop="hazardStatus">
          <el-select
            v-model="form.hazardStatus"
            placeholder="请选择状态"
            style="width: 100%"
          >
            <el-option label="启用" value="启用" />
            <el-option label="禁用" value="禁用" />
          </el-select>
        </el-form-item>
        <el-form-item label="排查内容" prop="investigationContent">
          <el-input
            v-model="form.investigationContent"
            :maxlength="255"
            rows="5"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="整改要求" prop="rectificationRequirements">
          <el-input
            v-model="form.rectificationRequirements"
            :maxlength="255"
            rows="5"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHazard,
  getHazard,
  delHazard,
  addHazard,
  updateHazard,
  treeHazard,
  treeHazardFirst,
  getHazardHazardCategory,
} from "@/api/inspection/hazard";
import selectHazardCategoryTree from "@/views/components/selectHazardCategoryTree.vue";

export default {
  name: "Hazard",
  components: {
    selectHazardCategoryTree,
  },
  data() {
    return {
      treeLoading: true,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 隐患问题库表格数据
      hazardList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        hazardName: null,
        hazardId: null,
        parentId: null,
        hazardCode: null,
        orderNum: null,
        target: null,
        examid: null,
        categoryType: null,
        investigationContent: null,
        hazardLevel: null,
        hazardStatus: null,
        rectificationDeadline: null,
        rectificationRequirements: null,
        hazardIdentification: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        hazardStatus: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
        investigationContent: [
          { required: true, message: "排查内容不能为空", trigger: "blur" },
        ],
        rectificationRequirements: [
          { required: true, message: "整改要求不能为空", trigger: "blur" },
        ],
      },
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
        isLeaf: "isLeaf",
      },
      selectId: null,
    };
  },
  created() {
    this.getHazardHazardCategory();
    this.getList();
  },
  methods: {
    handleHazardCategoryChange(selectedItem) {
      if (selectedItem) {
        console.log(selectedItem, "selectedItem");
        this.form.hazardCategory = selectedItem.label || selectedItem.value;
        this.form.parentId = selectedItem.id;
      } else {
        this.form.hazardCategory = null;
        this.form.parentId = null;
      }
    },

    // 设置隐患类别回显
    setHazardCategoryEcho() {
      console.log('开始设置隐患类别回显');
      console.log('form.hazardCategory:', this.form.hazardCategory);
      console.log('treeData:', this.treeData);

      if (!this.form.hazardCategory) {
        console.log('隐患类别值为空，跳过回显');
        return;
      }

      const trySetEcho = (retryCount = 0) => {
        console.log(`尝试设置回显，第${retryCount + 1}次`);

        this.$nextTick(() => {
          const component = this.$refs.hazardCategorySelect;
          console.log('组件引用:', component);

          if (component) {
            console.log('组件存在，开始设置回显');

            // 设置显示值
            component.displayValue = this.form.hazardCategory;
            console.log('设置 displayValue:', component.displayValue);

            // 检查树数据是否已加载
            if (this.treeData && this.treeData.length > 0) {
              console.log('树数据已加载，设置选中状态');

              // 等待树数据加载完成后设置选中状态
              setTimeout(() => {
                if (component.setTreeSelection) {
                  component.setTreeSelection();
                } else {
                  console.warn('setTreeSelection 方法不存在');
                }
              }, 200);
            } else {
              console.log('树数据未加载，等待数据加载');
              if (retryCount < 10) {
                setTimeout(() => {
                  trySetEcho(retryCount + 1);
                }, 300);
              }
            }

            console.log('隐患类别回显设置完成:', this.form.hazardCategory);
          } else if (retryCount < 10) {
            // 如果组件还没准备好，重试最多10次
            console.log('组件未准备好，等待重试');
            setTimeout(() => {
              trySetEcho(retryCount + 1);
            }, 300);
          } else {
            console.warn('隐患类别回显设置失败，组件未准备好');
          }
        });
      };

      trySetEcho();
    },
    expandToNode(node) {
      if (node && node.parent && !node.parent.isRoot) {
        this.expandToNode(node.parent);
        this.$refs.tree.setExpandedKey(node.parent.id, true);
      }
    },
    handleSelectVisibleChange(visible) {
      if (visible) {
        this.$nextTick(() => {
          if (this.form.hazardCategory) {
            // 确保节点已加载
            this.$refs.tree.setCurrentKey(this.form.hazardCategory, () => {
              const currentNode = this.$refs.tree.getCurrentNode();
              if (currentNode) {
                this.expandToNode(currentNode);
              }
            });
          }
        });
      }
    },

    handleCommand(command) {
      if (this.ids.length <= 0) {
        this.$message({
          message: "请选择隐患条目",
          type: "warning",
        });
        return;
      }
      // console.log(this.ids, "this.ids");
      const status = command === "enable" ? "正常" : "禁用";
      const promises = this.ids.map((item) => {
        return updateHazard({
          hazardId: item.hazardId,
          hazardStatus: status,
        });
      });
      Promise.allSettled(promises).then((res) => {
        const successCount = res.filter(
          (item) => item.status === "fulfilled"
        ).length;
        const failedResults = res.filter((item) => item.status === "rejected");
        if (successCount > 0) {
          this.$message({
            message: `${command === "enable" ? "启用" : "废弃"}成功`,
            type: "success",
          });
          this.handleQuery();
        } else {
          const errorMessages = failedResults
            .map((result, index) => {
              const id = this.ids[index].serialNumber;
              const errorMsg = `${command === "enable" ? "启用" : "废弃"}失败`;
              return `序号为 ${id} 的隐患条目：${errorMsg}`;
            })
            .join("\n");

          this.$message({
            message: `${errorMessages}`,
            type: "error",
            dangerouslyUseHTMLString: true,
          });
        }
      });
    },
    goToHazardCategory() {
      this.$router.push({
        path: "hazardCategory",
      });
    },
    getHazardHazardCategory() {
      this.treeLoading = true;
      getHazardHazardCategory().then((res) => {
        if (res.code === 200) {
          // console.log(res, "res");
          this.treeData = res.data;
          this.treeLoading = false;
          console.log(this.treeData, "this.treeData");
        }
      });
    },
    // loadNode(node, resolve) {
    //   if (node.level === 0) {
    //     return resolve([]);
    //   }

    //   const currentNode = node.data;
    //   // 若该节点已有 children，说明已请求过，直接解析现有 children
    //   if (currentNode.children) {
    //     return resolve(currentNode.children);
    //   }
    //   const parentId = currentNode.id;
    //   treeHazard({ parentId })
    //     .then((res) => {
    //       if (res.code === 200) {
    //         const children = res.rows.map((item) => ({
    //           id: item.hazardId,
    //           label: item.hazardName,
    //           // 根据子节点是否存在判断是否为叶子节点
    //           isLeaf: !item.children || item.children.length === 0,
    //         }));
    //         // 将子节点数据赋值给当前节点的 children 属性
    //         currentNode.children = children;
    //         resolve(children);
    //       } else {
    //         resolve([]);
    //       }
    //     })
    //     .catch(() => {
    //       resolve([]);
    //     });
    // },
    // transformChildren(children) {
    //   if (!children || children.length === 0) return [];
    //   return children.map((child) => ({
    //     label: child.hazardName,
    //     id: child.hazardId,
    //     children: this.transformChildren(child.children),
    //   }));
    // },
    // handleNodeClick(nodeData, node) {
    //   console.log(node, "nodeData");
    //   // this.queryParams.hazardId = nodeData.id;
    //   if (node.isLeaf) {
    //     this.selectId = nodeData.id;
    //     this.form.hazardCategory = nodeData.id; // 绑定ID而非label
    //     this.$refs.selectRef.blur();
    //   }
    // },
    // setLabelRef(el, node) {
    //   if (el) {
    //     this.labelRefs.set(node.id || node.label, el);
    //   }
    // },
    /** 查询隐患问题库列表 */
    getList() {
      this.loading = true;
      listHazard(this.queryParams).then((res) => {
        const data = res.rows;
        // data.sort((a, b) => {
        //   return new Date(b.createTime) - new Date(a.createTime);
        // });
        this.hazardList = data;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        hazardId: null,
        hazardName: null,
        parentId: null,
        hazardCode: null,
        orderNum: null,
        target: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        examid: null,
        categoryType: null,
        investigationContent: null,
        hazardLevel: null,
        hazardStatus: null,
        rectificationDeadline: null,
        rectificationRequirements: null,
        hazardIdentification: null,
        accidentHazard: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.parentId = this.selectId;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item, index) => {
        const serialNumber =
          (this.queryParams.pageNum - 1) * this.queryParams.pageSize +
          selection.indexOf(item) +
          1;
        return {
          serialNumber,
          hazardId: item.hazardId,
        };
      });
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加隐患问题库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const hazardId = row.hazardId || this.ids;
      getHazard(hazardId).then((res) => {
        this.form = res.data;
        this.open = true;
        this.title = "修改隐患问题库";

        console.log('获取到的表单数据:', this.form);
        console.log('隐患类别值:', this.form.hazardCategory);

        // 延迟设置，确保对话框和组件都已经渲染完成
        setTimeout(() => {
          this.setHazardCategoryEcho();
        }, 500);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.hazardId != null) {
            updateHazard(this.form).then((res) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // this.form.parentId = this.selectId;
            this.form.hazardStatus = "启用";
            // if (this.form.parentId == null) {
            //   this.$modal.msgError(
            //     "请先在左侧树形结构中选择具体的隐患类别，然后再新增隐患问题库"
            //   );
            //   return;
            // }
            addHazard(this.form).then((res) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      let hazardIds;
      if (row && row.hazardId) {
        // 单行删除
        hazardIds = [row.hazardId];
      } else {
        // 多行删除，从this.ids中提取hazardId
        hazardIds = this.ids.map((item) => item.hazardId);
      }

      this.$modal
        .confirm(
          '是否确认删除隐患问题库编号为"' + hazardIds.join(",") + '"的数据项？'
        )
        .then(function () {
          return delHazard(hazardIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "inspection/hazard/export",
        {
          ...this.queryParams,
        },
        `hazard_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
<style scoped>
.title {
  font-size: 16px;
  cursor: pointer;
}

::v-deep .el-tree-node__label {
  display: inline-block;
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

::v-deep .el-tree-node.is-current > .el-tree-node__content {
  background-color: #f0f7ff;
  color: #409eff;
  font-weight: bold;
}

/* 事故隐患标签样式 */
.hazard-tag {
  color: white !important;
  border: none !important;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 20px;
}

/* 一般隐患 - 黄色背景 */
.hazard-general {
  background-color: #f39c12 !important;
}

/* 常见隐患 - 红色背景 */
.hazard-common {
  background-color: #e74c3c !important;
}
</style>
