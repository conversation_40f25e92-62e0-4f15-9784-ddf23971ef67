{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\src\\views\\components\\selectPeopleTree.vue", "mtime": 1757423388005}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1757382154818}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1757382152797}, {"path": "C:\\Users\\<USER>\\Desktop\\zhian\\zjsj-module-qsms\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1757382155935}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["selectPeopleTree.vue"], "names": [], "mappings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file": "selectPeopleTree.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <el-select\r\n    ref=\"peopleSelectRef\"\r\n    :value=\"value\"\r\n    :placeholder=\"placeholder\"\r\n    :disabled=\"disabled\"\r\n    :visible=\"selectVisible\"\r\n    :filter-method=\"filterPeople\"\r\n    clearable\r\n    filterable\r\n    style=\"width: 100%\"\r\n    class=\"people-tree-select\"\r\n    popper-class=\"people-tree-dropdown\"\r\n    @input=\"handleInput\"\r\n    @change=\"handleSelectChange\"\r\n    @visible-change=\"handleVisibleChange\"\r\n    @focus=\"handleFocus\"\r\n    @clear=\"handleClear\"\r\n  >\r\n    <el-option :value=\"value\" style=\"height: auto; padding: 0\">\r\n      <el-tree\r\n        ref=\"peopleTree\"\r\n        :data=\"treeData\"\r\n        :props=\"treeProps\"\r\n        :filter-node-method=\"filterNode\"\r\n        :expand-on-click-node=\"false\"\r\n        :class=\"['people-tree', { searching: searchText && searchText.trim() }]\"\r\n        node-key=\"id\"\r\n        highlight-current\r\n        style=\"padding: 5px 0\"\r\n        @node-click=\"handleNodeClick\"\r\n      >\r\n        <span slot-scope=\"{ node, data }\" class=\"custom-tree-node\">\r\n          <i v-if=\"data.children && data.children.length\" />\r\n          <span\r\n            :class=\"['tree-label', { department: data.type === '1' }]\"\r\n            v-html=\"highlightSearchText(node.label, searchText)\"\r\n          />\r\n        </span>\r\n      </el-tree>\r\n    </el-option>\r\n  </el-select>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    value: {\r\n      type: [String, Number],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请选择\",\r\n    },\r\n    peopleList: {\r\n      type: Array,\r\n      default: () => [],\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false,\r\n    },\r\n    // 新增：选择模式，支持 'people'（人员选择）和 'category'（类别选择）\r\n    selectMode: {\r\n      type: String,\r\n      default: \"people\",\r\n      validator: (value) => [\"people\", \"category\"].includes(value),\r\n    },\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      searchText: \"\",\r\n      selectVisible: false,\r\n      searchTimer: null,\r\n      listenerBound: false, // 防止重复绑定监听器\r\n      inputHandler: null, // 存储输入处理函数引用\r\n      keyupHandler: null, // 存储按键处理函数引用\r\n      compositionHandler: null, // 存储组合输入处理函数引用\r\n\r\n      treeProps: {\r\n        label: \"label\",\r\n        children: \"children\",\r\n      },\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    // 转换为树形结构数据\r\n    treeData() {\r\n      return this.processTreeData(this.peopleList);\r\n    },\r\n  },\r\n\r\n  watch: {\r\n    searchText: {\r\n      immediate: true,\r\n      handler(val) {\r\n        // 使用 $nextTick 确保组件已完全渲染\r\n        this.$nextTick(() => {\r\n          if (this.$refs.peopleTree) {\r\n            this.$refs.peopleTree.filter(val);\r\n\r\n            // 搜索时自动展开匹配的父节点（无论是否首次）\r\n            if (val && val.trim()) {\r\n              // 使用短延迟确保过滤完成\r\n              setTimeout(() => {\r\n                this.expandMatchedNodes(val.trim());\r\n              }, 150);\r\n            } else {\r\n              // 清空搜索时收起所有节点\r\n              this.collapseAllNodes();\r\n            }\r\n          } else {\r\n            // 如果树组件还没准备好，短暂延迟后重试\r\n            setTimeout(() => {\r\n              if (this.$refs.peopleTree && val === this.searchText) {\r\n                this.$refs.peopleTree.filter(val);\r\n                if (val && val.trim()) {\r\n                  setTimeout(() => {\r\n                    this.expandMatchedNodes(val.trim());\r\n                  }, 100);\r\n                }\r\n              }\r\n            }, 200);\r\n          }\r\n        });\r\n      },\r\n    },\r\n\r\n    peopleList: {\r\n      handler(newVal) {\r\n        // 树数据更新后重新过滤\r\n        this.$nextTick(() => {\r\n          if (this.searchText) {\r\n            this.$refs.peopleTree.filter(this.searchText);\r\n          }\r\n        });\r\n      },\r\n      deep: true,\r\n    },\r\n  },\r\n\r\n  mounted() {\r\n    // 组件挂载后的初始化\r\n    this.$nextTick(() => {\r\n      this.setupInputListener();\r\n    });\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 清理定时器\r\n    if (this.searchTimer) {\r\n      clearTimeout(this.searchTimer);\r\n    }\r\n\r\n    // 清理事件监听器\r\n    if (this.inputHandler || this.keyupHandler || this.compositionHandler) {\r\n      const selectEl = this.$refs.peopleSelectRef;\r\n      if (selectEl && selectEl.$el) {\r\n        const input = selectEl.$el.querySelector(\"input\");\r\n        if (input) {\r\n          input.removeEventListener(\"input\", this.inputHandler);\r\n          input.removeEventListener(\"keyup\", this.keyupHandler);\r\n          input.removeEventListener(\"compositionend\", this.compositionHandler);\r\n        }\r\n      }\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 设置输入监听器\r\n    setupInputListener() {\r\n      // 如果已经绑定过，跳过\r\n      if (this.listenerBound) {\r\n        return;\r\n      }\r\n\r\n      // 减少延迟，提高响应速度\r\n      setTimeout(() => {\r\n        const selectEl = this.$refs.peopleSelectRef;\r\n\r\n        if (selectEl) {\r\n          // 更详细的输入元素检测\r\n          let actualInput = null;\r\n\r\n          // 方式1: 通过选择器查找\r\n          const inputBySelector = selectEl.$el\r\n            ? selectEl.$el.querySelector(\"input\")\r\n            : null;\r\n          if (inputBySelector) {\r\n            actualInput = inputBySelector;\r\n          }\r\n\r\n          // 方式2: 通过 $refs.input\r\n          else if (selectEl.$refs && selectEl.$refs.input) {\r\n            if (selectEl.$refs.input.$el) {\r\n              if (selectEl.$refs.input.$el.tagName === \"INPUT\") {\r\n                actualInput = selectEl.$refs.input.$el;\r\n              } else {\r\n                const nestedInput =\r\n                  selectEl.$refs.input.$el.querySelector(\"input\");\r\n                if (nestedInput) {\r\n                  actualInput = nestedInput;\r\n                }\r\n              }\r\n            } else if (selectEl.$refs.input.tagName === \"INPUT\") {\r\n              actualInput = selectEl.$refs.input;\r\n            }\r\n          }\r\n\r\n          if (actualInput && actualInput.tagName === \"INPUT\") {\r\n            // 移除可能存在的旧监听器\r\n            actualInput.removeEventListener(\"input\", this.inputHandler);\r\n            actualInput.removeEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.removeEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            // 创建绑定的处理函数\r\n            this.inputHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.keyupHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            this.compositionHandler = (e) => {\r\n              this.filterPeople(e.target.value);\r\n            };\r\n\r\n            // 添加事件监听器\r\n            actualInput.addEventListener(\"input\", this.inputHandler);\r\n            actualInput.addEventListener(\"keyup\", this.keyupHandler);\r\n            actualInput.addEventListener(\r\n              \"compositionend\",\r\n              this.compositionHandler\r\n            );\r\n\r\n            this.listenerBound = true;\r\n          } else {\r\n            // 如果找不到输入元素，稍后重试\r\n            setTimeout(() => {\r\n              this.listenerBound = false;\r\n              this.setupInputListener();\r\n            }, 300);\r\n          }\r\n        } else {\r\n          setTimeout(() => {\r\n            this.setupInputListener();\r\n          }, 300);\r\n        }\r\n      }, 200); // 增加延迟确保DOM完全渲染\r\n    },\r\n\r\n    handleVisibleChange(isVisible) {\r\n      if (isVisible) {\r\n        // 下拉框打开时，强制重新设置监听器\r\n        this.listenerBound = false;\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n\r\n        // 如果有现有搜索文本，立即触发过滤和展开\r\n        if (this.searchText) {\r\n          this.$nextTick(() => {\r\n            this.filterPeople(this.searchText);\r\n          });\r\n        }\r\n      } else {\r\n        // 关闭下拉框时清空搜索\r\n        this.searchText = \"\";\r\n        if (this.$refs.peopleTree) {\r\n          this.$refs.peopleTree.filter(\"\");\r\n          this.collapseAllNodes();\r\n        }\r\n      }\r\n    },\r\n\r\n    handleFocus() {\r\n      // 聚焦时确保监听器已设置\r\n      if (!this.listenerBound) {\r\n        this.$nextTick(() => {\r\n          this.setupInputListener();\r\n        });\r\n      }\r\n    },\r\n\r\n    handleClear() {\r\n      this.searchText = \"\";\r\n      this.filterPeople(\"\");\r\n    },\r\n    closeDropdown() {\r\n      this.$refs.peopleSelectRef.blur();\r\n    },\r\n\r\n    // 处理树形数据\r\n    // processTreeData(data) {\r\n    //   return data.map((item) => ({\r\n    //     ...item,\r\n    //     label: item.label ? item.label.trim() : \"\",\r\n    //     children: item.children ? this.processTreeData(item.children) : [],\r\n    //   }));\r\n    // },\r\n    processTreeData(data, parent = null) {\r\n      return data.map((item) => {\r\n        // 构建当前节点，继承原有属性\r\n        const currentNode = {\r\n          ...item,\r\n          label: item.label ? item.label.trim() : \"\",\r\n          parentId: parent?.id ?? null,\r\n          parentName: parent?.label ?? null,\r\n          // 递归处理子节点，并将当前节点作为父节点传递\r\n          children: item.children\r\n            ? this.processTreeData(item.children, item)\r\n            : [],\r\n        };\r\n        // console.log(\"currentNode\", currentNode);\r\n        return currentNode;\r\n      });\r\n    },\r\n\r\n    // 节点过滤方法 - 增强的模糊搜索\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n\r\n      const searchValue = value.toLowerCase().trim();\r\n      const label = data.label ? data.label.toLowerCase() : \"\";\r\n      const name = data.name ? data.name.toLowerCase() : \"\";\r\n\r\n      // 1. 精确匹配\r\n      if (label.includes(searchValue) || name.includes(searchValue)) {\r\n        return true;\r\n      }\r\n\r\n      // 2. 拼音首字母匹配\r\n      if (\r\n        this.matchPinyinInitials(label, searchValue) ||\r\n        this.matchPinyinInitials(name, searchValue)\r\n      ) {\r\n        return true;\r\n      }\r\n\r\n      // 3. 分词匹配 - 支持空格分隔的多个关键词\r\n      const keywords = searchValue.split(/\\s+/).filter((k) => k.length > 0);\r\n      if (keywords.length > 1) {\r\n        return keywords.every(\r\n          (keyword) =>\r\n            label.includes(keyword) ||\r\n            name.includes(keyword) ||\r\n            this.matchPinyinInitials(label, keyword) ||\r\n            this.matchPinyinInitials(name, keyword)\r\n        );\r\n      }\r\n\r\n      return false;\r\n    },\r\n\r\n    // 节点点击事件\r\n    handleNodeClick(data, node) {\r\n      console.log(\"点击当前节点\", data, node);\r\n\r\n      // 类别选择模式：所有叶子节点都可以选择\r\n      if (this.selectMode === \"category\") {\r\n        // 如果有子节点，切换展开状态\r\n        if (data.children && data.children.length > 0) {\r\n          node.expanded = !node.expanded;\r\n          return;\r\n        }\r\n\r\n        // 叶子节点，触发选择\r\n        this.handleSelectChange(data.id);\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      // 如果是 general-project 类型，可以选择\r\n      if (data.type === \"general-project\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n        return;\r\n      }\r\n\r\n      // 如果是其他类型且有子节点，切换展开状态\r\n      if (data.children && data.children.length) {\r\n        node.expanded = !node.expanded;\r\n        if (data.type != null) {\r\n          return;\r\n        }\r\n        // return;\r\n      }\r\n\r\n      // 如果是人员节点（type !== '1' 且不是 general-project），触发选择\r\n      if (data.type !== \"1\") {\r\n        this.handleSelectChange(data.id);\r\n\r\n        // 更新树的高亮选择\r\n        this.$refs.peopleTree.setCurrentKey(data.id);\r\n        this.$nextTick(() => {\r\n          this.closeDropdown();\r\n        });\r\n      }\r\n\r\n      // 其他情况（没有子节点的节点）不做任何操作\r\n    },\r\n\r\n    handleInput(value) {\r\n      this.$emit(\"input\", value);\r\n      // 主动调用 filterPeople 确保搜索生效\r\n      this.filterPeople(value);\r\n    },\r\n\r\n    handleSelectChange(selectedId) {\r\n      if (!selectedId) {\r\n        this.$emit(\"change\", null);\r\n        return;\r\n      }\r\n\r\n      const selectedItem = this.findNodeById(this.treeData, selectedId);\r\n      console.log(\"选择的信息\", selectedItem);\r\n\r\n      // 类别选择模式：直接返回选中的项目\r\n      if (this.selectMode === \"category\") {\r\n        if (selectedItem) {\r\n          const cleanItem = {\r\n            id: selectedItem.id,\r\n            label: selectedItem.label || \"\",\r\n            name: selectedItem.name || \"\",\r\n            type: selectedItem.type,\r\n          };\r\n          this.$emit(\"change\", cleanItem);\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 人员选择模式：原有逻辑\r\n      if (selectedItem && selectedItem.type !== \"1\") {\r\n        const cleanItem = {\r\n          id: selectedItem.id,\r\n          label: selectedItem.label || \"\",\r\n          name: selectedItem.name || \"\",\r\n          type: selectedItem.type,\r\n        };\r\n\r\n        this.$emit(\"change\", cleanItem);\r\n      }\r\n    },\r\n\r\n    // 根据ID查找节点\r\n    findNodeById(nodes, id) {\r\n      for (const node of nodes) {\r\n        if (node.id === id) return node;\r\n        if (node.children && node.children.length) {\r\n          const found = this.findNodeById(node.children, id);\r\n          if (found) return found;\r\n        }\r\n      }\r\n      return null;\r\n    },\r\n\r\n    // 拼音首字母匹配方法\r\n    matchPinyinInitials(text, searchValue) {\r\n      if (!text || !searchValue) return false;\r\n\r\n      // 简单的中文拼音首字母映射\r\n      const pinyinMap = {\r\n        a: \"阿啊\",\r\n        b: \"不把白本部被北备比表必标\",\r\n        c: \"从程成产出常场长城创传\",\r\n        d: \"的大都到道地点电当得对多段\",\r\n        e: \"二\",\r\n        f: \"发方法分风服费发放防发\",\r\n        g: \"工个过公管国广高改工构\",\r\n        h: \"和好后会化回还海合黄行\",\r\n        i: \"一\",\r\n        j: \"就进建交机计经检基级见技结\",\r\n        k: \"可看科开口控课快客\",\r\n        l: \"了来理里立流料量联力领路类\",\r\n        m: \"没么面民明名目模美门\",\r\n        n: \"年能内南那女你农\",\r\n        o: \"哦\",\r\n        p: \"平配品排培破片跑\",\r\n        q: \"去其全清情期前起请求区\",\r\n        r: \"人如让日然认入任\",\r\n        s: \"是时实施所说三设生手市上四十\",\r\n        t: \"他通同体统头条特提图天\",\r\n        u: \"有用于\",\r\n        v: \"\",\r\n        w: \"我为文物位问外王万五网维\",\r\n        x: \"现系性新学小心选许信下项行西\",\r\n        y: \"一要用有以业已应意音元月研运\",\r\n        z: \"中在这者主专注资制知至重组\",\r\n      };\r\n\r\n      // 尝试拼音首字母匹配\r\n      for (let i = 0; i < searchValue.length; i++) {\r\n        const char = searchValue[i];\r\n        const pinyinChars = pinyinMap[char];\r\n        if (pinyinChars && i < text.length) {\r\n          if (!pinyinChars.includes(text[i])) {\r\n            return false;\r\n          }\r\n        } else if (char !== text[i]) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return searchValue.length > 0 && searchValue.length <= text.length;\r\n    },\r\n\r\n    // 即时搜索方法 - 移除防抖，确保立即响应\r\n    filterPeople(value) {\r\n      // 立即更新搜索文本并触发过滤\r\n      this.searchText = value;\r\n\r\n      // 立即触发树过滤\r\n      if (this.$refs.peopleTree) {\r\n        this.$refs.peopleTree.filter(value);\r\n\r\n        // 如果有搜索值，立即展开匹配的节点\r\n        if (value && value.trim()) {\r\n          // 使用 setTimeout 确保过滤完成后再展开\r\n          setTimeout(() => {\r\n            this.expandMatchedNodes(value.trim());\r\n          }, 50);\r\n        }\r\n      }\r\n\r\n      // 对于 filter-method，我们总是返回 true，让所有选项都显示\r\n      // 因为我们的过滤逻辑是在树组件内部处理的\r\n      return true;\r\n    },\r\n\r\n    // 增强expandMatchedNodes方法，确保节点正确展开\r\n    expandMatchedNodes(searchValue) {\r\n      if (!this.$refs.peopleTree || !searchValue) return;\r\n\r\n      const expandedKeys = [];\r\n      this.collectExpandedNodes(this.treeData, searchValue, expandedKeys);\r\n\r\n      // 去重并确保展开逻辑生效\r\n      const uniqueKeys = [...new Set(expandedKeys)];\r\n\r\n      // 立即展开节点，不使用 nextTick 延迟\r\n      uniqueKeys.forEach((key) => {\r\n        const node = this.$refs.peopleTree.store.nodesMap[key];\r\n        if (node && !node.expanded) {\r\n          node.expand();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 收起所有节点的方法\r\n    collapseAllNodes() {\r\n      if (!this.$refs.peopleTree) return;\r\n\r\n      const allNodes = this.$refs.peopleTree.store.nodesMap;\r\n      Object.values(allNodes).forEach((node) => {\r\n        if (node.expanded && node.childNodes && node.childNodes.length > 0) {\r\n          node.collapse();\r\n        }\r\n      });\r\n    },\r\n\r\n    // 递归收集需要展开的节点\r\n    collectExpandedNodes(nodes, searchValue, expandedKeys, parentKey = null) {\r\n      let hasMatchedChild = false;\r\n\r\n      for (const node of nodes) {\r\n        // 检查当前节点是否匹配\r\n        if (this.filterNode(searchValue, node)) {\r\n          hasMatchedChild = true;\r\n\r\n          // 如果有父节点，添加到展开列表\r\n          if (parentKey) {\r\n            expandedKeys.push(parentKey);\r\n          }\r\n        }\r\n\r\n        // 递归检查子节点\r\n        if (node.children && node.children.length > 0) {\r\n          const childMatched = this.collectExpandedNodes(\r\n            node.children,\r\n            searchValue,\r\n            expandedKeys,\r\n            node.id\r\n          );\r\n\r\n          if (childMatched) {\r\n            hasMatchedChild = true;\r\n            // 如果子节点有匹配，当前节点也需要展开\r\n            if (node.id) {\r\n              expandedKeys.push(node.id);\r\n            }\r\n            // 如果有父节点，父节点也需要展开\r\n            if (parentKey) {\r\n              expandedKeys.push(parentKey);\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      return hasMatchedChild;\r\n    },\r\n\r\n    // 高亮搜索文本\r\n    highlightSearchText(text, searchValue) {\r\n      if (!text) return \"\";\r\n      if (!searchValue || !searchValue.trim()) return text;\r\n\r\n      const searchText = searchValue.trim();\r\n      // 防止XSS攻击，转义HTML特殊字符\r\n      const escapedText = text.replace(/[&<>\"']/g, function (match) {\r\n        const escapeMap = {\r\n          \"&\": \"&amp;\",\r\n          \"<\": \"&lt;\",\r\n          \">\": \"&gt;\",\r\n          '\"': \"&quot;\",\r\n          \"'\": \"&#x27;\",\r\n        };\r\n        return escapeMap[match];\r\n      });\r\n\r\n      // 如果搜索文本包含在显示文本中，高亮显示\r\n      if (escapedText.toLowerCase().includes(searchText.toLowerCase())) {\r\n        const regex = new RegExp(\r\n          `(${searchText.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\")})`,\r\n          \"gi\"\r\n        );\r\n        return escapedText.replace(\r\n          regex,\r\n          '<span class=\"search-highlight\">$1</span>'\r\n        );\r\n      }\r\n\r\n      return escapedText;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.custom-tree-node {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.tree-icon {\r\n  margin-right: 6px;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.tree-label {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 调整树组件样式 */\r\n:deep(.people-tree) {\r\n  min-width: 180px;\r\n  width: auto;\r\n}\r\n\r\n/* 控制下拉框宽度 - 使用特定class确保只影响这个组件 */\r\n:deep(.people-tree-dropdown) {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n/* 全局样式 - 更高优先级 */\r\n::v-deep .people-tree-dropdown.el-select-dropdown {\r\n  max-width: 250px !important;\r\n  min-width: 180px !important;\r\n  width: auto !important;\r\n}\r\n\r\n:deep(.el-tree-node__content) {\r\n  height: 32px;\r\n}\r\n\r\n:deep(\r\n    .el-tree--highlight-current\r\n      .el-tree-node.is-current\r\n      > .el-tree-node__content\r\n  ) {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n:deep(.el-select-dropdown__item) {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n\r\n/* 搜索高亮样式 */\r\n:deep(.search-highlight) {\r\n  background-color: #fffacd;\r\n  color: #d32f2f;\r\n  font-weight: bold;\r\n  padding: 1px 2px;\r\n  border-radius: 2px;\r\n}\r\n</style>\r\n\r\n<style>\r\n/* 全局样式 - 不受scoped限制，确保能够覆盖Element UI的默认样式 */\r\n.people-tree-dropdown.el-select-dropdown {\r\n  max-width: 650px !important;\r\n  min-width: 650px !important;\r\n  width: auto !important;\r\n}\r\n\r\n.people-tree-dropdown .el-select-dropdown__item {\r\n  padding: 0;\r\n  height: auto;\r\n}\r\n</style>\r\n"]}]}